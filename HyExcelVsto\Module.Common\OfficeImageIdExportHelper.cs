using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Tools.Ribbon;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Office图标提取辅助类
    /// </summary>
    /// <remarks>
    /// 基于博客园文章方法实现：https://www.cnblogs.com/liweis/p/11762679.html
    /// 使用GetImageMso方法提取Office内置图标
    /// </remarks>
    public static class OfficeImageHelper
    {
        /// <summary>
        /// IPictureDisp转Bitmap
        /// </summary>
        /// <param name="ipd">IPictureDisp对象</param>
        /// <returns>转换后的Bitmap</returns>
        public static Bitmap ConvertPixelByPixel(stdole.IPictureDisp ipd)
        {
            if (ipd == null) return null;

            try
            {
                // 获取HBITMAP内部信息
                var dibsection = new DIBSECTION();
                GetObjectDIBSection((IntPtr)ipd.Handle, Marshal.SizeOf(dibsection), ref dibsection);
                var width = dibsection.dsBm.bmWidth;
                var height = dibsection.dsBm.bmHeight;

                // 创建目标Bitmap对象
                var bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);

                unsafe
                {
                    // 获取原始位指针
                    var pBits = (RGBQUAD*)(void*)dibsection.dsBm.bmBits;

                    // 逐像素复制
                    for (var x = 0; x < dibsection.dsBmih.biWidth; x++)
                        for (var y = 0; y < dibsection.dsBmih.biHeight; y++)
                        {
                            var offset = y * dibsection.dsBmih.biWidth + x;
                            if (pBits[offset].rgbReserved != 0)
                            {
                                bitmap.SetPixel(x, y, Color.FromArgb(
                                    pBits[offset].rgbReserved,
                                    pBits[offset].rgbRed,
                                    pBits[offset].rgbGreen,
                                    pBits[offset].rgbBlue));
                            }
                        }
                }
                return bitmap;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"IPictureDisp转Bitmap失败: {ex.Message}");
                return null;
            }
        }

        #region Win32 API 结构体定义

        [StructLayout(LayoutKind.Sequential)]
        private struct RGBQUAD
        {
            public byte rgbBlue;
            public byte rgbGreen;
            public byte rgbRed;
            public byte rgbReserved;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct BITMAP
        {
            public int bmType;
            public int bmWidth;
            public int bmHeight;
            public int bmWidthBytes;
            public short bmPlanes;
            public short bmBitsPixel;
            public IntPtr bmBits;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct BITMAPINFOHEADER
        {
            public int biSize;
            public int biWidth;
            public int biHeight;
            public short biPlanes;
            public short biBitCount;
            public int biCompression;
            public int biSizeImage;
            public int biXPelsPerMeter;
            public int biYPelsPerMeter;
            public int biClrUsed;
            public int bitClrImportant;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct DIBSECTION
        {
            public BITMAP dsBm;
            public BITMAPINFOHEADER dsBmih;
            public int dsBitField1;
            public int dsBitField2;
            public int dsBitField3;
            public IntPtr dshSection;
            public int dsOffset;
        }

        [DllImport("gdi32.dll", EntryPoint = "GetObject")]
        public static extern int GetObjectDIBSection(IntPtr hObject, int nCount, ref DIBSECTION lpObject);

        #endregion
    }

    /// <summary>
    /// Office图标ID导出助手类
    /// </summary>
    /// <remarks>
    /// 此类提供以下主要功能：
    /// 1. 导出所有可用的OfficeImageId到Excel文件
    /// 2. 生成图标预览和对应的ID名称
    /// 3. 提供图标搜索和筛选功能
    /// 4. 支持导出为多种格式（Excel、CSV、HTML）
    ///
    /// 使用场景：
    /// - 开发人员查找合适的Office图标
    /// - 生成图标参考文档
    /// - 批量替换或更新图标ID
    /// </remarks>
    public static class OfficeImageIdExportHelper
    {
        /// <summary>
        /// OfficeImageId缓存文件路径（可选）
        /// </summary>
        private static readonly string OfficeImageIdCachePath = ETConfig.GetConfigDirectory("OfficeImageId_Cache.txt", ".data");

        /// <summary>
        /// 缓存的OfficeImageId列表
        /// </summary>
        private static List<string> _cachedOfficeImageIds = null;

        /// <summary>
        /// 是否启用文件缓存
        /// </summary>
        private static readonly bool EnableFileCache = true;

        /// <summary>
        /// 默认输出目录
        /// </summary>
        private static readonly string DefaultOutputDirectory = @"E:\Temp\工作临时目录\2025-07-30-08-37";

        /// <summary>
        /// 获取所有OfficeImageId（基于自动枚举，支持文件缓存）
        /// </summary>
        /// <returns>OfficeImageId列表</returns>
        /// <remarks>
        /// 此方法会：
        /// 1. 首次调用时检查内存缓存
        /// 2. 如果启用文件缓存且缓存文件存在，则从缓存文件读取
        /// 3. 否则执行自动枚举发现所有可用的ImageId
        /// 4. 将枚举结果保存到文件缓存（如果启用）
        /// </remarks>
        private static List<string> GetOfficeImageIdsFromConfig()
        {
            // 如果已经缓存，直接返回
            if (_cachedOfficeImageIds != null)
            {
                return _cachedOfficeImageIds;
            }

            try
            {
                // 尝试从缓存文件读取（如果启用且存在）
                if (EnableFileCache && File.Exists(OfficeImageIdCachePath))
                {
                    _cachedOfficeImageIds = LoadImageIdsFromCacheFile();
                    if (_cachedOfficeImageIds.Count > 0)
                    {
                        ETLogManager.Info($"成功从缓存文件读取 {_cachedOfficeImageIds.Count} 个OfficeImageId");
                        return _cachedOfficeImageIds;
                    }
                }

                // 执行自动枚举
                ETLogManager.Info("开始自动枚举OfficeImageId...");
                _cachedOfficeImageIds = AutoEnumerateOfficeImageIds();
                ETLogManager.Info($"自动枚举完成，发现 {_cachedOfficeImageIds.Count} 个有效的OfficeImageId");

                // 保存到缓存文件（如果启用）
                if (EnableFileCache && _cachedOfficeImageIds.Count > 0)
                {
                    SaveImageIdsToCacheFile(_cachedOfficeImageIds);
                }

                return _cachedOfficeImageIds;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取OfficeImageId失败: {ex.Message}");
                _cachedOfficeImageIds = GetFallbackImageIds();
                return _cachedOfficeImageIds;
            }
        }

        /// <summary>
        /// 从缓存文件加载ImageId列表
        /// </summary>
        /// <returns>ImageId列表</returns>
        private static List<string> LoadImageIdsFromCacheFile()
        {
            try
            {
                if (!File.Exists(OfficeImageIdCachePath))
                {
                    return new List<string>();
                }

                var lines = File.ReadAllLines(OfficeImageIdCachePath, System.Text.Encoding.UTF8);

                return lines
                    .Where(line => !string.IsNullOrWhiteSpace(line))  // 过滤空行
                    .Select(line => line.Trim())                      // 去除首尾空格
                    .Where(line => !line.StartsWith("#"))             // 过滤注释行
                    .Where(line => !line.StartsWith("//"))            // 过滤注释行
                    .Distinct()                                       // 去重
                    .OrderBy(id => id)                               // 按字母排序
                    .ToList();
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"读取缓存文件失败: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 保存ImageId列表到缓存文件
        /// </summary>
        /// <param name="imageIds">要保存的ImageId列表</param>
        private static void SaveImageIdsToCacheFile(List<string> imageIds)
        {
            try
            {
                // 确保缓存目录存在
                var cacheDir = Path.GetDirectoryName(OfficeImageIdCachePath);
                if (!Directory.Exists(cacheDir))
                {
                    Directory.CreateDirectory(cacheDir);
                }

                // 创建缓存文件内容
                var content = new List<string>
                {
                    "# Office ImageId 自动枚举缓存文件",
                    "# 此文件由系统自动生成，用于提高后续加载速度",
                    $"# 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    $"# 总数量: {imageIds.Count} 个",
                    "#",
                    "# 注意：此文件可以安全删除，系统会重新自动枚举",
                    "#"
                };

                // 添加ImageId列表
                content.AddRange(imageIds);

                // 保存到缓存文件
                File.WriteAllLines(OfficeImageIdCachePath, content, System.Text.Encoding.UTF8);

                ETLogManager.Debug($"成功保存 {imageIds.Count} 个ImageId到缓存文件");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"保存缓存文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自动枚举所有可用的OfficeImageId
        /// </summary>
        /// <returns>有效的ImageId列表</returns>
        /// <remarks>
        /// 此方法通过尝试常见的命名模式和已知的ImageId来发现可用的图标。
        /// 基于Office Fluent UI的命名规律进行智能枚举。
        /// </remarks>
        private static List<string> AutoEnumerateOfficeImageIds()
        {
            var validImageIds = new List<string>();
            var xlApp = ThisAddIn.ExcelApplication;

            if (xlApp?.CommandBars == null)
            {
                ETLogManager.Warning("CommandBars不可用，无法进行自动枚举");
                return GetFallbackImageIds();
            }

            try
            {
                // 1. 测试已知的基础ImageId
                var knownImageIds = GetKnownImageIds();
                validImageIds.AddRange(TestImageIds(xlApp, knownImageIds));

                // 2. 基于命名模式生成候选ImageId
                var candidateImageIds = GenerateCandidateImageIds();
                validImageIds.AddRange(TestImageIds(xlApp, candidateImageIds));

                // 3. 去重并排序
                validImageIds = validImageIds.Distinct().OrderBy(id => id).ToList();

                ETLogManager.Info($"自动枚举发现 {validImageIds.Count} 个有效ImageId");
                return validImageIds;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"自动枚举过程中发生错误: {ex.Message}");
                return GetFallbackImageIds();
            }
        }

        /// <summary>
        /// 获取已知的基础ImageId列表
        /// </summary>
        /// <returns>已知ImageId列表</returns>
        private static List<string> GetKnownImageIds()
        {
            return new List<string>
            {
                // 文件操作
                "FileOpen", "FileSave", "FileSaveAs", "FileNew", "FileClose", "FilePrint", "FilePrintPreview",
                "FileExit", "FileProperties", "FileFind", "FileReplace",

                // 编辑操作
                "Copy", "Paste", "Cut", "Undo", "Redo", "Find", "Replace", "Clear", "Delete", "Refresh",
                "SelectAll", "GoTo", "Spelling", "Thesaurus",

                // 格式化
                "Font", "FontBold", "FontItalic", "FontUnderline", "FontColor", "FormatCells", "PageSetup",
                "SizeToFit", "AlignLeft", "AlignCenter", "AlignRight", "AlignJustify",

                // 插入
                "PictureInsert", "TableInsert", "SheetInsert", "Comment", "ConvertTextToTable",
                "HyperlinkInsert", "SymbolInsert", "DateAndTimeInsert",

                // 视图
                "ViewNormalViewExcel", "ViewPageBreakPreviewView", "ViewPrintLayoutView", "ViewFullScreenView",
                "WindowHide", "WindowSwitchWindowsMenuExcel", "Filter", "AutoSum",

                // 工具
                "Properties", "FieldSettings", "MoreCommands", "Help", "Lock", "Calculator", "Calendar",
                "Folder", "MacroPlay", "MacroRecord", "ProtectDocument", "DataValidation",

                // 图形和绘制
                "ShapeRectangle", "ShapeOval", "ShapeLine", "ShapeArrow", "PictureReflectionGallery",
                "ObjectsGroup", "ObjectsUngroup", "ObjectBringToFront", "ObjectSendToBack",

                // 数据透视表
                "PivotTableLayoutShowInOutlineForm", "PivotTableOptions", "PivotChartType",

                // 其他常用
                "DropCapOptionsDialog", "SlideMasterTextPlaceholderInsert", "GoToNewRecord",
                "Zoom100", "ZoomIn", "ZoomOut", "PrintPreview", "PageMargins"
            };
        }

        /// <summary>
        /// 生成候选ImageId列表
        /// </summary>
        /// <returns>候选ImageId列表</returns>
        private static List<string> GenerateCandidateImageIds()
        {
            var candidates = new List<string>();

            // 基于常见前缀生成候选ID
            var prefixes = new[] { "File", "Edit", "Format", "Insert", "View", "Table", "Chart", "Picture",
                                  "Shape", "Text", "Font", "Page", "Print", "Review", "Macro", "Window",
                                  "Slide", "Animation", "Pivot", "Query", "Database", "Mail", "Contact",
                                  "Calendar", "Task" };

            var suffixes = new[] { "Insert", "Delete", "Edit", "Format", "Options", "Dialog", "Menu",
                                  "Gallery", "Preview", "Show", "Hide", "Open", "Close", "Save", "New" };

            // 生成组合
            foreach (var prefix in prefixes)
            {
                foreach (var suffix in suffixes)
                {
                    candidates.Add(prefix + suffix);
                }
            }

            return candidates;
        }

        /// <summary>
        /// 测试ImageId列表的有效性
        /// </summary>
        /// <param name="xlApp">Excel应用程序实例</param>
        /// <param name="imageIds">要测试的ImageId列表</param>
        /// <returns>有效的ImageId列表</returns>
        private static List<string> TestImageIds(Microsoft.Office.Interop.Excel.Application xlApp, List<string> imageIds)
        {
            var validIds = new List<string>();
            int testCount = 0;
            int validCount = 0;

            foreach (var imageId in imageIds)
            {
                testCount++;
                try
                {
                    // 尝试获取图标，如果成功则说明ImageId有效
                    stdole.IPictureDisp pictureDisp = xlApp.CommandBars.GetImageMso(imageId, 16, 16);
                    if (pictureDisp != null)
                    {
                        validIds.Add(imageId);
                        validCount++;
                    }
                }
                catch
                {
                    // 忽略无效的ImageId
                }

                // 每测试100个记录一次进度
                if (testCount % 100 == 0)
                {
                    ETLogManager.Debug($"已测试 {testCount} 个ImageId，发现 {validCount} 个有效");
                }
            }

            ETLogManager.Info($"测试完成：{testCount} 个候选中发现 {validCount} 个有效ImageId");
            return validIds;
        }

        /// <summary>
        /// 获取备用ImageId列表（当自动枚举失败时使用）
        /// </summary>
        /// <returns>备用ImageId列表</returns>
        private static List<string> GetFallbackImageIds()
        {
            ETLogManager.Warning("使用备用ImageId列表");
            return GetKnownImageIds();
        }

        /// <summary>
        /// 获取OfficeImageId的中文描述
        /// </summary>
        /// <param name="imageId">图标ID</param>
        /// <returns>中文描述</returns>
        /// <remarks>基于图标ID的命名规律生成中文描述， 如果无法识别则返回默认描述</remarks>
        private static string GetImageIdDescription(string imageId)
        {
            // 基于命名规律的描述映射
            var descriptionMappings = new Dictionary<string, string>
            {
                // 文件操作
                { "FileOpen", "打开文件" },
                { "FileSave", "保存文件" },
                { "FileSaveAs", "另存为" },
                { "FileClose", "关闭文件" },
                { "FileNew", "新建文件" },
                { "FilePrint", "打印" },
                { "FilePrintPreview", "打印预览" },

                // 编辑操作
                { "Copy", "复制" },
                { "Paste", "粘贴" },
                { "Cut", "剪切" },
                { "Undo", "撤销" },
                { "Redo", "重做" },
                { "Find", "查找" },
                { "Replace", "替换" },
                { "Clear", "清除" },
                { "Delete", "删除" },
                { "Refresh", "刷新" },

                // 格式化
                { "Font", "字体" },
                { "FontBold", "粗体" },
                { "FontItalic", "斜体" },
                { "FontUnderline", "下划线" },
                { "FormatCells", "格式化单元格" },

                // 插入
                { "PictureInsert", "插入图片" },
                { "TableInsert", "插入表格" },
                { "SheetInsert", "插入工作表" },
                { "Comment", "批注" },

                // 工具
                { "Properties", "属性" },
                { "Help", "帮助" },
                { "Calculator", "计算器" },
                { "Calendar", "日历" },
                { "Filter", "筛选" },
                { "AutoSum", "自动求和" }
            };

            // 如果有精确匹配，返回映射的描述
            if (descriptionMappings.ContainsKey(imageId))
            {
                return descriptionMappings[imageId];
            }

            // 基于命名规律生成描述
            return GenerateDescriptionFromImageId(imageId);
        }

        /// <summary>
        /// 基于ImageId命名规律生成中文描述
        /// </summary>
        /// <param name="imageId">图标ID</param>
        /// <returns>生成的中文描述</returns>
        private static string GenerateDescriptionFromImageId(string imageId)
        {
            // 基于常见前缀的描述生成
            if (imageId.StartsWith("File")) return "文件操作";
            if (imageId.StartsWith("Edit")) return "编辑操作";
            if (imageId.StartsWith("Format")) return "格式设置";
            if (imageId.StartsWith("Insert")) return "插入操作";
            if (imageId.StartsWith("View")) return "视图操作";
            if (imageId.StartsWith("Table")) return "表格操作";
            if (imageId.StartsWith("Chart")) return "图表操作";
            if (imageId.StartsWith("Picture")) return "图片操作";
            if (imageId.StartsWith("Shape")) return "形状操作";
            if (imageId.StartsWith("Text")) return "文本操作";
            if (imageId.StartsWith("Font")) return "字体设置";
            if (imageId.StartsWith("Page")) return "页面设置";
            if (imageId.StartsWith("Print")) return "打印操作";
            if (imageId.StartsWith("Review")) return "审阅功能";
            if (imageId.StartsWith("Macro")) return "宏操作";
            if (imageId.StartsWith("Window")) return "窗口操作";
            if (imageId.StartsWith("Slide")) return "幻灯片操作";
            if (imageId.StartsWith("Animation")) return "动画效果";
            if (imageId.StartsWith("Pivot")) return "数据透视表";
            if (imageId.StartsWith("Query")) return "查询操作";
            if (imageId.StartsWith("Database")) return "数据库操作";
            if (imageId.StartsWith("Mail")) return "邮件操作";
            if (imageId.StartsWith("Contact")) return "联系人操作";
            if (imageId.StartsWith("Calendar")) return "日历操作";
            if (imageId.StartsWith("Task")) return "任务操作";

            // 默认描述
            return $"Office功能 - {imageId}";
        }

        /// <summary>
        /// 获取默认输出目录
        /// </summary>
        /// <returns>默认输出目录路径</returns>
        public static string GetDefaultOutputDirectory()
        {
            return DefaultOutputDirectory;
        }

        /// <summary>
        /// 确保默认输出目录存在
        /// </summary>
        /// <returns>是否成功创建或目录已存在</returns>
        public static bool EnsureDefaultOutputDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(DefaultOutputDirectory))
                {
                    Directory.CreateDirectory(DefaultOutputDirectory);
                    ETLogManager.Info($"创建默认输出目录: {DefaultOutputDirectory}");
                }
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"创建默认输出目录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导出所有OfficeImageId到Excel文件
        /// </summary>
        /// <param name="outputPath">输出文件路径，如果为空则使用默认路径</param>
        /// <returns>导出是否成功</returns>
        public static bool ExportOfficeImageIdsToExcel(string outputPath = null)
        {
            try
            {
                // 设置默认输出路径
                if (string.IsNullOrEmpty(outputPath))
                {
                    // 确保输出目录存在
                    if (!Directory.Exists(DefaultOutputDirectory))
                    {
                        Directory.CreateDirectory(DefaultOutputDirectory);
                        ETLogManager.Info($"创建输出目录: {DefaultOutputDirectory}");
                    }

                    outputPath = Path.Combine(DefaultOutputDirectory, $"OfficeImageIds导出_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
                }

                // 获取Excel应用程序实例
                Microsoft.Office.Interop.Excel.Application xlApp = ThisAddIn.ExcelApplication;
                if (xlApp == null)
                {
                    throw new ETException("无法获取Excel应用程序实例", ExcelOperationType.ApplicationOperation);
                }

                // 创建新工作簿
                Workbook workbook = xlApp.Workbooks.Add();
                Worksheet worksheet = workbook.ActiveSheet;

                try
                {
                    worksheet.Name = "OfficeImageIds";

                    // 设置表头
                    SetupWorksheetHeaders(worksheet);

                    // 填充数据
                    FillOfficeImageIdData(worksheet);

                    // 格式化工作表
                    FormatWorksheet(worksheet);

                    // 保存文件
                    workbook.SaveAs(outputPath);
                }
                finally
                {
                    // 确保工作簿被关闭
                    workbook.Close(false); // 不保存更改，因为已经保存了
                }

                // 获取统计信息
                var imageIds = GetOfficeImageIdsFromConfig();
                MessageBox.Show($"OfficeImageId导出成功！\n\n" +
                    $"导出统计：\n" +
                    $"• 总图标数量：{imageIds.Count} 个\n" +
                    $"• 数据源：配置文件\n" +
                    $"• 文件路径：{outputPath}",
                    "导出完成", MessageBoxButtons.OK, MessageBoxIcon.Information);

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"导出OfficeImageId失败: {ex.Message}");
                throw new ETException("导出OfficeImageId失败", ExcelOperationType.WorkbookOperation, ex);
            }
        }

        /// <summary>
        /// 导出自动枚举的ImageId到指定文件
        /// </summary>
        /// <param name="imageIds">要导出的ImageId列表</param>
        /// <param name="filePath">导出文件路径</param>
        /// <returns>是否导出成功</returns>
        public static bool ExportEnumeratedImageIdsToFile(List<string> imageIds, string filePath)
        {
            try
            {
                // 确保目录存在
                var dir = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }

                // 创建文件内容
                var content = new List<string>
                {
                    "# Office ImageId 导出文件",
                    "# 此文件由自动枚举功能生成，包含所有可用的OfficeImageId",
                    $"# 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    $"# 总数量: {imageIds.Count} 个",
                    "#",
                    "# 说明:",
                    "# - 每行一个ImageId",
                    "# - 所有ImageId都已通过GetImageMso方法验证有效性",
                    "# - 按字母顺序排列",
                    "#"
                };

                // 添加ImageId列表
                content.AddRange(imageIds);

                // 保存到文件
                File.WriteAllLines(filePath, content, System.Text.Encoding.UTF8);

                ETLogManager.Info($"成功导出 {imageIds.Count} 个ImageId到文件: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"导出ImageId到文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 强制重新枚举所有OfficeImageId并更新缓存
        /// </summary>
        /// <param name="updateCache">是否更新文件缓存</param>
        /// <returns>枚举到的ImageId列表</returns>
        public static List<string> ForceReenumerateImageIds(bool updateCache = true)
        {
            try
            {
                ETLogManager.Info("开始强制重新枚举OfficeImageId...");

                // 清空内存缓存
                _cachedOfficeImageIds = null;

                // 删除旧的缓存文件
                if (File.Exists(OfficeImageIdCachePath))
                {
                    try
                    {
                        File.Delete(OfficeImageIdCachePath);
                        ETLogManager.Debug("已删除旧的缓存文件");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"删除旧缓存文件失败: {ex.Message}");
                    }
                }

                // 执行自动枚举
                var imageIds = AutoEnumerateOfficeImageIds();

                // 更新内存缓存
                _cachedOfficeImageIds = imageIds;

                // 更新文件缓存
                if (updateCache && EnableFileCache && imageIds.Count > 0)
                {
                    SaveImageIdsToCacheFile(imageIds);
                }

                ETLogManager.Info($"重新枚举完成，发现 {imageIds.Count} 个有效ImageId");
                return imageIds;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"强制重新枚举失败: {ex.Message}");
                return GetFallbackImageIds();
            }
        }

        /// <summary>
        /// 获取自动枚举统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public static string GetEnumerationStatistics()
        {
            try
            {
                var imageIds = GetOfficeImageIdsFromConfig();
                var categories = imageIds.GroupBy(id => GetImageIdCategory(id))
                    .OrderByDescending(g => g.Count())
                    .Take(5)
                    .Select(g => $"{g.Key}: {g.Count()}个")
                    .ToList();

                var dataSource = _cachedOfficeImageIds != null ? "内存缓存" :
                    (EnableFileCache && File.Exists(OfficeImageIdCachePath) ? "文件缓存" : "自动枚举");

                return $"OfficeImageId统计信息：\n" +
                    $"• 数据源：{dataSource}\n" +
                    $"• 总图标数量：{imageIds.Count} 个\n" +
                    $"• 主要分类：\n  {string.Join("\n  ", categories)}\n" +
                    $"• 缓存文件：{(File.Exists(OfficeImageIdCachePath) ? "存在" : "不存在")}\n" +
                    $"• 获取方式：基于GetImageMso自动枚举";
            }
            catch (Exception ex)
            {
                return $"获取统计信息失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 获取配置文件统计信息（兼容性方法）
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public static string GetConfigFileStatistics()
        {
            return GetEnumerationStatistics();
        }

        /// <summary>
        /// 设置工作表表头
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        private static void SetupWorksheetHeaders(Worksheet worksheet)
        {
            // 设置表头
            worksheet.Cells[1, 1] = "序号";
            worksheet.Cells[1, 2] = "OfficeImageId";
            worksheet.Cells[1, 3] = "中文描述";
            worksheet.Cells[1, 4] = "使用频率";
            worksheet.Cells[1, 5] = "分类";
            worksheet.Cells[1, 6] = "图标预览";

            // 设置表头格式
            Range headerRange = worksheet.Range["A1:F1"];
            headerRange.Font.Bold = true;
            headerRange.Interior.Color = System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.LightBlue);
            headerRange.HorizontalAlignment = XlHAlign.xlHAlignCenter;

            // 设置图标预览列的宽度
            worksheet.Columns[6].ColumnWidth = 15;
        }

        /// <summary>
        /// 填充OfficeImageId数据
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 从配置文件读取所有OfficeImageId
        /// 2. 为每个图标生成相应的描述和分类信息
        /// 3. 在第6列添加真实的Office图标预览
        /// 4. 显示处理进度信息
        /// </remarks>
        private static void FillOfficeImageIdData(Worksheet worksheet)
        {
            int row = 2;
            int index = 1;

            // 从配置文件获取所有图标ID
            var allImageIds = GetOfficeImageIdsFromConfig();

            if (allImageIds.Count == 0)
            {
                // 如果配置文件为空，添加一行提示信息
                worksheet.Cells[row, 1] = 1;
                worksheet.Cells[row, 2] = "配置文件为空";
                worksheet.Cells[row, 3] = "请检查配置文件是否存在";
                worksheet.Cells[row, 4] = "无";
                worksheet.Cells[row, 5] = "系统提示";
                worksheet.Cells[row, 6] = "❌";
                return;
            }

            ETLogManager.Info($"开始填充 {allImageIds.Count} 个OfficeImageId数据");

            // 填充所有图标数据
            foreach (var imageId in allImageIds)
            {
                try
                {
                    worksheet.Cells[row, 1] = index;
                    worksheet.Cells[row, 2] = imageId;
                    worksheet.Cells[row, 3] = GetImageIdDescription(imageId);
                    worksheet.Cells[row, 4] = GetImageIdUsageFrequency(imageId);
                    worksheet.Cells[row, 5] = GetImageIdCategory(imageId);

                    // 在第6列添加图标预览
                    AddIconPreviewToCell(worksheet, row, 6, imageId);

                    // 每处理100个图标记录一次进度
                    if (index % 100 == 0)
                    {
                        ETLogManager.Info($"已处理 {index}/{allImageIds.Count} 个图标");
                    }

                    row++;
                    index++;
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"处理图标 {imageId} 时发生错误: {ex.Message}");

                    // 即使出错也要填充基本信息
                    worksheet.Cells[row, 1] = index;
                    worksheet.Cells[row, 2] = imageId;
                    worksheet.Cells[row, 3] = "处理出错";
                    worksheet.Cells[row, 4] = "未知";
                    worksheet.Cells[row, 5] = "错误";
                    worksheet.Cells[row, 6] = "❌";

                    row++;
                    index++;
                }
            }

            ETLogManager.Info($"完成填充 {allImageIds.Count} 个OfficeImageId数据");
        }

        /// <summary>
        /// 在指定单元格中添加图标预览
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        /// <param name="row">行号</param>
        /// <param name="column">列号</param>
        /// <param name="imageId">图标ID</param>
        /// <remarks>
        /// 使用GetImageMso方法从Office中提取真实图标
        /// 参考：https://www.cnblogs.com/liweis/p/11762679.html
        /// </remarks>
        private static void AddIconPreviewToCell(Worksheet worksheet, int row, int column, string imageId)
        {
            try
            {
                // 使用GetImageMso方法提取图标
                if (TryExtractIconWithGetImageMso(worksheet, row, column, imageId))
                {
                    return;
                }

                // 如果提取失败，显示图标ID作为备用
                Range cell = worksheet.Cells[row, column];
                cell.Value = $"[{imageId}]";
                cell.Font.Size = 10;
                cell.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                cell.VerticalAlignment = XlVAlign.xlVAlignCenter;

                // 设置行高
                worksheet.Rows[row].RowHeight = 24;
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"添加图标预览失败 {imageId}: {ex.Message}");
                // 失败时显示图标ID
                worksheet.Cells[row, column] = $"[{imageId}]";
            }
        }

        /// <summary>
        /// 使用GetImageMso方法提取Office图标
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        /// <param name="row">行号</param>
        /// <param name="column">列号</param>
        /// <param name="imageId">图标ID</param>
        /// <returns>是否成功提取并插入图标</returns>
        /// <remarks>
        /// 基于博客园文章方法：https://www.cnblogs.com/liweis/p/11762679.html
        /// 使用Office的GetImageMso方法获取高质量图标
        /// </remarks>
        private static bool TryExtractIconWithGetImageMso(Worksheet worksheet, int row, int column, string imageId)
        {
            try
            {
                var xlApp = ThisAddIn.ExcelApplication;
                if (xlApp?.CommandBars == null)
                {
                    ETLogManager.Debug("CommandBars不可用");
                    return false;
                }

                // 使用GetImageMso方法获取图标，32x32像素
                stdole.IPictureDisp pictureDisp = xlApp.CommandBars.GetImageMso(imageId, 32, 32);
                if (pictureDisp == null)
                {
                    ETLogManager.Debug($"GetImageMso返回null: {imageId}");
                    return false;
                }

                // 将IPictureDisp转换为Bitmap
                Bitmap bitmap = OfficeImageHelper.ConvertPixelByPixel(pictureDisp);
                if (bitmap == null)
                {
                    ETLogManager.Debug($"Bitmap转换失败: {imageId}");
                    return false;
                }

                // 将Bitmap插入到Excel单元格
                return InsertBitmapToCell(worksheet, row, column, bitmap, imageId);
            }
            catch (Exception ex)
            {
                ETLogManager.Debug($"GetImageMso提取图标失败 {imageId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将Bitmap图像插入到Excel单元格
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        /// <param name="row">行号</param>
        /// <param name="column">列号</param>
        /// <param name="bitmap">图像对象</param>
        /// <param name="imageId">图标ID</param>
        /// <returns>是否成功插入</returns>
        private static bool InsertBitmapToCell(Worksheet worksheet, int row, int column, Bitmap bitmap, string imageId)
        {
            try
            {
                // 获取单元格范围
                Range cell = worksheet.Cells[row, column];

                // 设置行高和列宽以适应图标
                worksheet.Rows[row].RowHeight = 32;
                worksheet.Columns[column].ColumnWidth = 6;

                // 创建临时文件保存图片
                string tempPath = Path.GetTempFileName();
                string tempImagePath = Path.ChangeExtension(tempPath, ".png");

                try
                {
                    // 保存Bitmap为PNG文件
                    bitmap.Save(tempImagePath, ImageFormat.Png);

                    // 将图片插入到Excel中
                    var shapes = worksheet.Shapes;
                    var picture = shapes.AddPicture(
                        tempImagePath,
                        Microsoft.Office.Core.MsoTriState.msoFalse,
                        Microsoft.Office.Core.MsoTriState.msoTrue,
                        cell.Left + 2,
                        cell.Top + 2,
                        24, 24);

                    // 设置图片属性
                    picture.Name = $"Icon_{imageId}_{row}_{column}";
                    picture.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoTrue;

                    ETLogManager.Debug($"成功插入图标 {imageId} 到单元格 {row},{column}");
                    return true;
                }
                finally
                {
                    // 清理临时文件
                    try
                    {
                        if (File.Exists(tempPath)) File.Delete(tempPath);
                        if (File.Exists(tempImagePath)) File.Delete(tempImagePath);
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Debug($"插入Bitmap到单元格失败 {imageId}: {ex.Message}");
                return false;
            }
            finally
            {
                // 释放Bitmap资源
                bitmap?.Dispose();
            }
        }










        /// <summary>
        /// 格式化工作表
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        private static void FormatWorksheet(Worksheet worksheet)
        {
            try
            {
                // 自动调整列宽
                worksheet.Columns.AutoFit();

                // 获取使用的范围
                Range dataRange = worksheet.UsedRange;
                if (dataRange != null)
                {
                    // 设置边框
                    dataRange.Borders.LineStyle = XlLineStyle.xlContinuous;
                    dataRange.Borders.Weight = XlBorderWeight.xlThin;

                    // 只有当数据超过1行时才设置筛选
                    if (dataRange.Rows.Count > 1)
                    {
                        try
                        {
                            // 选择表头范围来应用筛选
                            Range headerRange = worksheet.Range["A1:F1"];
                            headerRange.AutoFilter();
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning($"设置自动筛选失败: {ex.Message}");
                            // 筛选失败不影响主要功能，继续执行
                        }
                    }

                    // 冻结首行
                    try
                    {
                        worksheet.Range["A2"].Select();
                        ThisAddIn.ExcelApplication.ActiveWindow.FreezePanes = true;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"冻结窗格失败: {ex.Message}");
                        // 冻结失败不影响主要功能，继续执行
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"格式化工作表时发生错误: {ex.Message}");
                // 格式化失败不影响数据导出，继续执行
            }
        }

        /// <summary>
        /// 测试GetImageMso方法提取单个图标
        /// </summary>
        /// <param name="imageId">要测试的图标ID</param>
        /// <returns>测试结果信息</returns>
        public static string TestGetImageMsoExtraction(string imageId)
        {
            try
            {
                var xlApp = ThisAddIn.ExcelApplication;
                if (xlApp?.CommandBars == null)
                {
                    return $"❌ CommandBars不可用";
                }

                // 使用GetImageMso方法获取图标
                stdole.IPictureDisp pictureDisp = xlApp.CommandBars.GetImageMso(imageId, 32, 32);
                if (pictureDisp == null)
                {
                    return $"❌ GetImageMso返回null: {imageId}";
                }

                // 将IPictureDisp转换为Bitmap
                Bitmap bitmap = OfficeImageHelper.ConvertPixelByPixel(pictureDisp);
                if (bitmap == null)
                {
                    return $"❌ Bitmap转换失败: {imageId}";
                }

                // 获取图片信息
                string result = $"✅ 成功提取图标: {imageId}\n" +
                    $"   尺寸: {bitmap.Width}x{bitmap.Height}\n" +
                    $"   格式: {bitmap.PixelFormat}\n" +
                    $"   Handle: {pictureDisp.Handle}";

                bitmap.Dispose();
                return result;
            }
            catch (Exception ex)
            {
                return $"❌ 提取失败 {imageId}: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试自动枚举功能
        /// </summary>
        /// <returns>测试结果信息</returns>
        public static string TestAutoEnumeration()
        {
            try
            {
                var startTime = DateTime.Now;
                ETLogManager.Info("开始测试自动枚举功能...");

                // 清空缓存以确保重新枚举
                _cachedOfficeImageIds = null;

                // 执行自动枚举
                var imageIds = AutoEnumerateOfficeImageIds();
                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                var result = $"✅ 自动枚举测试完成\n\n" +
                    $"📊 统计信息：\n" +
                    $"• 发现图标数量：{imageIds.Count} 个\n" +
                    $"• 耗时：{duration.TotalSeconds:F2} 秒\n" +
                    $"• 平均速度：{(imageIds.Count / duration.TotalSeconds):F1} 个/秒\n\n" +
                    $"🎯 前10个图标示例：\n" +
                    string.Join("\n", imageIds.Take(10).Select((id, index) => $"  {index + 1}. {id}"));

                ETLogManager.Info($"自动枚举测试完成，发现 {imageIds.Count} 个图标，耗时 {duration.TotalSeconds:F2} 秒");
                return result;
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ 自动枚举测试失败：{ex.Message}";
                ETLogManager.Error($"自动枚举测试失败: {ex.Message}");
                return errorMsg;
            }
        }



        /// <summary>
        /// 获取图标使用频率
        /// </summary>
        /// <param name="imageId">图标ID</param>
        /// <returns>使用频率描述</returns>
        private static string GetImageIdUsageFrequency(string imageId)
        {
            // 基于项目中的实际使用情况返回频率
            var highFrequencyIds = new[] { "Help", "Refresh", "Find", "Clear", "MoreCommands", "Properties" };
            var mediumFrequencyIds = new[] { "FileSave", "FileOpen", "Copy", "Delete", "Filter" };

            if (highFrequencyIds.Contains(imageId)) return "高频使用";
            if (mediumFrequencyIds.Contains(imageId)) return "中频使用";
            return "低频使用";
        }

        /// <summary>
        /// 获取图标分类
        /// </summary>
        /// <param name="imageId">图标ID</param>
        /// <returns>图标分类</returns>
        private static string GetImageIdCategory(string imageId)
        {
            if (imageId.StartsWith("File")) return "文件操作";
            if (imageId.StartsWith("Font")) return "字体格式";
            if (imageId.StartsWith("Picture") || imageId.StartsWith("Shape")) return "图形图像";
            if (imageId.StartsWith("Macro")) return "宏脚本";
            if (imageId.StartsWith("Window")) return "窗口视图";
            if (imageId.Contains("Insert")) return "插入操作";
            if (imageId.Contains("Format") || imageId.Contains("Setup")) return "格式设置";
            return "其他工具";
        }
    }
}