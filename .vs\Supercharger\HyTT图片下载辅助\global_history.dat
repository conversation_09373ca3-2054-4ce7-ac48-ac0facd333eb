<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.Dispose#void#bool</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerdownload.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:42:07.5803058+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.frmChinaTowerdownload##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>frmChinaTowerdownload</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:49:31.0046662+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.ReadIniConfig#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ReadIniConfig</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:49:22.733325+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetAuthorizationKey#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetAuthorizationKey</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:49:12.4021119+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetStationInfoListFromDatabase#List&lt;StationInfoExcerpt&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetStationInfoListFromDatabase</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:49:10.7257855+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetServerPhotoInfoList#List&lt;PhotoInfoExcerpt&gt;#StationInfoExcerpt</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetServerPhotoInfoList</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:48:47.9459506+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetHtml#string#string, string, string, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetHtml</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyTT\HyTT图片下载辅助.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T14:48:39.9045661+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyTT\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>HyTT图片下载辅助</ProjectName></ProjectData>