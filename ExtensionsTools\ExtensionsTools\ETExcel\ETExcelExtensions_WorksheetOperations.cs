using Microsoft.Office.Interop.Excel;
using System;

namespace ET
{
    /// <summary>
    /// Excel工作表操作扩展类
    /// </summary>
    /// <remarks>
    /// 此类提供了一系列用于操作Excel工作表的扩展方法。 主要功能包括：
    /// - 复制工作表
    /// - 创建新工作表
    /// - 从模板创建工作表
    ///
    /// 使用注意：
    /// 1. 所有方法都包含异常处理
    /// 2. 工作表名称相关操作会自动处理重名情况
    /// 3. 复制操作会保持原有格式和内容
    /// </remarks>
    public static partial class ETExcelExtensions
    {
        /// <summary>
        /// 将工作表复制到指定的工作簿
        /// </summary>
        /// <param name="worksheetFrom">源工作表</param>
        /// <param name="workbookTo">目标工作簿</param>
        /// <returns>复制后的工作表，如果复制失败则返回null</returns>
        /// <remarks>
        /// 此方法将源工作表复制到目标工作簿的第一个工作表之后。
        ///
        /// 使用示例： ```csharp var newSheet = sourceSheet.CopyWorksheetTo(targetWorkbook); if (newSheet
        /// != null) { // 工作表复制成功 } ```
        ///
        /// 注意事项：
        /// 1. 源工作表必须有效
        /// 2. 目标工作簿必须有效且至少包含一个工作表
        /// 3. 复制后的工作表名称与源工作表相同
        /// </remarks>
        /// <exception cref="ETException">当源工作表或目标工作簿无效时抛出</exception>
        public static Worksheet CopyWorksheetTo(this Worksheet worksheetFrom, Workbook workbookTo)
        {
            if (worksheetFrom == null)
                throw new ETException("源工作表不能为null");

            if (workbookTo == null)
                throw new ETException("目标工作簿不能为null");

            try
            {
                return worksheetFrom.CopyWorksheetAfter((Worksheet)workbookTo.Worksheets[1]);
            }
            catch (Exception ex)
            {
                throw new ETException($"复制工作表到工作簿时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将工作表复制到新的工作簿
        /// </summary>
        /// <param name="worksheetToCopy">要复制的工作表</param>
        /// <returns>新工作簿中复制的工作表，如果复制失败则返回null</returns>
        /// <remarks>
        /// 此方法创建一个新的工作簿，并将指定的工作表复制到其中。
        ///
        /// 使用示例： ```csharp var newSheet = sourceSheet.CopyWorksheetToNewWorkbook(); if (newSheet !=
        /// null) { // 工作表复制到新工作簿成功 } ```
        ///
        /// 注意事项：
        /// 1. 源工作表必须有效
        /// 2. 会自动删除新工作簿中的默认工作表
        /// 3. 新工作簿会自动设置为可见
        /// </remarks>
        /// <exception cref="ETException">当源工作表无效或复制过程中发生错误时抛出</exception>
        public static Worksheet CopyWorksheetToNewWorkbook(this Worksheet worksheetToCopy)
        {
            if (worksheetToCopy == null)
                throw new ETException("源工作表不能为null");

            Workbook newWorkbook = null;
            bool originalDisplayAlerts = XlApp.DisplayAlerts;

            try
            {
                // 创建新工作簿
                newWorkbook = XlApp.Workbooks.Add();

                // 关闭Excel警告提示
                XlApp.DisplayAlerts = false;

                // 复制工作表到新工作簿
                worksheetToCopy.Copy(Type.Missing, newWorkbook.Sheets[1]);

                // 获取新复制的工作表
                Worksheet copiedWorksheet = (Worksheet)newWorkbook.Sheets[newWorkbook.Sheets.Count];

                // 删除新工作簿中的默认工作表
                for (int i = newWorkbook.Worksheets.Count; i > 0; i--)
                {
                    Worksheet sheet = (Worksheet)newWorkbook.Worksheets[i];
                    if (sheet.Name == "Sheet1" && newWorkbook.Worksheets.Count > 1)
                    {
                        sheet.Delete();
                        break;
                    }
                }

                // 设置新Excel应用实例可见
                XlApp.Visible = true;

                return copiedWorksheet;
            }
            catch (Exception ex)
            {
                throw new ETException($"复制工作表到新工作簿时出错: {ex.Message}", ex);
            }
            finally
            {
                // 恢复Excel警告提示
                XlApp.DisplayAlerts = originalDisplayAlerts;
            }
        }

        /// <summary>
        /// 复制工作表到指定位置
        /// </summary>
        /// <param name="sourceWorksheet">源工作表</param>
        /// <param name="targetWorksheet">目标工作表（复制到此工作表之后）</param>
        /// <returns>复制后的新工作表，如果复制失败则返回null</returns>
        /// <remarks>
        /// 此方法会将源工作表复制到目标工作表之后。
        ///
        /// 使用示例： ```csharp var newSheet = sourceSheet.CopyWorksheetTo(targetSheet); if (newSheet !=
        /// null) { // 工作表复制成功 } ```
        ///
        /// 注意事项：
        /// 1. 源工作表和目标工作表必须有效
        /// 2. 复制过程会临时启用快速模式以提高性能
        /// 3. 复制后的工作表名称与源工作表相同
        /// </remarks>
        /// <exception cref="ETException">当工作表无效或复制过程中发生错误时抛出</exception>
        public static Worksheet CopyWorksheetAfter(
            this Worksheet sourceWorksheet,
            Worksheet targetWorksheet
        )
        {
            if (sourceWorksheet == null)
                throw new ETException("源工作表不能为null");

            if (targetWorksheet == null)
                throw new ETException("目标工作表不能为null");

            string sourceName = sourceWorksheet.Name;
            Workbook targetWorkbook = targetWorksheet.Parent;

            try
            {
                // 设置Excel为快速模式，提高性能
                SetAppFastMode();

                // 复制工作表
                sourceWorksheet.Copy(After: targetWorksheet);

                // 返回新复制的工作表
                return targetWorkbook.Worksheets[sourceName];
            }
            catch (Exception ex)
            {
                throw new ETException($"复制工作表时出错: {ex.Message}", ex);
            }
            finally
            {
                // 恢复Excel为正常模式
                SetAppNormalMode();
            }
        }

        /// <summary>
        /// 从模板文件复制工作表到目标工作簿
        /// </summary>
        /// <param name="worksheetName">要复制的工作表名称</param>
        /// <param name="targetWorkbook">目标工作簿</param>
        /// <param name="overwrite">是否覆盖已存在的同名工作表</param>
        /// <param name="templateFileName">模板文件名</param>
        /// <returns>复制后的新工作表，如果复制失败则返回null</returns>
        /// <remarks>
        /// 此方法从指定的模板文件中复制工作表到目标工作簿。
        ///
        /// 使用示例： ```csharp var newSheet = CopyWorksheetFromTemplate("模板表", workbook, true); if
        /// (newSheet != null) { // 从模板复制工作表成功 } ```
        ///
        /// 注意事项：
        /// 1. 模板文件必须存在且包含指定名称的工作表
        /// 2. 如果目标工作簿中已存在同名工作表：
        /// - overwrite为true时会覆盖
        /// - overwrite为false时会返回已存在的工作表
        /// 3. 复制过程会临时关闭屏幕更新以提高性能
        /// </remarks>
        /// <exception cref="ETException">当参数无效或复制过程中发生错误时抛出</exception>
        public static Worksheet CopyWorksheetFromTemplateToWorkbook(
            string worksheetName,
            Workbook targetWorkbook,
            bool overwrite = false,
            string templateFileName = "ExcelTemplate.xlsx"
        )
        {
            if (string.IsNullOrEmpty(worksheetName))
                throw new ETException("工作表名称不能为空");

            targetWorkbook = targetWorkbook ?? XlApp.ActiveWorkbook;
            if (targetWorkbook == null)
                throw new ETException("目标工作簿不能为null");

            try
            {
                // 检查目标工作簿中是否已存在同名工作表
                Worksheet existingSheet = targetWorkbook.GetWorksheetByName(worksheetName);
                if (existingSheet != null)
                {
                    if (overwrite)
                        existingSheet.Delete(); // 如果覆盖，且已经存在，则先删除
                    else
                        return existingSheet;
                }

                // 关闭屏幕更新，提高性能
                Update(false);

                // 打开模板工作簿
                Workbook templateWorkbook = templateFileName.GetWorkbookNameByFormulaThenByName()
                    ?? OpenWorkbook(ETExcelConfig.TemplateExcelPath);

                if (templateWorkbook == null)
                    throw new ETException("无法打开模板工作簿");

                // 获取要复制的工作表
                Worksheet sourceSheet = templateWorkbook.GetWorksheetByName(worksheetName);
                if (sourceSheet == null)
                    throw new ETException($"模板工作簿中不存在名为 {worksheetName} 的工作表");

                // 复制工作表到目标工作簿
                Worksheet newSheet = CopyWorksheetTo(sourceSheet, targetWorkbook);

                // 关闭模板工作簿
                templateWorkbook.Close(false);

                return newSheet;
            }
            catch (Exception ex) when (!(ex is ETException))
            {
                throw new ETException($"从模板复制工作表时出错: {ex.Message}", ex);
            }
            finally
            {
                // 恢复屏幕更新
                Update(true);
            }
        }

        /// <summary>
        /// 在工作簿中创建新的工作表
        /// </summary>
        /// <param name="workbook">目标工作簿</param>
        /// <param name="worksheetName">新工作表的名称</param>
        /// <param name="overwrite">是否覆盖已存在的同名工作表</param>
        /// <returns>新创建的工作表，如果创建失败则返回null</returns>
        /// <remarks>
        /// 此方法在指定的工作簿中创建一个新的工作表。
        ///
        /// 使用示例： ```csharp var newSheet = NewWorksheet(workbook, "新表", true); if (newSheet != null)
        /// { // 创建新工作表成功 } ```
        ///
        /// 注意事项：
        /// 1. 工作表名称不能为空
        /// 2. 如果目标工作簿中已存在同名工作表：
        /// - overwrite为true时会覆盖
        /// - overwrite为false时会返回已存在的工作表
        /// 3. 如果未指定工作簿，将使用活动工作簿
        /// </remarks>
        /// <exception cref="ETException">当参数无效或创建过程中发生错误时抛出</exception>
        public static Worksheet CreateNewWorksheet(
            Workbook workbook,
            string worksheetName,
            bool overwrite = false
        )
        {
            if (string.IsNullOrEmpty(worksheetName))
                throw new ETException("工作表名称不能为空");

            workbook = workbook ?? XlApp.ActiveWorkbook;
            if (workbook == null)
                throw new ETException("工作簿不能为null");

            try
            {
                // 检查是否已存在同名工作表
                Worksheet existingSheet = workbook.GetWorksheetByName(worksheetName);
                if (existingSheet != null)
                {
                    if (overwrite)
                        existingSheet.Delete(); // 如果覆盖，且已经存在，则先删除
                    else
                        return existingSheet;
                }

                // 创建新工作表
                Worksheet newSheet = workbook.Worksheets.Add();
                newSheet.Name = worksheetName;

                return newSheet;
            }
            catch (Exception ex)
            {
                throw new ETException($"创建新工作表时出错: {ex.Message}", ex);
            }
        }
    }
}