using System;
using System.Windows.Forms;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// 基站数据处理器主窗体 提供各个功能模块的入口（已重构为独立窗体）
    /// </summary>
    public partial class StationDataProcessorForm : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数 - 现在直接显示主窗体
        /// </summary>
        public StationDataProcessorForm()
        {
            InitializeComponent();
            
            // 直接显示新的主窗体
            var mainForm = new StationDataProcessorMainForm();
            mainForm.Show();
            
            ETLogManager.Info(this, "StationDataProcessorForm已重构为主窗体入口");
            
            // 关闭当前窗体（因为我们只是作为入口）
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;
            this.Visible = false;
        }

        #endregion 构造函数

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "StationDataProcessorForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}
