# VSTO安装程序签名问题解决方案

## 🚨 问题描述

**错误信息**：
```
System.Deployment.Application.InvalidDeploymentException: 清单 XML 签名无效
System.Security.Cryptography.CryptographicException: 无法为所提供的签名算法创建 SignatureDescription
```

**问题原因**：
1. 使用的数字签名算法在目标系统上不被支持
2. 证书兼容性问题
3. .NET Framework版本兼容性问题

## 🛠️ 解决方案

### 方案1：临时禁用签名（快速解决）

**优点**：立即解决问题，适合内部测试
**缺点**：降低安全性，不适合生产环境

**实施步骤**：
1. ✅ 已修改项目文件，将 `<SignManifests>true</SignManifests>` 改为 `false`
2. 重新编译项目
3. 重新发布VSTO安装程序

### 方案2：生成兼容证书（推荐）

**优点**：保持安全性，解决兼容性问题
**缺点**：需要重新配置证书

**实施步骤**：
1. 运行证书生成脚本：
   ```powershell
   .\.script\Generate-CompatibleCertificate.ps1 -CertificateName "HyExcelVsto" -Password "123456"
   ```

2. 更新项目配置：
   - 将新生成的证书文件复制到项目目录
   - 修改项目文件中的证书引用
   - 重新启用签名：`<SignManifests>true</SignManifests>`

### 方案3：配置信任设置

**优点**：不修改项目，通过系统配置解决
**缺点**：需要在每台目标机器上配置

**实施步骤**：
1. 在目标机器上以管理员身份运行：
   ```powershell
   .\.script\Configure-VSTOTrust.ps1 -EnableFullTrust -DisableSignatureCheck
   ```

2. 重启Office应用程序

## 📋 详细操作步骤

### 步骤1：立即解决（方案1）

1. **重新编译项目**：
   ```bash
   # 在Visual Studio中重新生成解决方案
   # 或使用命令行：
   msbuild HyExcelVsto.csproj /p:Configuration=Release
   ```

2. **重新发布**：
   - 右键项目 → 发布
   - 或使用发布配置文件

3. **测试安装**：
   - 将生成的安装文件复制到目标机器
   - 尝试安装VSTO插件

### 步骤2：长期解决（方案2）

1. **生成新证书**：
   ```powershell
   # 以管理员身份运行PowerShell
   cd "D:\HyDevelop\HyHelper\HyHelper"
   .\.script\Generate-CompatibleCertificate.ps1
   ```

2. **更新项目配置**：
   ```xml
   <!-- 在HyExcelVsto.csproj中更新 -->
   <PropertyGroup>
     <SignManifests>true</SignManifests>
   </PropertyGroup>
   <PropertyGroup>
     <ManifestKeyFile>HyExcelVsto_Compatible.pfx</ManifestKeyFile>
   </PropertyGroup>
   ```

3. **重新编译和发布**

### 步骤3：系统配置（方案3）

1. **配置目标机器信任**：
   ```powershell
   # 在目标机器上运行
   .\.script\Configure-VSTOTrust.ps1 -VSTOPath "C:\path\to\HyExcelVsto.vsto" -EnableFullTrust
   ```

2. **验证配置**：
   - 检查Office信任中心设置
   - 验证.NET Framework信任设置

## 🔧 故障排除

### 常见问题1：证书生成失败
**解决方法**：
- 确保以管理员身份运行脚本
- 检查Windows SDK是否已安装
- 使用PowerShell内置证书功能

### 常见问题2：仍然提示签名错误
**解决方法**：
- 清除Office缓存：删除 `%LOCALAPPDATA%\Apps\2.0` 目录
- 重新注册VSTO运行时
- 检查.NET Framework版本兼容性

### 常见问题3：ExtensionsTools依赖问题
**解决方法**：
- 确保ExtensionsTools.dll在输出目录中
- 检查程序集绑定重定向配置
- 验证依赖项版本兼容性

## 📊 方案对比

| 方案 | 安全性 | 实施难度 | 适用场景 | 推荐度 |
|------|--------|----------|----------|--------|
| 禁用签名 | 低 | 简单 | 内部测试 | ⭐⭐⭐ |
| 兼容证书 | 高 | 中等 | 生产环境 | ⭐⭐⭐⭐⭐ |
| 信任配置 | 中 | 复杂 | 特定环境 | ⭐⭐⭐⭐ |

## 🎯 推荐实施顺序

1. **立即解决**：使用方案1快速解决当前问题
2. **测试验证**：确认VSTO插件可以正常安装和运行
3. **长期优化**：实施方案2生成兼容证书
4. **部署配置**：根据需要使用方案3配置目标环境

## 📝 注意事项

1. **备份重要文件**：修改前备份原始证书和项目文件
2. **测试环境验证**：在测试环境中验证解决方案
3. **文档记录**：记录所有配置更改和证书信息
4. **安全考虑**：生产环境建议使用正式的代码签名证书

## 🔄 后续维护

1. **定期更新证书**：证书过期前及时更新
2. **监控兼容性**：关注.NET Framework和Office版本更新
3. **用户反馈**：收集用户安装问题反馈
4. **持续优化**：根据实际使用情况优化配置
