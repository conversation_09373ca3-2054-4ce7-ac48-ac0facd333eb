﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Windows.Forms;
using HyExcelVsto.Extensions.Dx51Helper;

namespace HyExcelVsto.Module.WX
{
    public partial class frm51Helper : Form
    {



        public async void 上传图纸()
        {
            try
            {
                ETNotificationHelper.ShowNotification("注意：只能选择数据区域，不能包含表头，交付编号为空将跳过", true);
                Range inputRange = ETExcelExtensions.GetRangeSelection(
                    $"请选择要上传图纸的数据区域。{Environment.NewLine}{Environment.NewLine}交付编号\t文件路径\t上传结果{Environment.NewLine}{Environment.NewLine}注意：只能选择数据区域，不能包含表头，交付编号为空将跳过。{Environment.NewLine}",
                    false);
                if (inputRange == null)
                {
                    textBoxLog.WriteLog("未选择数据区域，或者选择区域无效");
                    return;
                }
                if (inputRange.Columns.Count != 3)
                {
                    textBoxLog.WriteLog("选择区域列数不正确，请选择交付编号、文件路径、上传结果 3列");
                    return;
                }

                if (!ETExcelExtensions.VerifyCode("确认是否开始上传，上传时间费时较多，上传期间，无法操作Excel"))
                    return;

                //总数，有效操作数
                int totalCount = 0;
                int successCount = 0;

                inputRange = inputRange.GetVisibleRange();
                if (inputRange.Rows.Count > 1000)
                {
                    ETNotificationHelper.ShowNotification("行数限制为1000行，请缩小范围", true);
                    return;
                }

                //如果行数大于10，则初始化站点信息缓存
                if (inputRange.Rows.Count > 10)
                    await Zn51Helper.InitializeWorksPointCache(true).ConfigureAwait(false);

                foreach (Range row in inputRange.Rows)
                {
                    totalCount++;
                    Range cellWorksPointNo = row.Cells[1, 1];
                    Range cellFilePath = row.Cells[1, 2];
                    Range cellResult = row.Cells[1, 3];
                    if (cellWorksPointNo.IsCellEmpty() || cellFilePath.IsCellEmpty())
                        continue;

                    string worksPointNo = cellWorksPointNo.Value2.ToString();
                    string filePath = cellFilePath.Value2.ToString();
                    if (!File.Exists(filePath))
                    {
                        textBoxLog.WriteLog($"文件{filePath}不存在，跳过上传");
                        cellResult.Value2 = "文件不存在";
                        continue;
                    }

                    var (errorMessage, pointInfo) = await Zn51Helper.UploadFileAndUpdateServer(filePath, worksPointNo).ConfigureAwait(false);
                    cellResult.Value2 = errorMessage ?? "成功";
                    textBoxLog.WriteLog(
                        $"交付编号：{worksPointNo}，交付名称：{pointInfo?.worksPointName ?? "null"}，处理结果：{errorMessage ?? "成功"}");
                    if (errorMessage == null)
                        successCount++;
                }

                textBoxLog.WriteLog($"上传图纸 全部操作完成，总数{totalCount}，有效操作数{successCount}");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"上传图纸过程中发生错误: {ex.Message}");
                ETNotificationHelper.ShowNotification($"上传图纸失败: {ex.Message}", true);
            }
        }

        public async void 上传设计交底()
        {
            try
            {
                ETNotificationHelper.ShowNotification("注意：只能选择数据区域，不能包含表头，交付编号为空将跳过。", true);
                Range inputRange = ETExcelExtensions.GetRangeSelection(
                    $"请选择要上传设计交底的数据区域。{Environment.NewLine}{Environment.NewLine}交付编号\t文件路径\t上传结果{Environment.NewLine}{Environment.NewLine}注意：只能选择数据区域，不能包含表头，交付编号为空将跳过。{Environment.NewLine}",
                    false);
                if (inputRange == null)
                {
                    textBoxLog.WriteLog("未选择数据区域，或者选择区域无效");
                    return;
                }
                if (inputRange.Columns.Count != 3)
                {
                    textBoxLog.WriteLog("选择区域列数不正确，请选择交付编号、文件路径、上传结果 3列");
                    return;
                }

                if (!ETExcelExtensions.VerifyCode("确认是否开始上传，上传时间费时较多，上传期间，无法操作Excel"))
                    return;

                //总数，有效操作数
                int totalCount = 0;
                int successCount = 0;

                inputRange = inputRange.GetVisibleRange();
                if (inputRange.Rows.Count > 1000)
                {
                    ETNotificationHelper.ShowNotification("行数限制为1000行，请缩小范围", true);
                    return;
                }

                //如果行数大于10，则初始化站点信息缓存
                if (inputRange.Rows.Count > 10)
                    await Zn51Helper.InitializeWorksPointCache(true).ConfigureAwait(false);

                foreach (Range row in inputRange.Rows)
                {
                    totalCount++;

                    Range cellWorksPointNo = row.Cells[1, 1];
                    Range cellFilePath = row.Cells[1, 2];
                    Range cellResult = row.Cells[1, 3];

                    if (cellWorksPointNo.IsCellEmpty() || cellFilePath.IsCellEmpty())
                        continue;

                    string worksPointNo = cellWorksPointNo.Value2.ToString();
                    string filePath = cellFilePath.Value2.ToString();
                    if (!File.Exists(filePath))
                    {
                        textBoxLog.WriteLog($"文件{filePath}不存在，跳过上传");
                        cellResult.Value2 = "文件不存在";
                        continue;
                    }

                    var (errorMessage, pointInfo) = await Zn51Helper.UploadDesignCommitmentAndUpdateServer(
                        worksPointNo,
                        filePath).ConfigureAwait(false);
                    cellResult.Value2 = errorMessage ?? "成功";
                    textBoxLog.WriteLog(
                        $"交付编号：{worksPointNo}，交付名称：{pointInfo?.worksPointName ?? "null"}，处理结果：{errorMessage ?? "成功"}");
                    if (errorMessage == null)
                        successCount++;
                }

                textBoxLog.WriteLog($"上传设计交底 全部操作完成，总数{totalCount}，有效操作数{successCount}");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"上传设计交底过程中发生错误: {ex.Message}");
                ETNotificationHelper.ShowNotification($"上传设计交底失败: {ex.Message}", true);
            }
        }

        public async void 上传预算()
        {
            try 
            {
                ETNotificationHelper.ShowNotification("注意：只能选择数据区域，不能包含表头，交付编号为空将跳过。", true);
                Range inputRange = ETExcelExtensions.GetRangeSelection(
                    $"请选择要上传预算的数据区域。{Environment.NewLine}{Environment.NewLine}交付编号\t文件路径\t预算金额(万元)\t主设备预算金额(万元)\t无线配套及服务预算金额(万元)\t上传结果{Environment.NewLine}{Environment.NewLine}注意：能选择数据区域，不能包含表头，交付编号为空将跳过。{Environment.NewLine}",
                    false);
                if (inputRange == null)
                {
                    textBoxLog.WriteLog("未选择数据区域，或者选择区域无效");
                    return;
                }
                if (inputRange.Columns.Count != 6)
                {
                    textBoxLog.WriteLog("选择区域列数不正确，请选择交付编号、文件路径、预算金额(万元)、主设备预算金额(万元)、无线配套及服务预算金额(万元)、上传结果 6列");
                    return;
                }

                if (!ETExcelExtensions.VerifyCode("确认是否开始上传，上传时间费时较多，上传期间，无法操作Excel"))
                    return;

                //总数，有效操作数
                int totalCount = 0;
                int successCount = 0;

                inputRange = inputRange.GetVisibleRange();
                if (inputRange.Rows.Count > 1000)
                {
                    ETNotificationHelper.ShowNotification("行数限制为1000行，请缩小范围", true);
                    return;
                }

                //如果行数大于10，则初始化站点信息缓存
                if (inputRange.Rows.Count > 10)
                    await Zn51Helper.InitializeWorksPointCache(true).ConfigureAwait(false);

                foreach (Range row in inputRange.Rows)
                {
                    totalCount++;

                    Range cellWorksPointNo = row.Cells[1, 1];
                    Range cellFilePath = row.Cells[1, 2];
                    Range cellBudget = row.Cells[1, 3]; //预算金额
                    Range cellMainBudget = row.Cells[1, 4]; //主设备预算金额
                    Range cellSupplementBudget = row.Cells[1, 5];//配套及服务预算金额
                    Range cellResult = row.Cells[1, 6];

                    if (cellWorksPointNo.IsCellEmpty() || cellFilePath.IsCellEmpty())
                        continue;

                    string worksPointNo = cellWorksPointNo.Value2.ToString();
                    string filePath = cellFilePath.Value2.ToString();
                    double budget = 0;
                    double mainBudget = 0;
                    double supplementBudget = 0;

                    if (!double.TryParse(cellBudget.Value2.ToString(), out budget) ||
                        !double.TryParse(cellMainBudget.Value2.ToString(), out mainBudget) ||
                        !double.TryParse(cellSupplementBudget.Value2.ToString(), out supplementBudget))
                    {
                        textBoxLog.WriteLog($"预算金额、主设备预算金额、无线配套及服务预算金额必须为数字，请检查行号：{row.Row}");
                        cellResult.Value2 = $"预算金额、主设备预算金额、无线配套及服务预算金额必须为数字";
                        continue;
                    }

                    if (!File.Exists(filePath))
                    {
                        textBoxLog.WriteLog($"文件{filePath}不存在，跳过上传");
                        cellResult.Value2 = "文件不存在";
                        continue;
                    }

                    var (errorMessage, pointInfo) = await Zn51Helper.UploadDesignBudgetAndUpdateServer(
                        worksPointNo,
                        filePath,
                        budget,
                        mainBudget,
                        supplementBudget).ConfigureAwait(false);
                    cellResult.Value2 = errorMessage ?? "成功";
                    textBoxLog.WriteLog(
                        $"交付编号：{worksPointNo}，交付名称：{pointInfo?.worksPointName ?? "null"}，处理结果：{errorMessage ?? "成功"}");
                    if (errorMessage == null)
                        successCount++;
                }

                textBoxLog.WriteLog($"上传预算 全部操作完成，总数{totalCount}，有效操作数{successCount}");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"上传预算过程中发生错误: {ex.Message}");
                ETNotificationHelper.ShowNotification($"上传预算失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 更新征楼谈点信息
        /// </summary>
        public async void 批量换点()
        {
            try
            {
                ETNotificationHelper.ShowNotification("注意：只能选择数据区域，不能包含表头，交付编号为空将跳过。", true);
                Range inputRange = ETExcelExtensions.GetRangeSelection(
                    $"请选择数据区域。{Environment.NewLine}{Environment.NewLine}",
                    false);

                if (inputRange == null)
                {
                    MessageBox.Show("请先选择数据区域");
                    return;
                }

                int totalCount = 0;
                int successCount = 0;

                if (inputRange.Rows.Count > 1000)
                {
                    ETNotificationHelper.ShowNotification("行数限制为1000行，请缩小范围", true);
                    return;
                }

                //如果行数大于10，则初始化站点信息缓存
                if (inputRange.Rows.Count > 10)
                    await Zn51Helper.InitializeWorksPointCache(true).ConfigureAwait(false);

                foreach (Range row in inputRange.Rows)
                {
                    totalCount++;

                    // 获取单元格数据
                    Range cellWorksPointNo = row.Cells[1, 1];  // 站点编号
                    Range cellTandianqingkuang = row.Cells[1, 2];  // 谈点情况
                    Range cellShifouxuyaohuandian = row.Cells[1, 3];  // 是否需要换点
                    Range cellWuyeshuxing = row.Cells[1, 4];  // 物业属性
                    Range cellShijizhanzhidizhi = row.Cells[1, 5];  // 实际站址地址
                    Range cellShijizhanzhimingcheng = row.Cells[1, 6];  // 实际站址名称
                    Range cellShijizhanzhijingdu = row.Cells[1, 7];  // 实际站址经度
                    Range cellShijizhanzhiweidu = row.Cells[1, 8];  // 实际站址纬度
                    Range cellGuihuazhanzhijingdu = row.Cells[1, 9];  // 规划站址经度
                    Range cellGuihuazhanzhiweidu = row.Cells[1, 10];  // 规划站址纬度
                    Range cellResult = row.Cells[1, 11];  // 结果列

                    // 检查必填项
                    if (cellWorksPointNo.IsCellEmpty())
                        continue;

                    // 获取数据
                    string worksPointNo = cellWorksPointNo.Value2.ToString();
                    string tandianqingkuang = cellTandianqingkuang.IsCellEmpty()
                        ? string.Empty
                        : cellTandianqingkuang.Value2.ToString();
                    string shifouxuyaohuandian = cellShifouxuyaohuandian.IsCellEmpty()
                        ? string.Empty
                        : cellShifouxuyaohuandian.Value2.ToString();
                    string wuyeshuxing = cellWuyeshuxing.IsCellEmpty()
                        ? string.Empty
                        : cellWuyeshuxing.Value2.ToString();
                    string shijizhanzhidizhi = cellShijizhanzhidizhi.IsCellEmpty()
                        ? string.Empty
                        : cellShijizhanzhidizhi.Value2.ToString();
                    string shijizhanzhimingcheng = cellShijizhanzhimingcheng.IsCellEmpty()
                        ? string.Empty
                        : cellShijizhanzhimingcheng.Value2.ToString();
                    string shijizhanzhijingdu = cellShijizhanzhijingdu.IsCellEmpty()
                        ? string.Empty
                        : cellShijizhanzhijingdu.Value2.ToString();
                    string shijizhanzhiweidu = cellShijizhanzhiweidu.IsCellEmpty()
                        ? string.Empty
                        : cellShijizhanzhiweidu.Value2.ToString();
                    string guihuazhanzhijingdu = cellGuihuazhanzhijingdu.IsCellEmpty()
                        ? string.Empty
                        : cellGuihuazhanzhijingdu.Value2.ToString();
                    string guihuazhanzhiweidu = cellGuihuazhanzhiweidu.IsCellEmpty()
                        ? string.Empty
                        : cellGuihuazhanzhiweidu.Value2.ToString();

                    // 调用更新方法
                    var (errorMessage, pointInfo) = await Zn51Helper.UpdateTalkPointInfo(
                        worksPointNo,
                        tandianqingkuang,
                        shifouxuyaohuandian,
                        wuyeshuxing,
                        shijizhanzhidizhi,
                        shijizhanzhimingcheng,
                        shijizhanzhijingdu,
                        shijizhanzhiweidu,
                        guihuazhanzhijingdu,
                        guihuazhanzhiweidu).ConfigureAwait(false);

                    // 更新结果
                    cellResult.Value2 = errorMessage ?? "成功";
                    textBoxLog.WriteLog($"交付编号：{worksPointNo}，结果：{errorMessage ?? "成功"}");
                    if (errorMessage == null)
                        successCount++;

                    // 更新日志
                    textBoxLog.WriteLog(
                        $"交付编号：{worksPointNo}，交付名称：{pointInfo?.worksPointName ?? "null"}，处理结果：{errorMessage ?? "成功"}");
                }

                MessageBox.Show($"处理完成，共{totalCount}条数据，成功{successCount}条");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"批量换点过程中发生错误: {ex.Message}");
                ETNotificationHelper.ShowNotification($"批量换点失败: {ex.Message}", true);
            }
        }

        void 一键审批通过()
        {
            string returnMessage = Zn51Helper.ApproveAll();
        }

        async void 默认衔接表()
        {
            double? junctionCount = ETExcelExtensions.GetDoubleInput("请输入衔接表数量：");
            if (junctionCount == null)
                return;

            try
            {
                // 使用await等待异步操作完成，并添加ConfigureAwait(false)避免死锁
                await Zn51Helper.UploadJunctionTableAndUpdateServerAsync((int)junctionCount, true).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // 处理异步操作中可能出现的异常
                textBoxLog?.WriteLog($"上传衔接表时发生错误: {ex.Message}");
            }
        }

        void 空白表模()
        {
            ETExcelExtensions.SetAppFastMode();

            // 新建一个工作簿，包含3个表
            Workbook workbook = Globals.ThisAddIn.Application.Workbooks.Add();

            // 表1：上传图纸
            Worksheet sheet1 = workbook.Worksheets[1];
            sheet1.Name = "上传图纸";
            sheet1.Cells[1, 1] = "交付编号";
            sheet1.Cells[1, 2] = "文件路径";
            sheet1.Cells[1, 3] = "执行结果";
            sheet1.Cells[1, 20] = " ";
            sheet1.Rows[1].AutoFilter(1, Missing.Value, XlAutoFilterOperator.xlAnd, Missing.Value, true);
            ;
            sheet1.Rows[1].Select();
            Globals.ThisAddIn.Application.ActiveWindow.FreezePanes = true;

            // 表2：上传安全交底
            Worksheet sheet2 = workbook.Worksheets.Add();
            sheet2.Name = "上传安全交底";
            sheet2.Cells[1, 1] = "交付编号";
            sheet2.Cells[1, 2] = "文件路径";
            sheet2.Cells[1, 3] = "执行结果";
            sheet2.Cells[1, 20] = " ";
            sheet2.Rows[1].AutoFilter(1, Missing.Value, XlAutoFilterOperator.xlAnd, Missing.Value, true);
            sheet2.Rows[1].Select();
            Globals.ThisAddIn.Application.ActiveWindow.FreezePanes = true;

            // 表3：上传预算
            Worksheet sheet3 = workbook.Worksheets.Add();
            sheet3.Name = "上传预算";
            sheet3.Cells[1, 1] = "交付编号";
            sheet3.Cells[1, 2] = "文件路径";
            sheet3.Cells[1, 3] = "算金额(万元)";
            sheet3.Cells[1, 4] = "主设备预算金额(万元)";
            sheet3.Cells[1, 5] = "无线配套及服务预金额(万元)";
            sheet3.Cells[1, 6] = "执行结果";
            sheet3.Cells[1, 20] = " ";
            sheet3.Rows[1].AutoFilter(1, Missing.Value, XlAutoFilterOperator.xlAnd, Missing.Value, true);
            sheet3.Rows[1].Select();
            Globals.ThisAddIn.Application.ActiveWindow.FreezePanes = true;

            // 表4：换点情况表
            Worksheet sheet4 = workbook.Worksheets.Add();
            sheet4.Name = "换点情况表";
            sheet4.Cells[1, 1] = "交付编号";
            sheet4.Cells[1, 2] = "谈点情况";
            sheet4.Cells[1, 3] = "是否需要换点";
            sheet4.Cells[1, 4] = "物业属性";
            sheet4.Cells[1, 5] = "实际站址地址";
            sheet4.Cells[1, 6] = "实际站址名称";
            sheet4.Cells[1, 7] = "实际站址经度";
            sheet4.Cells[1, 8] = "实际站址纬度";
            sheet4.Cells[1, 9] = "规划站址经度";
            sheet4.Cells[1, 10] = "规划站址纬度";
            sheet4.Cells[1, 20] = " ";
            sheet4.Rows[1].AutoFilter(1, Missing.Value, XlAutoFilterOperator.xlAnd, Missing.Value, true);
            sheet4.Rows[1].Select();
            Globals.ThisAddIn.Application.ActiveWindow.FreezePanes = true;


            // 设置列宽
            sheet1.Columns.AutoFit();
            sheet2.Columns.AutoFit();
            sheet3.Columns.AutoFit();
            sheet4.Columns.AutoFit();

            // 激活第一个工作表
            sheet1.Activate();

            ETExcelExtensions.SetAppNormalMode(true);

            textBoxLog.WriteLog("已创建空白表模，包含上传图纸、上传安全交底、上传预算、换点情况表 四个工作表。已锁定第一行并设置筛选。");
        }

        public async void 下载任务管理并更新()
        {
            try
            {
                ETExcelExtensions.SetAppFastMode();

                textBoxLog.WriteLog("开始执行任务管理文件下载和更新...");

                // 1. 下载文件
                textBoxLog.WriteLog("步骤1/8: 正在下载任务管理文件...");
                // 设置时间范围：结束时间为当前时间，开始时间为3年前
                DateTime endTime = DateTime.Now;
                DateTime startTime = endTime.AddYears(-1);
                textBoxLog.WriteLog($"设置查询时间范围：{startTime:yyyy-MM-dd} 至 {endTime:yyyy-MM-dd}");

                var (success, filePath) = await Zn51Helper.DownloadTaskManagementFile(null, startTime, endTime).ConfigureAwait(false);
                if (!success)
                {
                    textBoxLog.WriteLog($"下载失败：{filePath}");
                    return;
                }
                textBoxLog.WriteLog($"文件下载成功：{filePath}");

                // 2. 打开下载的文件
                textBoxLog.WriteLog("步骤2/8: 正在打开下载的文件...");
                Workbook downloadedWorkbook = Globals.ThisAddIn.Application.Workbooks.Open(filePath);
                Worksheet downloadedSheet = downloadedWorkbook.Worksheets[1];
                textBoxLog.WriteLog("文件打开成功");

                // 新增：删除不符合条件的行
                textBoxLog.WriteLog("步骤3/8: 正在删除不符合条件的行...");
                Range headerRow = downloadedSheet.Rows[1];

                // 查找"所属模板"列
                Range templateColumn = ETExcelExtensions.FindColumnByHeaderTitle("所属模板", headerRow);
                if (templateColumn == null)
                {
                    textBoxLog.WriteLog("未找到\"所属模板\"列");
                    return;
                }

                // 获取要筛选的范围
                Range rangeToFilter = downloadedSheet.Range[
                    downloadedSheet.Cells[2, templateColumn.Column],
                    downloadedSheet.Cells[downloadedSheet.UsedRange.Rows.Count, templateColumn.Column]
                ];

                // 删除不包含"第八"和"第十"的行
                int totalRows = downloadedSheet.UsedRange.Rows.Count;
                ETExcelExtensions.BatchDeleteRowsByCriteria(
                    rangeToFilter,
                    new[] { "第八", "第十" },
                    false  // false表示删除不包含这些键词的行
                );
                int remainingRows = downloadedSheet.UsedRange.Rows.Count;
                textBoxLog.WriteLog($"行筛选完成：原有{totalRows}行，筛选后保留{remainingRows}行");

                // 原有的步骤序号向后顺延
                textBoxLog.WriteLog("步骤4/8: 正在查找\"提交人组织\"列...");
                // ... (后续代码保持不变，只需更新步骤序号)
                Range organizationColumn = ETExcelExtensions.FindColumnByHeaderTitle("提交人组织", headerRow);
                if (organizationColumn == null)
                {
                    textBoxLog.WriteLog("未找到\"提交人组织\"列");
                    return;
                }
                textBoxLog.WriteLog($"找到\"提交人组织\"列，位于第{organizationColumn.Column}列");

                // 4. 删除不含"设计"字样的行
                textBoxLog.WriteLog("步骤5/8: 正在筛选设计部门数据...");
                rangeToFilter = downloadedSheet.Range[
                    downloadedSheet.Cells[2, organizationColumn.Column],
                    downloadedSheet.Cells[downloadedSheet.UsedRange.Rows.Count, organizationColumn.Column]];

                totalRows = downloadedSheet.UsedRange.Rows.Count;
                ETExcelExtensions.BatchDeleteRowsByCriteria(
                    rangeToFilter,
                    new[] { "设计" },
                    false);
                remainingRows = downloadedSheet.UsedRange.Rows.Count;
                textBoxLog.WriteLog($"数据筛选完成：原有{totalRows}行，筛选后保留{remainingRows}行");

                // 5. 获取汇总目标工作表信息
                textBoxLog.WriteLog("步骤6/8: 正在读取配置信息...");
                string configPath = ETConfig.GetConfigDirectory("config.ini");
                if (!File.Exists(configPath))
                {
                    textBoxLog.WriteLog("未在配置文件中找到目标工作簿路径");
                    return;
                }

                ETIniFile config = new(configPath);
                string targetWorkbookPath = config.GetValue("51Helper", "51任务记录", string.Empty);
                string targetWorksheetName = config.GetValue("51Helper", "51任务记录WorksheetName", "任务记录");

                if (string.IsNullOrEmpty(targetWorkbookPath))
                {
                    textBoxLog.WriteLog("未在配置文件中找到目标工作簿路径");
                    return;
                }
                textBoxLog.WriteLog($"配置读取成功：目标文件 {targetWorkbookPath}，工作表 {targetWorksheetName}");

                // 6. 查找索引列
                textBoxLog.WriteLog("步骤7/8: 正在查找必要索引列...");
                Range deliveryNoColumn = ETExcelExtensions.FindColumnByHeaderTitle("交付编号", headerRow);
                Range submitTimeColumn = ETExcelExtensions.FindColumnByHeaderTitle("提交时间", headerRow);
                if (deliveryNoColumn == null || submitTimeColumn == null)
                {
                    textBoxLog.WriteLog("未找到必要的索引列（交付编号或提交时间）");
                    return;
                }
                textBoxLog.WriteLog($"索引列查找成功：交付编号列={deliveryNoColumn.Column}，提交时间列={submitTimeColumn.Column}");

                // 7. 准备汇总参数
                textBoxLog.WriteLog("步骤8/8: 正在准备汇总参数...");
                List<string> indexColumnNames = ["交付编号", "提交时间"];
                textBoxLog.WriteLog("汇总参数准备完成");

                // 8. 执行汇总
                textBoxLog.WriteLog("开始执行数据汇总...");
                try
                {
                    // 使用HyOpenWorkbook打开目标工作簿
                    Workbook targetWorkbook = ETExcelExtensions.OpenWorkbook(targetWorkbookPath);
                    if (targetWorkbook == null)
                    {
                        textBoxLog.WriteLog("目标文件打开出错，请检查文件路径和文件是否存在。");
                        return;
                    }
                    Worksheet targetSheet = targetWorkbook.Worksheets[targetWorksheetName];

                    // 获取筛选行作为表头行
                    Range targetHeaderRow = targetSheet.GetAutoFilterRow();
                    if (targetHeaderRow == null)
                    {
                        textBoxLog.WriteLog("目标工作表未找到筛选行，请检查目标表格式是否正确");
                        return;
                    }

                    // 使用静态方法执行汇总
                    Range newDataRange = ETExcelDataSummarizer.SummarizeData(
                        headerRow,           // 源表头行
                        targetHeaderRow,     // 目标表头行
                        indexColumnNames,    // 索引列名列表
                        new ETExcelDataSummarizer.SummarizeOptions
                        {
                            SkipExistingData = true,
                            LogTextBox = textBoxLog,  // 传入日志控件以显示进度
                            ShowProgress = true,
                            AppendToEnd = false
                        }
                    );

                    // 如果有新增数据，给第一列标色
                    if (newDataRange != null)
                    {
                        Range firstColumn = newDataRange.Columns[1];
                        firstColumn.Format条件格式警示色(EnumWarningColor.虾红);
                        textBoxLog.WriteLog($"已为新增的 {newDataRange.Rows.Count} 行数据添加标记");
                    }

                    // 将提交时间列设置为时间格式
                    submitTimeColumn.NumberFormat = "yyyy-mm-dd hh:mm:ss";

                    textBoxLog.WriteLog("数据汇总完成");
                }
                catch (Exception ex)
                {
                    textBoxLog.WriteLog($"汇总过程中发生错误：{ex.Message}");
                }

                // 9. 清理，这里暂时不删除下载的临时文件，以便于查看错误信息
                //downloadedWorkbook.Close(false);
                //File.Delete(filePath); // 删除下载的临时文件
                textBoxLog.WriteLog("临时文件清理完成");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"处理过程中发生错误：{ex.Message}");
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
                textBoxLog.WriteLog("任务执行完成");
            }
        }

        public async void 下载交付列表并更新()
        {
            try
            {
                ETExcelExtensions.SetAppFastMode();
                textBoxLog.WriteLog("开始执行交付列表下载和更新...");

                // 1. 下载文件
                textBoxLog.WriteLog("步骤1/7: 正在下载交付列表文件...");
                // 设置时间范围：结束时间为当前时间，开始时间为3年前
                DateTime endTime = DateTime.Now;
                DateTime startTime = endTime.AddYears(-4);
                textBoxLog.WriteLog($"设置查询时间范围：{startTime:yyyy-MM-dd} 至 {endTime:yyyy-MM-dd}");

                var (success, filePath) = await Zn51Helper.DownloadDeliveryListFile(startTime, endTime).ConfigureAwait(false);
                if (!success)
                {
                    textBoxLog.WriteLog($"下载失败：{filePath}");
                    return;
                }
                textBoxLog.WriteLog($"文件下载成功：{filePath}");

                // 2. 打开下载的文件
                textBoxLog.WriteLog("步骤2/7: 正在打开下载的文件...");
                Workbook downloadedWorkbook = Globals.ThisAddIn.Application.Workbooks.Open(filePath);
                Worksheet downloadedSheet = downloadedWorkbook.Worksheets[1];
                textBoxLog.WriteLog("文件打开成功");

                // 3. 删除不符合条件的行
                textBoxLog.WriteLog("步骤3/7: 正在删除不符合条件的行...");
                Range headerRow = downloadedSheet.Rows[1];

                // 查找"所属模板"列
                Range templateColumn = ETExcelExtensions.FindColumnByHeaderTitle("所属模板", headerRow);
                if (templateColumn == null)
                {
                    textBoxLog.WriteLog("未找到\"所属模板\"列");
                    return;
                }

                // 删除不包含"第八"和"第十"的行
                Range rangeToFilter = downloadedSheet.Range[
                    downloadedSheet.Cells[2, templateColumn.Column],
                    downloadedSheet.Cells[downloadedSheet.UsedRange.Rows.Count, templateColumn.Column]
                ];

                int totalRows = downloadedSheet.UsedRange.Rows.Count;
                ETExcelExtensions.BatchDeleteRowsByCriteria(
                    rangeToFilter,
                    new[] { "第八", "第十" },
                    false
                );
                int remainingRows = downloadedSheet.UsedRange.Rows.Count;
                textBoxLog.WriteLog($"行筛选完成：原有{totalRows}行，只保留\"第八\"/ \"第十\"后保留{remainingRows}行");

                // 4. 获取目标工作簿信息
                textBoxLog.WriteLog("步骤4/7: 正在读取配置信息...");
                string targetWorkbookPath = ThisAddIn.ConfigurationSettings.GetValue("51Helper", "51交付列表", string.Empty);
                string targetWorksheetName = ThisAddIn.ConfigurationSettings.GetValue("51Helper", "51交付列表WorksheetName", "51总表及操作记录");

                if (string.IsNullOrEmpty(targetWorkbookPath))
                {
                    textBoxLog.WriteLog("未在配置文件中找到目标工作簿路径");
                    return;
                }
                textBoxLog.WriteLog($"配置读取成功：目标文件 {targetWorkbookPath}，工作表 {targetWorksheetName}");

                // 5. 确定汇总范围
                textBoxLog.WriteLog("步骤5/7: 正在确定汇总范围...");
                string startColumnName = ThisAddIn.ConfigurationSettings.GetValue("51Helper", "51交付列表startColumn", "交付名称");
                string endColumnName = ThisAddIn.ConfigurationSettings.GetValue("51Helper", "51交付列表endColumn", "站址地址");

                Range startColumn = ETExcelExtensions.FindColumnByHeaderTitle(startColumnName, headerRow);
                Range endColumn = ETExcelExtensions.FindColumnByHeaderTitle(endColumnName, headerRow);

                if (startColumn == null || endColumn == null)
                {
                    textBoxLog.WriteLog($"未找到必要的列（{startColumnName}或{endColumnName}）");
                    return;
                }
                textBoxLog.WriteLog($"找到列范围：从{startColumnName}列到{endColumnName}列");

                // 6. 准备汇总参数
                textBoxLog.WriteLog("步骤6/7: 正在准备汇总参数...");
                List<string> indexColumnNames = ["交付名称", "交付编号"];

                // 7. 执行汇总
                textBoxLog.WriteLog("步骤7/7: 开始执行数据汇总...");
                try
                {
                    Workbook targetWorkbook = ETExcelExtensions.OpenWorkbook(targetWorkbookPath);
                    if (targetWorkbook == null)
                    {
                        textBoxLog.WriteLog("目标文件打开出错，请检查文件路径和文件是否存在。");
                        return;
                    }
                    Worksheet targetSheet = targetWorkbook.Worksheets[targetWorksheetName];

                    // 获取筛选行作为表头行
                    Range targetHeaderRow = targetSheet.GetAutoFilterRow();
                    if (targetHeaderRow == null)
                    {
                        textBoxLog.WriteLog("目标工作表未找到筛选行，请检查目标表格式是否正确");
                        return;
                    }


                    // 构建要汇总的列范围
                    Range targetRange = targetSheet.Range[
                        targetHeaderRow.Cells[1, startColumn.Column],
                        targetHeaderRow.Cells[1, endColumn.Column]
                    ];

                    // 执行汇总
                    Range newDataRange = ETExcelDataSummarizer.SummarizeData(
                        headerRow,
                        targetRange,
                        indexColumnNames,
                        new ETExcelDataSummarizer.SummarizeOptions
                        {
                            SkipExistingData = false, // 允许更新已存在的数据
                            LogTextBox = textBoxLog,
                            ShowProgress = true,
                            AppendToEnd = false
                        }
                    );

                    // 如果有新增数据，给第一列标色
                    if (newDataRange != null)
                    {
                        Range firstColumn = newDataRange.Columns[1];
                        firstColumn.Format条件格式警示色(EnumWarningColor.虾红);
                        textBoxLog.WriteLog($"已为新增的 {newDataRange.Rows.Count} 行数据添加标记");
                    }

                    textBoxLog.WriteLog("数据汇总完成");
                }
                catch (Exception ex)
                {
                    textBoxLog.WriteLog($"汇总过程中发生错误：{ex.Message}");
                }

                textBoxLog.WriteLog("临时文件清理完成");
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"处理过程中发生错误：{ex.Message}");
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
                textBoxLog.WriteLog("任务执行完成");
            }
        }

        /// <summary>
        /// 设置存储目录
        /// </summary>
        void 设置存储目录()
        {
            try
            {
                // 获取配置文件路径
                string configPath = ETConfig.GetConfigDirectory("config.ini");
                ETIniFile config = new(configPath);

                // 读取当前保存路径
                string currentSaveFolder = config.GetValue("51Helper", "saveFilePath", string.Empty);

                // 显示输入对话框
                string promptText = "请输入51Helper文件存储目录路径：\n\n注意：\n- 请输入完整的文件夹路径\n- 如果目录不存在，系统将自动创建\n- 建议选择有足够空间的目录";

                string inputPath = ET.Controls.ETInputDialog.Show(
                    promptText,
                    "设置存储目录",
                    currentSaveFolder);

                // 用户取消输入
                if (inputPath == null)
                {
                    textBoxLog.WriteLog("用户取消了目录设置");
                    return;
                }

                // 去除首尾空格
                inputPath = inputPath.Trim();

                // 验证输入是否为空
                if (string.IsNullOrEmpty(inputPath))
                {
                    textBoxLog.WriteLog("输入的路径不能为空");
                    ETNotificationHelper.ShowNotification("输入的路径不能为空，请重新设置", true);
                    return;
                }

                // 验证路径格式是否有效
                try
                {
                    // 尝试获取完整路径，这会验证路径格式
                    string fullPath = Path.GetFullPath(inputPath);

                    // 如果目录不存在，尝试创建
                    if (!Directory.Exists(fullPath))
                    {
                        Directory.CreateDirectory(fullPath);
                        textBoxLog.WriteLog($"目录不存在，已自动创建: {fullPath}");
                    }

                    // 保存到配置文件
                    config.IniWriteValue("51Helper", "saveFilePath", fullPath);

                    textBoxLog.WriteLog($"存储目录已设置为: {fullPath}");
                    textBoxLog.WriteLog("配置已保存到config.ini文件");

                    // 显示成功通知
                    ETNotificationHelper.ShowNotification($"存储目录设置成功！\n路径: {fullPath}", false);
                }
                catch (Exception pathEx)
                {
                    string errorMsg = $"路径格式无效或创建目录失败: {pathEx.Message}";
                    textBoxLog.WriteLog(errorMsg);
                    ETNotificationHelper.ShowNotification($"路径无效：{pathEx.Message}\n请输入有效的文件夹路径", true);
                }
            }
            catch (Exception ex)
            {
                string errorMsg = $"设置存储目录时发生错误: {ex.Message}";
                textBoxLog.WriteLog(errorMsg);
                ETLogManager.Error(errorMsg, ex);
                ETNotificationHelper.ShowNotification(errorMsg, true);
            }
        }
    }
}