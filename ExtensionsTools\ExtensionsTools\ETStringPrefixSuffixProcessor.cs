﻿using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace ET
{
    /// <summary>
    /// 字符串前后缀处理器，提供基于配置文件的字符串前后缀移除功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 从配置文件加载前后缀处理规则
    /// 2. 支持普通字符串匹配和正则表达式匹配
    /// 3. 提供前缀、后缀和前后缀组合处理
    /// 4. 支持配置文件的动态重载
    /// 5. 延迟初始化，提高性能
    ///
    /// 配置文件格式： 配置文件名：StringPrefixSuffix.config 支持的节点：
    /// - [前缀]: 普通前缀匹配规则
    /// - [前缀正则表达式]: 正则表达式前缀匹配规则
    /// - [后缀]: 普通后缀匹配规则
    /// - [后缀正则表达式]: 正则表达式后缀匹配规则
    ///
    /// 使用示例：
    /// <code>
    ///// 移除前缀和后缀
    ///string cleaned = "前缀_内容_后缀".RemovePrefixAndSuffix();
    ///
    ///// 只移除前缀
    ///string result = ETStringPrefixSuffixProcessor.RemovePrefix("前缀_内容");
    ///
    ///// 打开配置文件进行编辑
    ///ETStringPrefixSuffixProcessor.OpenPrefixSuffixConfigFile();
    /// </code>
    /// </remarks>
    public static class ETStringPrefixSuffixProcessor
    {
        /// <summary>
        /// 配置文件名称
        /// </summary>
        private const string CONFIG_FILE_NAME = "StringPrefixSuffix.config";

        /// <summary>
        /// 普通前缀规则列表
        /// </summary>
        private static List<string[]> PrefixRules = [];

        /// <summary>
        /// 正则表达式前缀规则列表
        /// </summary>
        private static List<string[]> PrefixRegexRules = [];

        /// <summary>
        /// 普通后缀规则列表
        /// </summary>
        private static List<string[]> SuffixRules = [];

        /// <summary>
        /// 正则表达式后缀规则列表
        /// </summary>
        private static List<string[]> SuffixRegexRules = [];

        /// <summary>
        /// 所有规则的集合，包含普通和正则表达式的前后缀规则
        /// </summary>
        private static List<string[]> AllRules = [];

        /// <summary>
        /// 是否已初始化配置
        /// </summary>
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化配置，从配置文件中读取前后缀规则
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            try
            {
                string configPath = ETConfig.GetConfigDirectory(CONFIG_FILE_NAME, ".et");
                if (!File.Exists(configPath)) return;

                // 读取配置文件
                LoadRulesFromConfig(configPath);

                // 合并所有规则
                MergeAllRules();

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化ETStringPrefixSuffixProcessor失败: {ex.Message}");
                // 设置为已初始化，避免重复尝试
                _isInitialized = true;
                // 不重新抛出异常，允许程序继续运行
            }
        }

        /// <summary>
        /// 从配置文件加载规则
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        private static void LoadRulesFromConfig(string configPath)
        {
            Dictionary<string, string[]> config = ETConfig.ConfigFileToDictionary(configPath);

            // 清空现有规则
            PrefixRules.Clear();
            PrefixRegexRules.Clear();
            SuffixRules.Clear();
            SuffixRegexRules.Clear();

            // 加载普通前缀规则
            if (config.TryGetValue("前缀", out string[] prefixRules))
            {
                foreach (string rule in prefixRules.Where(r => !string.IsNullOrWhiteSpace(r)))
                {
                    PrefixRules.Add(new[] { rule, string.Empty });
                }
            }

            // 加载正则表达式前缀规则
            if (config.TryGetValue("前缀正则表达式", out string[] prefixRegexRules))
            {
                foreach (string rule in prefixRegexRules.Where(r => !string.IsNullOrWhiteSpace(r)))
                {
                    PrefixRegexRules.Add(new[] { rule, string.Empty });
                }
            }

            // 加载普通后缀规则
            if (config.TryGetValue("后缀", out string[] suffixRules))
            {
                foreach (string rule in suffixRules.Where(r => !string.IsNullOrWhiteSpace(r)))
                {
                    SuffixRules.Add(new[] { rule, string.Empty });
                }
            }

            // 加载正则表达式后缀规则
            if (config.TryGetValue("后缀正则表达式", out string[] suffixRegexRules))
            {
                foreach (string rule in suffixRegexRules.Where(r => !string.IsNullOrWhiteSpace(r)))
                {
                    SuffixRegexRules.Add(new[] { rule, string.Empty });
                }
            }
        }

        /// <summary>
        /// 合并所有规则
        /// </summary>
        private static void MergeAllRules()
        {
            AllRules.Clear();
            AllRules = AllRules.Concat(PrefixRules).ToList();
            AllRules = AllRules.Concat(PrefixRegexRules).ToList();
            AllRules = AllRules.Concat(SuffixRules).ToList();
            AllRules = AllRules.Concat(SuffixRegexRules).ToList();
        }

        /// <summary>
        /// 使用给定的规则列表对字符串进行搜索和替换操作
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="rules">规则列表</param>
        /// <returns>处理后的字符串</returns>
        private static string RemoveByRules(string input, List<string[]> rules)
        {
            if (string.IsNullOrEmpty(input)) return input;
            if (rules == null || rules.Count == 0) return input;

            // 遍历规则列表，每一个规则包含一对模式和替换字符串
            foreach (string[] rule in rules)
            {
                if (rule == null || rule.Length != 2) continue;
                if (string.IsNullOrEmpty(rule[0])) continue;

                // 执行替换操作
                input = ETString.RegexReplace(input, rule[0], string.IsNullOrEmpty(rule[1]) ? string.Empty : rule[1]);
            }

            return input;
        }

        /// <summary>
        /// 移除字符串中的前缀
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="useRegex">是否使用正则表达式规则</param>
        /// <returns>处理后的字符串</returns>
        public static string RemovePrefix(string input, bool useRegex = true)
        {
            if (!_isInitialized)
                Initialize();

            if (string.IsNullOrEmpty(input))
                return input;

            if (useRegex)
                return RemoveByRules(input, PrefixRegexRules);
            else
                return RemoveByRules(input, PrefixRules);
        }

        /// <summary>
        /// 移除字符串中的后缀
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="useRegex">是否使用正则表达式规则</param>
        /// <returns>处理后的字符串</returns>
        public static string RemoveSuffix(string input, bool useRegex = true)
        {
            if (!_isInitialized)
                Initialize();

            if (string.IsNullOrEmpty(input))
                return input;

            if (useRegex)
                return RemoveByRules(input, SuffixRegexRules);
            else
                return RemoveByRules(input, SuffixRules);
        }

        /// <summary>
        /// 移除字符串中的前缀和后缀
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>处理后的字符串</returns>
        public static string RemovePrefixAndSuffix(this string input)
        {
            if (!_isInitialized)
                Initialize();

            if (string.IsNullOrEmpty(input))
                return input;

            return RemoveByRules(input, AllRules);
        }

        /// <summary>
        /// 打开编辑前后缀配置文件
        /// </summary>
        public static void OpenPrefixSuffixConfigFile()
        {
            if (!_isInitialized)
                Initialize();

            ETConfig.OpenConfigFile(CONFIG_FILE_NAME);
        }

        /// <summary>
        /// 重新加载配置文件
        /// </summary>
        public static void ReloadConfig()
        {
            _isInitialized = false;
            Initialize();
        }
    }
}