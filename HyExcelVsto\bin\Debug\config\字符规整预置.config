# ========================================
# 字符规整预置配置文件
# ========================================
#
# 【文件说明】
# 本配置文件用于定义Excel数据处理中的字符替换规则，支持多种类型的数据规整操作。
# 通过预定义的替换规则，可以快速统一数据格式，提高数据处理效率。
#
# 【配置格式】
# [规则组名称(显示名称|默认值)]
# 原始字符|替换字符
# 原始字符|替换字符
# ...
#
# 【格式说明】
# 1. 规则组名称：用方括号[]包围，定义一组相关的替换规则
# 2. 显示名称：括号内第一个参数，用于界面显示的友好名称
# 3. 默认值：括号内第二个参数，用竖线|分隔，表示默认的替换示例
# 4. 替换规则：每行一个，格式为"原始字符|替换字符"
# 5. 空行：用于分隔不同的规则组
# 6. 注释：以#开头的行为注释，程序会忽略
#
# 【使用示例】
# [示例规则组(测试替换|A|B)]
# 旧值1|新值1
# 旧值2|新值2
# 旧值3|新值3
#
# 【注意事项】
# 1. 所有替换都是精确匹配，区分大小写
# 2. 替换顺序按照配置文件中的顺序执行
# 3. 如果同一个原始字符在多个规则中出现，以最后一个为准
# 4. 竖线"|"是分隔符，如需在替换内容中使用，请使用转义符
# 5. 建议按照业务逻辑对规则进行分组，便于维护
#
# ========================================
# 预置规则配置
# ========================================

[频段转换(3.5G|100M)]
3.5G|100M
2.1G|20M
800M|15M

[行政区替换(榕城|榕城区)]
榕城|榕城区
城区|榕城区
市区|榕城区
普宁|普宁市
揭西|揭西县
空港|空港经济区
惠来|惠来县
揭东|揭东区

[厂家替换(NR城区|中兴)]
城区|中兴
市区|中兴
榕城|中兴
普宁|爱立信
揭西|中兴
空港|中兴
惠来|中兴
揭东|中兴
榕城区|中兴
普宁市|爱立信
空港经济区|中兴
惠来县|中兴
揭东区|中兴
揭东县|中兴
揭西县|中兴

[厂家替换(LTE城区|中兴)]
城区|中兴
市区|中兴
榕城|中兴
普宁|华为
揭西|华为
空港|中兴
惠来|华为
揭东|中兴
榕城区|中兴
普宁市|华为
空港经济区|中兴
惠来县|华为
揭东区|中兴
揭东县|中兴
揭西县|华为

[后缀转换(2100M|2.1G)]
2100M|2.1G
3500M|3.5G
1800M|1.8G
