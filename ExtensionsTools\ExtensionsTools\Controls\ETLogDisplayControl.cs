using System;
using System.Drawing;
using System.Windows.Forms;
using ET;

namespace ET.Controls
{
    /// <summary>
    /// ET库通用日志显示控件 增强功能：统一ETLogManager集成、多级别日志过滤、线程安全UI更新、可自定义样式
    /// </summary>
    public partial class ETLogDisplayControl : UserControl
    {
        #region 私有字段

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 日志级别过滤设置
        /// </summary>
        private LogLevel _currentLogLevel = LogLevel.Info;

        /// <summary>
        /// 最大日志行数限制
        /// </summary>
        private int _maxLogLines = 1000;

        /// <summary>
        /// 当前日志行数
        /// </summary>
        private int _currentLogLines = 0;

        /// <summary>
        /// 是否显示初始化消息
        /// </summary>
        private bool _showInitialMessage = true;

        /// <summary>
        /// 自定义初始化消息
        /// </summary>
        private string _customInitialMessage = null;

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 当前日志级别过滤设置
        /// </summary>
        /// <remarks>只显示等于或高于此级别的日志</remarks>
        public LogLevel CurrentLogLevel
        {
            get => _currentLogLevel;
            set
            {
                _currentLogLevel = value;
                ETLogManager.Debug(this, $"日志级别设置为：{value}");
            }
        }

        /// <summary>
        /// 最大日志行数限制
        /// </summary>
        /// <remarks>超过此行数时会自动清理旧日志</remarks>
        public int MaxLogLines
        {
            get => _maxLogLines;
            set
            {
                _maxLogLines = Math.Max(100, value); // 最少保留100行
                ETLogManager.Debug(this, $"最大日志行数设置为：{_maxLogLines}");
            }
        }

        /// <summary>
        /// 是否自动滚动到底部
        /// </summary>
        public bool AutoScrollToBottom { get; set; } = true;

        /// <summary>
        /// 是否显示时间戳
        /// </summary>
        public bool ShowTimestamp { get; set; } = true;

        /// <summary>
        /// 是否显示日志级别
        /// </summary>
        public bool ShowLogLevel { get; set; } = true;

        /// <summary>
        /// 是否显示初始化消息
        /// </summary>
        /// <remarks>设置为false可以创建空白的日志显示控件</remarks>
        public bool ShowInitialMessage
        {
            get => _showInitialMessage;
            set
            {
                _showInitialMessage = value;
                if (!value && textBoxLog != null)
                {
                    Clear();
                }
            }
        }

        /// <summary>
        /// 自定义初始化消息
        /// </summary>
        /// <remarks>如果设置了此属性，将使用自定义消息替代默认的初始化消息</remarks>
        public string CustomInitialMessage
        {
            get => _customInitialMessage;
            set
            {
                _customInitialMessage = value;
                if (textBoxLog != null && _showInitialMessage)
                {
                    InitializeLogDisplay();
                }
            }
        }

        /// <summary>
        /// 日志文本框的字体
        /// </summary>
        public Font LogFont
        {
            get => textBoxLog?.Font;
            set => SetLogFont(value);
        }

        /// <summary>
        /// 日志文本框的背景色
        /// </summary>
        public Color LogBackColor
        {
            get => textBoxLog?.BackColor ?? Color.White;
            set => SetLogBackColor(value);
        }

        /// <summary>
        /// 日志文本框的前景色
        /// </summary>
        public Color LogForeColor
        {
            get => textBoxLog?.ForeColor ?? Color.Black;
            set => SetLogForeColor(value);
        }

        #endregion 公共属性

        #region 日志级别枚举

        /// <summary>
        /// 日志级别枚举
        /// </summary>
        public enum LogLevel
        {
            /// <summary>
            /// 调试
            /// </summary>
            Debug = 0,

            /// <summary>
            /// 信息
            /// </summary>
            Info = 1,

            /// <summary>
            /// 警告
            /// </summary>
            Warning = 2,

            /// <summary>
            /// 错误
            /// </summary>
            Error = 3
        }

        #endregion 日志级别枚举

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETLogDisplayControl()
        {
            InitializeComponent();
            InitializeLogDisplay();
            ETLogManager.Info(this, "ETLogDisplayControl控件初始化完成");
        }

        /// <summary>
        /// 构造函数（带自定义初始化消息）
        /// </summary>
        /// <param name="customInitialMessage">自定义初始化消息</param>
        public ETLogDisplayControl(string customInitialMessage) : this()
        {
            _customInitialMessage = customInitialMessage;
            InitializeLogDisplay();
        }

        /// <summary>
        /// 构造函数（可选择是否显示初始化消息）
        /// </summary>
        /// <param name="showInitialMessage">是否显示初始化消息</param>
        public ETLogDisplayControl(bool showInitialMessage) : this()
        {
            _showInitialMessage = showInitialMessage;
            if (!showInitialMessage)
            {
                Clear();
            }
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 写入信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void WriteInfo(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        /// <summary>
        /// 写入警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void WriteWarning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        /// <summary>
        /// 写入错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void WriteError(string message, Exception exception = null)
        {
            string fullMessage = exception != null
                ? $"{message} - 异常：{exception.Message}"
                : message;
            WriteLog(LogLevel.Error, fullMessage);
        }

        /// <summary>
        /// 写入调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void WriteDebug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// 写入指定级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        public void WriteLog(LogLevel level, string message)
        {
            // 检查日志级别过滤
            if (level < _currentLogLevel)
            {
                return;
            }

            try
            {
                // 格式化日志消息
                string formattedMessage = FormatLogMessage(level, message);

                // 线程安全的UI更新
                if (textBoxLog.InvokeRequired)
                {
                    textBoxLog.Invoke(new Action(() => AppendLogMessage(formattedMessage)));
                }
                else
                {
                    AppendLogMessage(formattedMessage);
                }

                // 同时写入ETLogManager
                WriteToETLogManager(level, message);
            }
            catch (Exception ex)
            {
                // 日志写入失败时的备用处理
                try
                {
                    textBoxLog.AppendText($"{Environment.NewLine}[ERROR] 日志写入失败：{ex.Message}");
                }
                catch
                {
                    // 忽略备用处理的异常
                }
            }
        }

        /// <summary>
        /// 清空日志显示
        /// </summary>
        public void Clear()
        {
            try
            {
                if (textBoxLog.InvokeRequired)
                {
                    textBoxLog.Invoke(new Action(() =>
                    {
                        textBoxLog.Clear();
                        _currentLogLines = 0;
                    }));
                }
                else
                {
                    textBoxLog.Clear();
                    _currentLogLines = 0;
                }

                ETLogManager.Debug(this, "日志显示已清空");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "清空日志显示失败", ex);
            }
        }

        /// <summary>
        /// 获取日志文本框引用
        /// </summary>
        /// <returns>内部TextBox控件</returns>
        /// <remarks>用于兼容原版本的扩展方法</remarks>
        public TextBox GetTextBox()
        {
            return textBoxLog;
        }

        /// <summary>
        /// 设置日志文本框的字体
        /// </summary>
        /// <param name="font">字体</param>
        public void SetLogFont(Font font)
        {
            try
            {
                if (textBoxLog != null && font != null)
                {
                    if (textBoxLog.InvokeRequired)
                    {
                        textBoxLog.Invoke(new Action(() => textBoxLog.Font = font));
                    }
                    else
                    {
                        textBoxLog.Font = font;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置日志字体失败", ex);
            }
        }

        /// <summary>
        /// 设置日志文本框的背景色
        /// </summary>
        /// <param name="color">背景色</param>
        public void SetLogBackColor(Color color)
        {
            try
            {
                if (textBoxLog != null)
                {
                    if (textBoxLog.InvokeRequired)
                    {
                        textBoxLog.Invoke(new Action(() => textBoxLog.BackColor = color));
                    }
                    else
                    {
                        textBoxLog.BackColor = color;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置日志背景色失败", ex);
            }
        }

        /// <summary>
        /// 设置日志文本框的前景色
        /// </summary>
        /// <param name="color">前景色</param>
        public void SetLogForeColor(Color color)
        {
            try
            {
                if (textBoxLog != null)
                {
                    if (textBoxLog.InvokeRequired)
                    {
                        textBoxLog.Invoke(new Action(() => textBoxLog.ForeColor = color));
                    }
                    else
                    {
                        textBoxLog.ForeColor = color;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置日志前景色失败", ex);
            }
        }

        /// <summary>
        /// 获取当前日志内容
        /// </summary>
        /// <returns>日志内容</returns>
        public string GetLogContent()
        {
            try
            {
                if (textBoxLog?.InvokeRequired == true)
                {
                    return (string)textBoxLog.Invoke(new Func<string>(() => textBoxLog.Text));
                }
                return textBoxLog?.Text ?? string.Empty;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "获取日志内容失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 保存日志到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        public bool SaveLogToFile(string filePath)
        {
            try
            {
                string content = GetLogContent();
                System.IO.File.WriteAllText(filePath, content, System.Text.Encoding.UTF8);
                ETLogManager.Info(this, $"日志已保存到文件：{filePath}");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"保存日志到文件失败：{filePath}", ex);
                return false;
            }
        }

        /// <summary>
        /// 设置日志显示的初始化消息
        /// </summary>
        /// <param name="message">初始化消息</param>
        /// <param name="clearExisting">是否清空现有内容</param>
        public void SetInitialMessage(string message, bool clearExisting = true)
        {
            try
            {
                if (clearExisting)
                {
                    Clear();
                }

                if (!string.IsNullOrEmpty(message))
                {
                    if (textBoxLog.InvokeRequired)
                    {
                        textBoxLog.Invoke(new Action(() =>
                        {
                            textBoxLog.Text = message;
                            _currentLogLines = message.Split(new[] { Environment.NewLine, "\r\n", "\n" }, StringSplitOptions.None).Length;
                        }));
                    }
                    else
                    {
                        textBoxLog.Text = message;
                        _currentLogLines = message.Split(new[] { Environment.NewLine, "\r\n", "\n" }, StringSplitOptions.None).Length;
                    }
                }

                ETLogManager.Debug(this, "设置初始化消息完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置初始化消息失败", ex);
            }
        }

        #endregion 公共方法

        #region 私有方法

        /// <summary>
        /// 初始化日志显示
        /// </summary>
        private void InitializeLogDisplay()
        {
            try
            {
                if (!_showInitialMessage)
                {
                    return;
                }

                // 使用自定义初始化消息或默认消息
                string initialMessage = _customInitialMessage ?? "";

                textBoxLog.Text = initialMessage;
                _currentLogLines = initialMessage.Split(new[] { Environment.NewLine, "\r\n", "\n" }, StringSplitOptions.None).Length;

                // 注册ETLogManager的TextBox输出
                RegisterETLogManager();

                ETLogManager.Debug(this, "日志显示初始化完成");
            }
            catch (Exception ex)
            {
                // 初始化失败时使用基本错误处理
                textBoxLog.Text = $"日志初始化失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 注册ETLogManager的TextBox输出
        /// </summary>
        private void RegisterETLogManager()
        {
            try
            {
                // 这里可以扩展ETLogManager的TextBox注册功能 当前版本直接使用WriteLog方法进行日志输出
                ETLogManager.Debug(this, "ETLogManager注册完成");
            }
            catch (Exception ex)
            {
                textBoxLog.AppendText($"{Environment.NewLine}注册ETLogManager失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 格式化日志消息
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">原始消息</param>
        /// <returns>格式化后的消息</returns>
        private string FormatLogMessage(LogLevel level, string message)
        {
            var parts = new System.Collections.Generic.List<string>();

            if (ShowTimestamp)
            {
                parts.Add(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }

            if (ShowLogLevel)
            {
                parts.Add($"[{level.ToString().ToUpper()}]");
            }

            parts.Add(message);

            return string.Join(" ", parts);
        }

        /// <summary>
        /// 追加日志消息到文本框
        /// </summary>
        /// <param name="formattedMessage">格式化后的消息</param>
        private void AppendLogMessage(string formattedMessage)
        {
            try
            {
                // 检查是否需要清理旧日志
                if (_currentLogLines >= _maxLogLines)
                {
                    ClearOldLogs();
                }

                textBoxLog.AppendText($"{Environment.NewLine}{formattedMessage}");
                _currentLogLines++;

                // 自动滚动到底部
                if (AutoScrollToBottom)
                {
                    textBoxLog.SelectionStart = textBoxLog.Text.Length;
                    textBoxLog.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                // 忽略UI更新异常
                System.Diagnostics.Debug.WriteLine($"追加日志消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清理旧日志（保留最近的一半）
        /// </summary>
        private void ClearOldLogs()
        {
            try
            {
                string[] lines = textBoxLog.Text.Split(new[] { Environment.NewLine, "\r\n", "\n" }, StringSplitOptions.None);
                int keepLines = _maxLogLines / 2;

                if (lines.Length > keepLines)
                {
                    string[] newLines = new string[keepLines];
                    Array.Copy(lines, lines.Length - keepLines, newLines, 0, keepLines);
                    textBoxLog.Text = string.Join(Environment.NewLine, newLines);
                    _currentLogLines = keepLines;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "清理旧日志失败", ex);
            }
        }

        /// <summary>
        /// 写入到ETLogManager
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息</param>
        private void WriteToETLogManager(LogLevel level, string message)
        {
            try
            {
                switch (level)
                {
                    case LogLevel.Debug:
                        ETLogManager.Debug(this, message);
                        break;

                    case LogLevel.Info:
                        ETLogManager.Info(this, message);
                        break;

                    case LogLevel.Warning:
                        ETLogManager.Warning(this, message);
                        break;

                    case LogLevel.Error:
                        ETLogManager.Error(this, message);
                        break;
                }
            }
            catch
            {
                // 忽略ETLogManager写入异常
            }
        }

        #endregion 私有方法

        #region 资源释放

        // Dispose方法在Designer.cs中实现

        #endregion 资源释放
    }
}