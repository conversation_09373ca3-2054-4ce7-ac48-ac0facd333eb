using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ET.AI.Services.Core
{
    /// <summary>
    /// AI JSON处理器，负责处理JSON相关操作
    /// </summary>
    /// <remarks>
    /// 该类主要负责：
    /// 1. 构造AI请求的JSON格式
    /// 2. 解析AI响应的JSON内容
    /// 3. 处理JSON格式的错误和重试
    /// 4. 规范化JSON数据结构
    /// </remarks>
    class AIJsonHelper
    {
        /// <summary>
        /// 构造请求JSON
        /// </summary>
        /// <param name="questionList">问题列表（JSON数组）</param>
        /// <param name="rulesFilePath">规则文件路径</param>
        /// <returns>构造好的请求JSON对象</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 检查规则文件是否存在
        /// 2. 尝试在配置目录中查找规则文件
        /// 3. 读取规则文件内容
        /// 4. 构造标准格式的请求JSON
        /// </remarks>
        public static JObject ConstructRequestJson(JArray questionList, string rulesFilePath)
        {
            string rules = null;
            string fullPath = rulesFilePath;

            if (!File.Exists(fullPath))
            {
                fullPath = ETConfig.GetConfigPath(Path.GetFileName(rulesFilePath), "AiModel");
            }

            if (File.Exists(fullPath))
            {
                rules = File.ReadAllText(fullPath);
            }

            return new JObject
            {
                ["需求"] = "根据问题列表中的已知信息，按回答问题规则思考并输出回答结果",
                ["问题列表"] = questionList,
                ["回答问题规则"] = rules
            };
        }

        /// <summary>
        /// 尝试解析JSON响应
        /// </summary>
        /// <param name="responseContent">响应内容</param>
        /// <param name="retryAction">重试操作的委托</param>
        /// <param name="retryCount">当前重试次数</param>
        /// <returns>解析后的JSON对象</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 处理被Markdown代码块包围的JSON
        /// 2. 尝试解析不同格式的JSON响应
        /// 3. 在解析失败时进行重试
        /// 4. 标准化响应格式
        /// 
        /// 支持的响应格式：
        /// - JSON数组：包装为 {"list": [...]}
        /// - JSON对象：直接返回
        /// - 其他JSON值：包装为 {"content": value}
        /// </remarks>
        /// <exception cref="JsonReaderException">当JSON解析失败且超过重试次数时抛出</exception>
        public static async Task<JObject> TryParseJsonResponse(string responseContent, Func<Task<string>> retryAction, int retryCount = 0)
        {
            try
            {
                // 处理被```json ```包围的情况
                if (responseContent.Contains("```"))
                {
                    // 查找 ```json 和 ``` 之间的内容
                    int startIndex = responseContent.IndexOf("```json", StringComparison.OrdinalIgnoreCase);
                    if (startIndex >= 0)
                    {
                        startIndex += "```json".Length;
                        int endIndex = responseContent.IndexOf("```", startIndex);
                        if (endIndex > startIndex)
                        {
                            responseContent = responseContent.Substring(startIndex, endIndex - startIndex).Trim();
                        }
                    }
                }

                // 尝试解析JSON
                JToken token = JToken.Parse(responseContent);

                // 检查是否是 ChatCompletion 结构
                if (token is JObject jsonObj && jsonObj.ContainsKey("ChatCompletion"))
                {
                    token = jsonObj["ChatCompletion"];
                }

                // 根据不同类型返回适当的结果
                return token switch
                {
                    JArray array => new JObject { ["list"] = array },
                    JObject obj => obj,
                    _ => new JObject { ["content"] = token }
                };
            }
            catch (JsonReaderException)
            {
                // 如果有重试操作且未达到最大重试次数，则重试
                if (retryCount < 3 && retryAction != null)
                {
                    string newResponse = await retryAction();
                    return await TryParseJsonResponse(newResponse, retryAction, retryCount + 1);
                }
                throw;
            }
        }
    }
}