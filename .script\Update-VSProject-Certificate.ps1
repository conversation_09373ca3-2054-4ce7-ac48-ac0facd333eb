# Visual Studio 项目证书配置更新脚本
# 自动更新项目文件以使用新生成的证书

param(
    [string]$ProjectPath = "HyExcelVsto\HyExcelVsto.csproj",
    [string]$CertificateFile = "",
    [string]$CertificateThumbprint = "",
    [switch]$EnableSigning = $true,
    [switch]$BackupProject = $true
)

Write-Host "=== Visual Studio 项目证书配置更新 ===" -ForegroundColor Cyan

# 检查项目文件
if (-not (Test-Path $ProjectPath)) {
    Write-Error "项目文件不存在: $ProjectPath"
    exit 1
}

# 备份项目文件
if ($BackupProject) {
    $backupPath = "$ProjectPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $ProjectPath $backupPath
    Write-Host "已备份项目文件: $backupPath" -ForegroundColor Green
}

try {
    Write-Host "`n=== 读取项目配置 ===" -ForegroundColor Green
    
    # 读取项目文件内容
    $content = Get-Content $ProjectPath -Raw
    $originalContent = $content
    
    # 分析当前配置
    $currentSigning = if ($content -match '<SignManifests>(.*?)</SignManifests>') { $matches[1] } else { "未配置" }
    $currentCertFile = if ($content -match '<ManifestKeyFile>(.*?)</ManifestKeyFile>') { $matches[1] } else { "未配置" }
    $currentThumbprint = if ($content -match '<ManifestCertificateThumbprint>(.*?)</ManifestCertificateThumbprint>') { $matches[1] } else { "未配置" }
    
    Write-Host "当前签名状态: $currentSigning" -ForegroundColor Yellow
    Write-Host "当前证书文件: $currentCertFile" -ForegroundColor Yellow
    Write-Host "当前证书指纹: $currentThumbprint" -ForegroundColor Yellow
    
    Write-Host "`n=== 更新项目配置 ===" -ForegroundColor Green
    
    # 1. 更新签名设置
    if ($EnableSigning) {
        if ($content -match '<SignManifests>.*?</SignManifests>') {
            $content = $content -replace '<SignManifests>.*?</SignManifests>', '<SignManifests>true</SignManifests>'
            Write-Host "✓ 已启用清单签名" -ForegroundColor Green
        }
        else {
            # 添加签名配置
            $signingConfig = @"
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
"@
            # 在第一个PropertyGroup后插入
            $content = $content -replace '(</PropertyGroup>)', "`$1`r`n$signingConfig"
            Write-Host "✓ 已添加签名配置" -ForegroundColor Green
        }
    }
    else {
        $content = $content -replace '<SignManifests>.*?</SignManifests>', '<SignManifests>false</SignManifests>'
        Write-Host "✓ 已禁用清单签名" -ForegroundColor Green
    }
    
    # 2. 更新证书文件路径
    if ($CertificateFile -ne "") {
        $certFileName = Split-Path $CertificateFile -Leaf
        
        if ($content -match '<ManifestKeyFile>.*?</ManifestKeyFile>') {
            $content = $content -replace '<ManifestKeyFile>.*?</ManifestKeyFile>', "<ManifestKeyFile>$certFileName</ManifestKeyFile>"
            Write-Host "✓ 已更新证书文件路径: $certFileName" -ForegroundColor Green
        }
        else {
            # 添加证书文件配置
            $certFileConfig = @"
  <PropertyGroup>
    <ManifestKeyFile>$certFileName</ManifestKeyFile>
  </PropertyGroup>
"@
            $content = $content -replace '(<SignManifests>true</SignManifests>\s*</PropertyGroup>)', "`$1`r`n$certFileConfig"
            Write-Host "✓ 已添加证书文件配置: $certFileName" -ForegroundColor Green
        }
        
        # 确保证书文件在项目目录中
        $projectDir = Split-Path $ProjectPath -Parent
        $targetCertPath = Join-Path $projectDir $certFileName
        
        if ($CertificateFile -ne $targetCertPath -and (Test-Path $CertificateFile)) {
            Copy-Item $CertificateFile $targetCertPath -Force
            Write-Host "✓ 已复制证书文件到项目目录" -ForegroundColor Green
        }
    }
    
    # 3. 更新证书指纹
    if ($CertificateThumbprint -ne "") {
        if ($content -match '<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>') {
            $content = $content -replace '<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>', "<ManifestCertificateThumbprint>$CertificateThumbprint</ManifestCertificateThumbprint>"
            Write-Host "✓ 已更新证书指纹" -ForegroundColor Green
        }
        else {
            # 添加证书指纹配置
            $thumbprintConfig = @"
  <PropertyGroup>
    <ManifestCertificateThumbprint>$CertificateThumbprint</ManifestCertificateThumbprint>
  </PropertyGroup>
"@
            $content = $content -replace '(<ManifestKeyFile>.*?</ManifestKeyFile>\s*</PropertyGroup>)', "`$1`r`n$thumbprintConfig"
            Write-Host "✓ 已添加证书指纹配置" -ForegroundColor Green
        }
    }
    
    # 4. 清理注释掉的配置
    $content = $content -replace '<!--\s*<PropertyGroup>\s*<ManifestKeyFile>.*?</ManifestKeyFile>\s*</PropertyGroup>\s*-->', ''
    $content = $content -replace '<!--\s*<PropertyGroup>\s*<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>\s*</PropertyGroup>\s*-->', ''
    
    # 5. 保存更新的项目文件
    if ($content -ne $originalContent) {
        $content | Set-Content $ProjectPath -Encoding UTF8
        Write-Host "`n✓ 项目文件已更新" -ForegroundColor Green
    }
    else {
        Write-Host "`n- 项目文件无需更新" -ForegroundColor Yellow
    }
    
    Write-Host "`n=== 验证更新结果 ===" -ForegroundColor Green
    
    # 重新读取并验证
    $updatedContent = Get-Content $ProjectPath -Raw
    $newSigning = if ($updatedContent -match '<SignManifests>(.*?)</SignManifests>') { $matches[1] } else { "未配置" }
    $newCertFile = if ($updatedContent -match '<ManifestKeyFile>(.*?)</ManifestKeyFile>') { $matches[1] } else { "未配置" }
    $newThumbprint = if ($updatedContent -match '<ManifestCertificateThumbprint>(.*?)</ManifestCertificateThumbprint>') { $matches[1] } else { "未配置" }
    
    Write-Host "更新后签名状态: $newSigning" -ForegroundColor Cyan
    Write-Host "更新后证书文件: $newCertFile" -ForegroundColor Cyan
    Write-Host "更新后证书指纹: $newThumbprint" -ForegroundColor Cyan
    
    # 检查证书文件是否存在
    if ($newCertFile -ne "未配置") {
        $projectDir = Split-Path $ProjectPath -Parent
        $certPath = Join-Path $projectDir $newCertFile
        
        if (Test-Path $certPath) {
            $certSize = (Get-Item $certPath).Length
            Write-Host "✓ 证书文件存在: $certPath ($certSize 字节)" -ForegroundColor Green
        }
        else {
            Write-Warning "证书文件不存在: $certPath"
        }
    }
    
    Write-Host "`n=== 配置完成 ===" -ForegroundColor Cyan
    Write-Host "下一步操作：" -ForegroundColor Yellow
    Write-Host "1. 在Visual Studio中重新加载项目" -ForegroundColor White
    Write-Host "2. 生成 → 清理解决方案" -ForegroundColor White
    Write-Host "3. 生成 → 重新生成解决方案" -ForegroundColor White
    Write-Host "4. 发布项目并测试安装程序" -ForegroundColor White
    
}
catch {
    Write-Error "更新项目配置失败: $($_.Exception.Message)"
    
    # 恢复备份
    if ($BackupProject -and (Test-Path $backupPath)) {
        Write-Host "正在恢复备份..." -ForegroundColor Yellow
        Copy-Item $backupPath $ProjectPath -Force
        Write-Host "已恢复原始项目文件" -ForegroundColor Green
    }
}

Write-Host "`n=== 使用示例 ===" -ForegroundColor Cyan
Write-Host "更新证书文件: .\Update-VSProject-Certificate.ps1 -CertificateFile 'path\to\cert.pfx'" -ForegroundColor Gray
Write-Host "禁用签名: .\Update-VSProject-Certificate.ps1 -EnableSigning:`$false" -ForegroundColor Gray
