/*
 * ========================================
 * ETAIv2 - Excel AI辅助工具库 v2.0
 * ========================================
 *
 * 文件名: AIRequestModels.cs
 * 描述: AI请求相关的数据模型定义，包含请求结构、模型配置、API选项等
 * 作者: ETAIv2开发团队
 * 创建时间: 2024
 * 版本: 2.0.0
 *
 * 主要功能:
 * - 定义AI请求的完整数据结构
 * - 定义AI模型配置信息（兼容现有.ai文件格式）
 * - 定义不同API类型的特定选项
 * - 提供配置验证和默认值设置
 *
 * 兼容性:
 * - 完全兼容现有的.ai配置文件格式
 * - 支持OpenAI Chat Completions和Assistants双API
 *
 * ========================================
 */

using System;
using System.Collections.Generic;

namespace ExtensionsTools.ETAIv2.Models
{
    /// <summary>
    /// AI请求模型
    /// </summary>
    public class AIRequest
    {
        public string RequestId { get; set; }               // 请求ID
        public string GlobalPrompt { get; set; }            // 全局提示词
        public List<AIDataGroup> DataGroups { get; set; }   // 数据组
        public AIModelConfig ModelConfig { get; set; }      // 模型配置
        public APIType ApiType { get; set; }                // API类型
        public DateTime CreatedAt { get; set; }             // 创建时间

        public AIRequest()
        {
            RequestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            DataGroups = new List<AIDataGroup>();
            CreatedAt = DateTime.Now;
            ApiType = APIType.ChatCompletion;
        }
    }

    /// <summary>
    /// API类型
    /// </summary>
    public enum APIType
    {
        ChatCompletion,     // Chat Completions API
        Assistant          // Assistants API
    }

    /// <summary>
    /// AI模型配置（兼容现有.ai文件格式）
    /// </summary>
    public class AIModelConfig
    {
        public string Server { get; set; }                  // 服务器类型
        public string Model { get; set; }                   // 模型名称
        public string BaseURL { get; set; }                 // API基础URL
        public string APIKey { get; set; }                  // API密钥
        public string ProxyHost { get; set; }               // 代理主机
        public int ProxyPort { get; set; }                  // 代理端口
        public int RequestTimeout { get; set; } = 30;       // 请求超时
        public float TopP { get; set; } = 0.9f;             // TopP参数
        public float Temperature { get; set; } = 0.7f;      // 温度参数
        public string ResponseFormatType { get; set; }      // 响应格式类型
        public int BaseGroupSize { get; set; } = 10;        // 基础组大小
        public int MaxJsonRetryCount { get; set; } = 3;     // JSON重试次数
        public int MaxConcurrentRequests { get; set; } = 3; // 最大并发请求
        public int MaxRequestsPerMinute { get; set; } = 60; // 每分钟最大请求
        public int TotalMaxRequests { get; set; } = 1000;   // 总最大请求
        public string SystemContent { get; set; }           // 系统内容

        /// <summary>
        /// 是否为OpenAI兼容的服务器
        /// </summary>
        public bool IsOpenAICompatible =>
            string.Equals(Server, "openai", StringComparison.OrdinalIgnoreCase) ||
            (BaseURL != null && BaseURL.ToLower().Contains("openai.com"));

        /// <summary>
        /// 获取有效的API密钥
        /// </summary>
        public string GetEffectiveApiKey()
        {
            if (!string.IsNullOrEmpty(APIKey))
                return APIKey;
            
            // 尝试从环境变量获取
            return Environment.GetEnvironmentVariable("OPENAI_API_KEY");
        }

        /// <summary>
        /// 获取有效的基础URL
        /// </summary>
        /// <remarks>
        /// 不提供默认值，端点路径完全由配置文件中的字段确定
        /// 这样可以避免端点配置错误导致的404问题
        /// </remarks>
        public string GetEffectiveBaseUrl()
        {
            // 直接返回配置的BaseURL，不提供默认值
            // 如果为空，调用方需要处理这种情况
            return BaseURL;
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Model) && 
                   !string.IsNullOrEmpty(GetEffectiveApiKey());
        }
    }

    /// <summary>
    /// Chat API特定选项
    /// </summary>
    public class ChatAPIOptions
    {
        public int MaxTokens { get; set; } = 2000;          // 最大令牌数
        public bool UseStructuredOutput { get; set; } = true; // 使用结构化输出
        public string JsonSchema { get; set; }              // JSON Schema定义
        public List<string> StopSequences { get; set; }     // 停止序列

        public ChatAPIOptions()
        {
            StopSequences = new List<string>();
        }
    }

    /// <summary>
    /// Assistant API特定选项
    /// </summary>
    public class AssistantAPIOptions
    {
        public string AssistantId { get; set; }             // 助手ID（如果使用现有助手）
        public List<string> Tools { get; set; }             // 工具列表
        public bool UseFileSearch { get; set; } = true;     // 使用文件搜索
        public bool UseCodeInterpreter { get; set; } = false; // 使用代码解释器
        public string Instructions { get; set; }            // 助手指令

        public AssistantAPIOptions()
        {
            Tools = new List<string>();
        }
    }
}
