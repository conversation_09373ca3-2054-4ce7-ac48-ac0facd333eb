﻿using ET;
using ET.ETLoginWebBrowser;
using ServiceStack.Script;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json.Nodes;
using System.Windows.Forms;
using HyExcelVsto.Extensions.Dx51Helper;

namespace HyExcelVsto.Module.WX
{
    public partial class frm51Helper : Form
    {
        public Dictionary<string, string> Cookies;

        public string cookiesFile = ThisAddIn.ConfigurationSettings.GetValue("51Helper", "cookiesDatePath");

        public frm51Helper()
        { InitializeComponent(); }

        /// <summary>
        /// 获取登录信息 - 使用ETLoginWebBrowser创建登录框
        /// </summary>
        private void 获取登录信息()
        {
            try
            {
                textBoxLog.WriteLog($"{Environment.NewLine}正在检查WebView2运行时...");

                // 检查WebView2运行时是否可用
                if (!ET.ETLoginWebBrowser.WebView2Helper.IsWebView2RuntimeAvailable())
                {
                    textBoxLog.WriteLog($"{Environment.NewLine}WebView2运行时未安装或不可用");
                    textBoxLog.WriteLog($"{Environment.NewLine}请从以下地址下载并安装WebView2运行时：");
                    textBoxLog.WriteLog($"{Environment.NewLine}https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/");

                    MessageBox.Show("WebView2运行时未安装或不可用。\n\n请从以下地址下载并安装WebView2运行时：\nhttps://developer.microsoft.com/zh-cn/microsoft-edge/webview2/",
                        "WebView2运行时错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                string runtimeVersion = ET.ETLoginWebBrowser.WebView2Helper.GetWebView2RuntimeVersion();

                textBoxLog.WriteLog($"{Environment.NewLine}正在打开登录界面...");

                // 使用ETLoginWebBrowser创建登录框并设置用户提示
                var loginBrowser = new ET.ETLoginWebBrowser.ETLoginWebBrowser(Zn51Helper.vsto51Constant.Url.登录);
                loginBrowser.SetUserTip("登录51系统后，需右键手动刷新一次页面，才能捕捉到登录信息");

                loginBrowser.ShowDialog(this);

                string headersJson = loginBrowser.HeadersJson;

                if (!string.IsNullOrEmpty(headersJson))
                {
                    textBoxLog.WriteLog($"{Environment.NewLine}登录成功，正在处理登录信息...");

                    // 从返回的JSON数据获取cookies信息
                    var cookies = ExtractCookiesFromHeadersJson(headersJson);

                    if (cookies != null && cookies.Count > 0)
                    {
                        // 传递给 public Dictionary<string, string> Cookies
                        Cookies = cookies;
                        Zn51Helper.Cookies = cookies;

                        textBoxLog.WriteLog($"{Environment.NewLine}成功获取到 {cookies.Count} 个Cookie");
                        textBoxLog.WriteLog($"已加载登录信息，共 {Cookies.Count} 个Cookie");

                        // 验证登录信息有效性
                        JsonObject jsonObject = Zn51Helper.GetUserInfo().Result;
                        switch (jsonObject["code"]?.AsString())
                        {
                            case "200":
                                string userName = jsonObject["data"]["trueName"].AsString();
                                textBoxLog.WriteLog($"{Environment.NewLine}当前登录信息有效，登录名：{userName}");
                                break;

                            case "504":
                                textBoxLog.WriteLog($"{Environment.NewLine}登录超时，请重新登录");
                                break;

                            default:
                                textBoxLog.WriteLog($"{Environment.NewLine}登录信息无效，请重新登录");
                                break;
                        }
                        // 保存登录信息到文件
                        File.WriteAllText(cookiesFile, headersJson);
                        textBoxLog.WriteLog($"{Environment.NewLine}已保存登录信息到文件，登录信息一般有效期24小时");
                    }
                    else
                    {
                        textBoxLog.WriteLog($"{Environment.NewLine}未能从登录信息中提取到有效的Cookie");
                    }
                }
                else
                {
                    textBoxLog.WriteLog($"{Environment.NewLine}登录取消或失败");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                textBoxLog.WriteLog($"{Environment.NewLine}登录过程中发生权限错误: {ex.Message}");
                textBoxLog.WriteLog($"{Environment.NewLine}建议解决方案：");
                textBoxLog.WriteLog($"{Environment.NewLine}1. 以管理员身份运行程序");
                textBoxLog.WriteLog($"{Environment.NewLine}2. 检查防病毒软件是否阻止了程序运行");
                textBoxLog.WriteLog($"{Environment.NewLine}3. 确保程序安装目录有写入权限");

                MessageBox.Show($"登录过程中发生权限错误：{ex.Message}\n\n建议解决方案：\n1. 以管理员身份运行程序\n2. 检查防病毒软件是否阻止了程序运行\n3. 确保程序安装目录有写入权限",
                    "权限错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"{Environment.NewLine}登录过程中发生错误: {ex.Message}");

                // 检查是否是WebView2相关错误
                if (ex.HResult == unchecked((int)0x80070005)) // E_ACCESSDENIED
                {
                    textBoxLog.WriteLog($"{Environment.NewLine}这是一个访问权限错误，错误代码：0x80070005");
                    textBoxLog.WriteLog($"{Environment.NewLine}建议解决方案：");
                    textBoxLog.WriteLog($"{Environment.NewLine}1. 以管理员身份运行程序");
                    textBoxLog.WriteLog($"{Environment.NewLine}2. 检查WebView2运行时是否已正确安装");
                    textBoxLog.WriteLog($"{Environment.NewLine}3. 检查防病毒软件是否阻止了程序运行");
                    textBoxLog.WriteLog($"{Environment.NewLine}4. 尝试重新安装WebView2运行时");

                    MessageBox.Show($"登录过程中发生访问权限错误：{ex.Message}\n\n这通常是WebView2权限问题。建议解决方案：\n1. 以管理员身份运行程序\n2. 检查WebView2运行时是否已正确安装\n3. 检查防病毒软件是否阻止了程序运行\n4. 尝试重新安装WebView2运行时",
                        "WebView2权限错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show($"登录过程中发生错误：{ex.Message}\n\n如果问题持续存在，请尝试以管理员身份运行程序。",
                        "登录错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 从ETLoginWebBrowser返回的HeadersJson中提取cookies信息
        /// </summary>
        /// <param name="headersJson">ETLoginWebBrowser返回的JSON数据</param>
        /// <returns>提取的cookies字典</returns>
        private Dictionary<string, string> ExtractCookiesFromHeadersJson(string headersJson)
        {
            try
            {
                // 使用ETWebBrowserJsonFormatter解析JSON数据
                if (ETWebBrowserJsonFormatter.IsNewFormat(headersJson))
                {
                    var loginInfo = ETWebBrowserJsonFormatter.ParseLoginInfoJson(headersJson);
                    if (loginInfo?.Cookies != null && loginInfo.Cookies.Count > 0)
                    {
                        var cookieDict = new Dictionary<string, string>();
                        foreach (var cookie in loginInfo.Cookies)
                        {
                            if (!string.IsNullOrEmpty(cookie.Name) && !string.IsNullOrEmpty(cookie.Value))
                            {
                                cookieDict[cookie.Name] = cookie.Value;
                            }
                        }
                        return cookieDict;
                    }
                }

                return new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"{Environment.NewLine}解析登录信息时发生错误: {ex.Message}");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private void 登录_Click(object sender, EventArgs e)
        { 获取登录信息(); }

        /// <summary>
        /// 登录按钮点击事件 - 新增的登录按钮
        /// </summary>
        private void button登录_Click(object sender, EventArgs e)
        { 获取登录信息(); }

        /// <summary>
        /// WebView2诊断按钮点击事件
        /// </summary>
        private void button诊断WebView2_Click(object sender, EventArgs e)
        {
            try
            {
                textBoxLog.WriteLog($"{Environment.NewLine}正在运行WebView2诊断...");
                ET.ETLoginWebBrowser.WebView2DiagnosticTool.ShowDiagnosticDialog(this);
            }
            catch (Exception ex)
            {
                textBoxLog.WriteLog($"{Environment.NewLine}运行WebView2诊断失败: {ex.Message}");
                MessageBox.Show($"运行WebView2诊断失败：{ex.Message}", "诊断错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载事件 - 初始化时从cookiesFile路径读取文件中的json数据
        /// </summary>
        private void frm51Helper_Load(object sender, EventArgs e)
        {
            Zn51Helper.logTextBox = textBoxLog;

            if (File.Exists(cookiesFile))
            {
                try
                {
                    // 从cookiesFile路径读取文件中的json数据，从json数据获取cookies信息
                    var loadedCookies = Zn51Helper.LoadFromFile(cookiesFile);

                    // 传递给 public Dictionary<string, string> Cookies
                    Cookies = loadedCookies;
                    Zn51Helper.Cookies = loadedCookies;

                    textBoxLog.WriteLog($"已加载登录信息，共 {loadedCookies.Count} 个Cookie");

                    // 验证登录信息有效性
                    JsonObject jsonObject = Zn51Helper.GetUserInfo().Result;
                    switch (jsonObject["code"]?.AsString())
                    {
                        case "200":
                            string userName = jsonObject["data"]["trueName"].AsString();
                            textBoxLog.WriteLog($"{Environment.NewLine}当前登录信息有效，登录名：{userName}");
                            break;

                        case "504":
                            textBoxLog.WriteLog($"{Environment.NewLine}登录超时，请重新登录");
                            break;

                        default:
                            textBoxLog.WriteLog($"{Environment.NewLine}登录信息无效，请重新登录");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    textBoxLog.WriteLog($"{Environment.NewLine}加载登录信息时发生错误: {ex.Message}");
                    textBoxLog.WriteLog($"{Environment.NewLine}请点击登录按钮重新登录");
                }
            }
            else
            {
                textBoxLog.WriteLog("未找到登录信息文件，请点击登录按钮进行登录");
            }
        }

        private void button空白表模_Click(object sender, EventArgs e)
        {
            空白表模();
        }

        private void button上传预算_Click(object sender, EventArgs e)
        {
            上传预算();
        }

        private void button上传设计交底_Click(object sender, EventArgs e)
        {
            上传设计交底();
        }

        private void button上传图纸_Click(object sender, EventArgs e)
        {
            上传图纸();
        }

        private void button默认衔接表_Click(object sender, EventArgs e)
        {
            默认衔接表();
        }

        private void button批量换点_Click(object sender, EventArgs e)
        {
            批量换点();
        }

        private void button一键审批通过_Click(object sender, EventArgs e)
        {
            一键审批通过();
        }

        private void button设置存储目录_Click(object sender, EventArgs e)
        {
            设置存储目录();
        }

        private void button下载任务管理并更新_Click(object sender, EventArgs e)
        {
            下载任务管理并更新();
        }

        private void button下载交付列表并更新_Click(object sender, EventArgs e)
        {
            下载交付列表并更新();
        }
    }
}