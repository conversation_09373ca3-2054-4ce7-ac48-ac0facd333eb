# Visual Studio 证书生成自动化脚本
# 模拟Visual Studio IDE的证书生成过程

param(
    [string]$ProjectPath = "HyExcelVsto\HyExcelVsto.csproj",
    [string]$CertificateName = "HyExcelVsto_VS",
    [string]$Password = "123456",
    [switch]$UpdateProject = $true
)

Write-Host "=== Visual Studio 证书生成脚本 ===" -ForegroundColor Cyan
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow
Write-Host "证书名称: $CertificateName" -ForegroundColor Yellow

# 检查项目文件
if (-not (Test-Path $ProjectPath)) {
    Write-Error "项目文件不存在: $ProjectPath"
    exit 1
}

$projectDir = Split-Path $ProjectPath -Parent
$certFile = Join-Path $projectDir "$CertificateName.pfx"

try {
    Write-Host "`n=== 步骤1: 生成自签名证书 ===" -ForegroundColor Green
    
    # 使用PowerShell生成证书（模拟VS行为）
    $cert = New-SelfSignedCertificate -Subject "CN=$CertificateName" -CertStoreLocation "Cert:\CurrentUser\My" -KeyUsage DigitalSignature -KeySpec Signature -KeyLength 2048 -KeyAlgorithm RSA -HashAlgorithm SHA256 -NotAfter (Get-Date).AddYears(5) -Type CodeSigningCert
    
    Write-Host "证书已创建，指纹: $($cert.Thumbprint)" -ForegroundColor Green
    
    Write-Host "`n=== 步骤2: 导出PFX文件 ===" -ForegroundColor Green
    
    # 导出为PFX文件
    $securePassword = ConvertTo-SecureString -String $Password -Force -AsPlainText
    Export-PfxCertificate -Cert $cert -FilePath $certFile -Password $securePassword -Force
    
    Write-Host "PFX文件已生成: $certFile" -ForegroundColor Green
    
    if ($UpdateProject) {
        Write-Host "`n=== 步骤3: 更新项目配置 ===" -ForegroundColor Green
        
        # 读取项目文件
        $content = Get-Content $ProjectPath -Raw
        
        # 启用签名
        $content = $content -replace '<SignManifests>false</SignManifests>', '<SignManifests>true</SignManifests>'
        
        # 更新证书文件路径
        if ($content -match '<ManifestKeyFile>.*?</ManifestKeyFile>') {
            $content = $content -replace '<ManifestKeyFile>.*?</ManifestKeyFile>', "<ManifestKeyFile>$CertificateName.pfx</ManifestKeyFile>"
        }
        else {
            # 添加证书配置
            $certConfig = @"
  <PropertyGroup>
    <ManifestKeyFile>$CertificateName.pfx</ManifestKeyFile>
  </PropertyGroup>
"@
            $content = $content -replace '(</PropertyGroup>\s*<!--)', "$certConfig`r`n`$1"
        }
        
        # 更新证书指纹
        if ($content -match '<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>') {
            $content = $content -replace '<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>', "<ManifestCertificateThumbprint>$($cert.Thumbprint)</ManifestCertificateThumbprint>"
        }
        else {
            # 添加指纹配置
            $thumbprintConfig = @"
  <PropertyGroup>
    <ManifestCertificateThumbprint>$($cert.Thumbprint)</ManifestCertificateThumbprint>
  </PropertyGroup>
"@
            $content = $content -replace '(</PropertyGroup>\s*<!--)', "$thumbprintConfig`r`n`$1"
        }
        
        # 保存项目文件
        $content | Set-Content $ProjectPath -Encoding UTF8
        Write-Host "项目配置已更新" -ForegroundColor Green
    }
    
    Write-Host "`n=== 步骤4: 验证配置 ===" -ForegroundColor Green
    
    # 验证证书文件
    if (Test-Path $certFile) {
        $fileSize = (Get-Item $certFile).Length
        Write-Host "✓ PFX文件存在，大小: $fileSize 字节" -ForegroundColor Green
    }
    
    # 验证证书在存储区中
    $installedCert = Get-ChildItem -Path "Cert:\CurrentUser\My" | Where-Object { $_.Thumbprint -eq $cert.Thumbprint }
    if ($installedCert) {
        Write-Host "✓ 证书已安装到用户存储区" -ForegroundColor Green
        Write-Host "  主题: $($installedCert.Subject)" -ForegroundColor Gray
        Write-Host "  有效期: $($installedCert.NotBefore) 到 $($installedCert.NotAfter)" -ForegroundColor Gray
    }
    
    Write-Host "`n=== 证书生成完成 ===" -ForegroundColor Cyan
    Write-Host "证书文件: $certFile" -ForegroundColor White
    Write-Host "证书密码: $Password" -ForegroundColor White
    Write-Host "证书指纹: $($cert.Thumbprint)" -ForegroundColor White
    
    Write-Host "`n=== 下一步操作 ===" -ForegroundColor Cyan
    Write-Host "1. 在Visual Studio中重新生成解决方案" -ForegroundColor White
    Write-Host "2. 发布VSTO项目" -ForegroundColor White
    Write-Host "3. 测试安装程序" -ForegroundColor White
    
    # 生成Visual Studio操作指南
    $vsGuide = @"

=== Visual Studio 操作指南 ===

如果您更喜欢在Visual Studio IDE中手动操作：

1. 打开 HyExcelVsto 项目
2. 右键项目 → 属性
3. 选择"签名"选项卡
4. 勾选"为ClickOnce清单签名"
5. 在"选择存储证书"中选择"从文件选择"
6. 浏览并选择: $certFile
7. 输入密码: $Password
8. 保存项目并重新生成

"@
    
    Write-Host $vsGuide -ForegroundColor Gray
    
}
catch {
    Write-Error "证书生成失败: $($_.Exception.Message)"
    
    # 清理可能的部分文件
    if (Test-Path $certFile) {
        Remove-Item $certFile -Force -ErrorAction SilentlyContinue
    }
}

Write-Host "`n=== 故障排除 ===" -ForegroundColor Cyan
Write-Host "如果遇到问题，请尝试：" -ForegroundColor Yellow
Write-Host "1. 以管理员身份运行PowerShell" -ForegroundColor White
Write-Host "2. 检查Windows证书存储区权限" -ForegroundColor White
Write-Host "3. 临时禁用防病毒软件" -ForegroundColor White
Write-Host "4. 使用Visual Studio IDE手动创建证书" -ForegroundColor White
