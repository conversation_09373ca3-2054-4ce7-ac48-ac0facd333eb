using System;
using System.Drawing;
using System.IO;
using System.Media;
using System.Windows.Forms;

namespace ET
{
    public static class ETNotificationHelper
    {
        static NotificationForm notificationForm;

        /// <summary>
        /// 获取或设置默认提示音文件路径
        /// </summary>
        public static string DefaultSoundFilePath { get; set; }

        /// <summary>
        /// 显示消息通知
        /// </summary>
        /// <param name="message">要显示的消息</param>
        /// <param name="centerScreen">是否在屏幕正中心显示，默认为false（居中底部显示）</param>
        /// <param name="playSound">是否播放提示音，默认为false</param>
        /// <param name="soundFilePath">提示音文件路径，为null时使用默认提示音</param>
        public static void ShowNotification(string message, bool centerScreen = false, bool playSound = false, string soundFilePath = null)
        {
            if (playSound)
            {
                PlayNotificationSound(soundFilePath);
            }

            if (notificationForm == null || notificationForm.IsDisposed)
            {
                notificationForm = new NotificationForm(message, centerScreen);
                notificationForm.Show();
            }
            else
            {
                notificationForm.AddMessage(message);
            }
        }

        /// <summary>
        /// 播放提示音
        /// </summary>
        /// <param name="soundFilePath">提示音文件路径，为null时使用默认提示音</param>
        static void PlayNotificationSound(string soundFilePath)
        {
            try
            {
                string fileToPlay = soundFilePath;

                // 检查指定的音频文件是否存在
                if (string.IsNullOrEmpty(fileToPlay) || !File.Exists(fileToPlay))
                {
                    // 如果指定的文件不存在，使用默认提示音文件
                    fileToPlay = DefaultSoundFilePath;
                }

                // 如果有可用的音频文件，播放它
                if (!string.IsNullOrEmpty(fileToPlay) && File.Exists(fileToPlay))
                {
                    using (SoundPlayer player = new(fileToPlay))
                    {
                        player.Play();
                    }
                }
                else
                {
                    // 如果没有可用的音频文件，播放系统提示音
                    SystemSounds.Asterisk.Play();
                }
            }
            catch
            {
                // 如果播放音频时发生错误，使用系统提示音作为后备
                SystemSounds.Asterisk.Play();
            }
        }

        class NotificationForm : Form
        {
            Timer timer;
            Label messageLabel;
            bool centerScreen;

            public NotificationForm(string message, bool centerScreen)
            {
                this.centerScreen = centerScreen;
                InitializeComponent();
                messageLabel.Text = message;
                AdjustFormSize();
            }

            void InitializeComponent()
            {
                timer = new Timer();
                messageLabel = new Label();

                // 设置窗体属性
                FormBorderStyle = FormBorderStyle.None;
                StartPosition = FormStartPosition.Manual;
                BackColor = Color.LightYellow;
                TopMost = true;
                ShowInTaskbar = false; // 不在任务栏中显示

                // 添加1px红色边框
                Paint += (sender, e) =>
                {
                    using (Pen pen = new Pen(Color.Red, 1))
                    {
                        e.Graphics.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
                    }
                };

                // 设置消息标签属性
                messageLabel.AutoSize = true;
                messageLabel.Location = new Point(3, 3); // 调整为3号边距
                messageLabel.Font = new Font("宋体", 9); // 调整为9号字体
                messageLabel.Click += NotificationForm_Click; // 为标签添加点击事件
                Controls.Add(messageLabel);

                // 设置定时器属性
                timer.Interval = 5000; // 5秒
                timer.Tick += Timer_Tick;

                // 设置窗体位置
                SetFormPosition();

                // 启动定时器
                timer.Start();

                // 鼠标事件
                MouseEnter += NotificationForm_MouseEnter;
                MouseLeave += NotificationForm_MouseLeave;
                Click += NotificationForm_Click; // 添加单击事件
                DoubleClick += NotificationForm_DoubleClick;
            }

            void SetFormPosition()
            {
                Rectangle screen = Screen.PrimaryScreen.WorkingArea;
                if (centerScreen)
                {
                    Location = new Point((screen.Width - Width) / 2, (screen.Height - Height) / 2);
                }
                else
                {
                    Location = new Point((screen.Width - Width) / 2, screen.Height - Height - 2);
                }
            }

            void AdjustFormSize()
            {
                // 根据消息内容调整窗体大小
                using (Graphics g = CreateGraphics())
                {
                    SizeF size = g.MeasureString(messageLabel.Text, messageLabel.Font);
                    Size = new System.Drawing.Size((int)size.Width + 8, (int)size.Height + 8); // 调整为4号边距，为边框留出空间
                }

                // 调整窗体位置
                SetFormPosition();
            }

            void Timer_Tick(object sender, EventArgs e)
            {
                timer.Stop();
                Close();
            }

            void NotificationForm_MouseEnter(object sender, EventArgs e)
            {
                timer.Stop();
            }

            void NotificationForm_MouseLeave(object sender, EventArgs e)
            {
                timer.Start();
            }

            /// <summary>
            /// 单击提示消息时立即关闭
            /// </summary>
            void NotificationForm_Click(object sender, EventArgs e)
            {
                timer.Stop();
                Close();
            }

            /// <summary>
            /// 双击提示消息时立即关闭（保持原有功能）
            /// </summary>
            void NotificationForm_DoubleClick(object sender, EventArgs e)
            {
                timer.Stop();
                Close();
            }

            /// <summary>
            /// 添加新的消息到现有窗体
            /// </summary>
            /// <param name="message">要添加的消息</param>
            public void AddMessage(string message)
            {
                messageLabel.Text += $"{Environment.NewLine}{Environment.NewLine}{message}";
                AdjustFormSize();
                timer.Stop();
                timer.Start();
            }

            /// <summary>
            /// 释放资源
            /// </summary>
            /// <param name="disposing">是否释放托管资源</param>
            protected override void Dispose(bool disposing)
            {
                if (disposing)
                {
                    timer?.Stop();
                    timer?.Dispose();
                }
                base.Dispose(disposing);
            }
        }
    }
}