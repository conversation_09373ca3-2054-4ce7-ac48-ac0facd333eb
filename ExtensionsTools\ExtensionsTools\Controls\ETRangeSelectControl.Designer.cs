namespace ET.Controls
{
    partial class ETRangeSelectControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.textBox地址 = new System.Windows.Forms.RichTextBox();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.label选择按钮 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // textBox地址
            // 
            this.textBox地址.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.textBox地址.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox地址.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox地址.Location = new System.Drawing.Point(0, 0);
            this.textBox地址.Multiline = false;
            this.textBox地址.Name = "textBox地址";
            this.textBox地址.ScrollBars = System.Windows.Forms.RichTextBoxScrollBars.None;
            this.textBox地址.Size = new System.Drawing.Size(176, 19);
            this.textBox地址.TabIndex = 1;
            this.textBox地址.Text = "";
            this.textBox地址.Enter += new System.EventHandler(this.textBox地址_Enter);
            this.textBox地址.Leave += new System.EventHandler(this.textBox地址_Leave);
            // 
            // splitContainer1
            // 
            this.splitContainer1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(0);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.textBox地址);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.label选择按钮);
            this.splitContainer1.Panel2.Click += new System.EventHandler(this.button选择_Click);
            this.splitContainer1.Size = new System.Drawing.Size(200, 21);
            this.splitContainer1.SplitterDistance = 178;
            this.splitContainer1.SplitterWidth = 1;
            this.splitContainer1.TabIndex = 3;
            this.splitContainer1.Resize += new System.EventHandler(this.splitContainer1_Resize);
            // 
            // label选择按钮
            // 
            this.label选择按钮.BackColor = System.Drawing.SystemColors.Control;
            this.label选择按钮.Cursor = System.Windows.Forms.Cursors.Hand;
            this.label选择按钮.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label选择按钮.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label选择按钮.ForeColor = System.Drawing.Color.DarkBlue;
            this.label选择按钮.Location = new System.Drawing.Point(0, 0);
            this.label选择按钮.Name = "label选择按钮";
            this.label选择按钮.Size = new System.Drawing.Size(19, 19);
            this.label选择按钮.TabIndex = 0;
            this.label选择按钮.Text = "∷";
            this.label选择按钮.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.label选择按钮.Click += new System.EventHandler(this.button选择_Click);
            // 
            // ETRangeSelectControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.splitContainer1);
            this.Name = "ETRangeSelectControl";
            this.Size = new System.Drawing.Size(200, 21);
            this.SizeChanged += new System.EventHandler(this.ETRangeSelectControl_SizeChanged);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.RichTextBox textBox地址;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.Label label选择按钮;
    }
}
