<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\Argument.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>OpenAI.Argument.AssertNotNullstatic##void#T, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AssertNotNull</ItemName><ItemPath>OpenAI.Argument</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\OpenAI.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\Argument.cs</ProjectItemFileName><TimeStamp>2025-06-22T15:03:10.1094864+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>OpenAI.MultiPartFormDataBinaryContent.Add#void#Stream, string, string, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Add</ItemName><ItemPath>OpenAI.MultiPartFormDataBinaryContent</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\OpenAI.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName><TimeStamp>2025-06-22T15:03:23.8527073+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>OpenAI.MultiPartFormDataBinaryContent.MultiPartFormDataBinaryContent##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>MultiPartFormDataBinaryContent</ItemName><ItemPath>OpenAI.MultiPartFormDataBinaryContent</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\OpenAI.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName><TimeStamp>2025-06-22T15:03:02.5956865+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>OpenAI.MultiPartFormDataBinaryContent.CreateBoundarystatic##string#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateBoundary</ItemName><ItemPath>OpenAI.MultiPartFormDataBinaryContent</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\OpenAI.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName><TimeStamp>2025-06-22T15:02:44.2813899+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\Other\openai-dotnet-main\src\Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>OpenAI</ProjectName></ProjectData>