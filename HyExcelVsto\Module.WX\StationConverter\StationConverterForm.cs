using System;
using System.Windows.Forms;
using ET;
using ET.Controls;
using ExtensionsTools;
using HyExcelVsto.Module.WX.StationConverter;

namespace HyExcelVsto.Module.WX.StationConverter
{
    /// <summary>
    /// 站点数据转换器独立窗体 提供站点数据转换功能
    /// </summary>
    public partial class StationConverterForm : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StationConverterForm()
        {
            InitializeComponent();
            InitializeControls();

            ETLogManager.Info(this, "StationConverterForm初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 初始化日志控件
            etLogDisplayControlConverter.WriteInfo("站点数据转换器已就绪");
            etLogDisplayControlConverter.WriteInfo("功能说明：");
            etLogDisplayControlConverter.WriteInfo("• 创建空白工作簿：自动创建包含\"数据来源\"和\"数据输出\"表的标准工作簿");
            etLogDisplayControlConverter.WriteInfo("• 执行站点转换：将逻辑站点数据转换为物理站点汇总数据");
            etLogDisplayControlConverter.WriteInfo("• 支持智能站点分组、动态频段配置和高性能GPS算法");
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 创建工作簿按钮点击事件处理
        /// </summary>
        private void BtnCreateWorkbook_Click(object sender, EventArgs e)
        {
            try
            {
                etLogDisplayControlConverter.Clear();
                etLogDisplayControlConverter.WriteInfo("开始创建空白站点转换工作簿...");

                // 调用StationConverterHelper创建空白工作簿
                var newWorkbook = StationConverterHelper.CreateBlankWorkbook();

                if (newWorkbook != null)
                {
                    etLogDisplayControlConverter.WriteInfo("✅ 空白工作簿创建成功！");
                    etLogDisplayControlConverter.WriteInfo("工作簿包含：");
                    etLogDisplayControlConverter.WriteInfo("  • '数据来源'表（已设置标准表头）");
                    etLogDisplayControlConverter.WriteInfo("  • '数据输出'表（已设置标准表头）");
                    etLogDisplayControlConverter.WriteInfo("请在'数据来源'表中填入数据，然后可以执行转换。");
                }
                else
                {
                    etLogDisplayControlConverter.WriteError("❌ 创建工作簿失败！");
                    MessageBox.Show("创建工作簿失败！", "创建失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "创建工作簿失败", ex);
                etLogDisplayControlConverter.WriteError("创建工作簿时发生错误", ex);
                MessageBox.Show($"创建工作簿时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行转换按钮点击事件处理
        /// </summary>
        private void BtnExecuteConversion_Click(object sender, EventArgs e)
        {
            try
            {
                etLogDisplayControlConverter.Clear();
                etLogDisplayControlConverter.WriteInfo("开始执行站点数据转换...");

                // 先获取验证报告
                string validationReport = StationConverterHelper.GetValidationReport();
                etLogDisplayControlConverter.WriteInfo("=== 数据验证报告 ===");
                etLogDisplayControlConverter.WriteInfo(validationReport);

                // 检查是否有明显错误
                if (validationReport.Contains("❌"))
                {
                    etLogDisplayControlConverter.WriteWarning("⚠️ 数据验证发现问题，请检查后再执行转换");
                    MessageBox.Show($"数据验证发现问题：\n\n{validationReport}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 确认执行
                DialogResult confirmResult = MessageBox.Show(
                    "数据验证通过，是否开始转换站点数据？",
                    "确认转换",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (confirmResult == DialogResult.Yes)
                {
                    etLogDisplayControlConverter.WriteInfo("=== 开始执行转换 ===");

                    // 执行转换
                    bool success = StationConverterHelper.ExecuteAutoConversion();

                    if (success)
                    {
                        etLogDisplayControlConverter.WriteInfo("🎉 站点数据转换完成！");
                        MessageBox.Show("站点数据转换完成！", "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        etLogDisplayControlConverter.WriteError("❌ 转换失败，请查看日志了解详细信息");
                        MessageBox.Show("转换失败，请查看日志了解详细信息。", "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    etLogDisplayControlConverter.WriteInfo("用户取消了转换操作");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "执行转换失败", ex);
                etLogDisplayControlConverter.WriteError("执行转换时发生错误", ex);
                MessageBox.Show($"执行转换时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件处理

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "StationConverterForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}