using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// ET库通用Excel范围选择控件 增强功能：集成ETLogManager日志记录、优化错误处理、线程安全UI更新、支持事件触发控制
    /// </summary>
    public partial class ETRangeSelectControl : UserControl
    {
        #region 私有字段

        /// <summary>
        /// 父窗体原始高度，用于隐藏/显示父窗体
        /// </summary>
        private int _parentFormHeight;

        /// <summary>
        /// 上一次的地址文本，用于检测地址变化
        /// </summary>
        private string _previousAddress;

        /// <summary>
        /// 当前选中的Range对象
        /// </summary>
        private Range _range = null;

        /// <summary>
        /// Excel应用程序窗口句柄
        /// </summary>
        private IntPtr _activeExcelIntPtr = IntPtr.Zero;

        /// <summary>
        /// Excel应用程序提供者
        /// </summary>
        private IExcelApplicationProvider _excelProvider;

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 是否启用回车键触发选择功能
        /// </summary>
        /// <remarks>当设置为true时，文本框获得焦点即触发选择对话框</remarks>
        public bool EnableEnterThenSelect { get; set; } = false;

        /// <summary>
        /// 是否在选择时隐藏父窗体
        /// </summary>
        /// <remarks>隐藏父窗体可以避免遮挡Excel界面，提升用户体验</remarks>
        public bool HideParentForm { get; set; } = true;

        /// <summary>
        /// 输入提示文本
        /// </summary>
        /// <remarks>在Excel InputBox中显示的提示信息</remarks>
        public string InputPromptText { get; set; } = "请选择Excel范围：";

        /// <summary>
        /// 当前选中的Excel Range对象
        /// </summary>
        /// <remarks>封装SelectedRange的get和set逻辑，简化代码并移除不必要的变量赋值 设置新值时会自动更新文本框显示和颜色</remarks>
        public Range SelectedRange
        {
            get => _range;
            set => SetSelectedRange(value);
        }

        /// <summary>
        /// 获取或设置控件显示的文本
        /// </summary>
        /// <remarks>直接操作内部文本框的Text属性</remarks>
        public new string Text
        {
            get => textBox地址.Text;
            set => textBox地址.Text = value;
        }

        /// <summary>
        /// 获取选中范围的完整地址
        /// </summary>
        /// <remarks>优化FullSelectedAddress的实现，避免每次都调用函数 返回格式：'工作表名'!单元格地址</remarks>
        public string FullSelectedAddress => _range?.GetFullAddress();

        #endregion 公共属性

        #region 公共事件

        /// <summary>
        /// 文本框获得焦点时触发的事件
        /// </summary>
        /// <remarks>使用内置事件处理器替代自定义委托类型</remarks>
        public event EventHandler EnterEvent;

        /// <summary>
        /// 开始选择范围时触发的事件
        /// </summary>
        /// <remarks>在显示Excel选择对话框前触发</remarks>
        public event EventHandler BeginSelectEvent;

        /// <summary>
        /// 选择完成时触发的事件
        /// </summary>
        /// <remarks>选择完成后触发，参数为选中的Range对象</remarks>
        public event EventHandler SelectedEvent;

        #endregion 公共事件

        #region 公共方法

        /// <summary>
        /// 设置选中的Range对象，并可选择是否触发事件
        /// </summary>
        /// <param name="range">要设置的Range对象</param>
        /// <param name="triggerEvent">是否触发SelectedEvent事件，默认为true</param>
        /// <remarks>此方法允许外部代码程序化设置SelectedRange，并控制是否触发SelectedEvent事件 当triggerEvent为true时，会触发SelectedEvent事件，通知订阅者范围已更改</remarks>
        public void SetSelectedRangeWithEvent(Range range, bool triggerEvent = true)
        {
            SetSelectedRange(range, triggerEvent);
        }

        /// <summary>
        /// 设置Excel应用程序提供者
        /// </summary>
        /// <param name="provider">Excel应用程序提供者</param>
        /// <remarks>允许外部代码设置自定义的Excel应用程序提供者 如果不设置，将使用默认的COM互操作提供者</remarks>
        public void SetExcelApplicationProvider(IExcelApplicationProvider provider)
        {
            _excelProvider = provider;
            ETLogManager.Debug(this, $"设置Excel应用程序提供者：{provider?.GetType().Name ?? "null"}");
        }

        /// <summary>
        /// 调试方法：测试所有可能的Excel应用程序获取方式
        /// </summary>
        /// <remarks>用于诊断WPS环境下的应用程序获取问题</remarks>
        public void DebugTestAllApplicationGetters()
        {
            ETLogManager.Info(this, "🔍 开始调试测试所有Excel应用程序获取方式");

            // 测试1：当前提供者
            ETLogManager.Info(this, "=== 测试1：当前提供者 ===");
            try
            {
                var app1 = GetExcelApplication();
                ETLogManager.Info(this, $"当前提供者结果: {(app1 != null ? "成功" : "失败")}");
                if (app1 != null)
                {
                    ETLogManager.Info(this, $"应用程序名称: {app1.Name}");
                    ETLogManager.Info(this, $"启动路径: {app1.StartupPath}");
                    ETLogManager.Info(this, $"版本: {app1.Version}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"当前提供者测试失败: {ex.Message}");
            }

            // 测试2：直接COM互操作
            ETLogManager.Info(this, "=== 测试2：直接COM互操作 ===");
            try
            {
                var app2 = (Microsoft.Office.Interop.Excel.Application)
                    System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                ETLogManager.Info(this, $"COM互操作结果: {(app2 != null ? "成功" : "失败")}");
                if (app2 != null)
                {
                    ETLogManager.Info(this, $"应用程序名称: {app2.Name}");
                    ETLogManager.Info(this, $"启动路径: {app2.StartupPath}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Info(this, $"COM互操作测试失败: {ex.Message}");
            }

            // 测试3：WPS特定COM对象
            ETLogManager.Info(this, "=== 测试3：WPS特定COM对象 ===");
            try
            {
                var app3 = (Microsoft.Office.Interop.Excel.Application)
                    System.Runtime.InteropServices.Marshal.GetActiveObject("ET.Application");
                ETLogManager.Info(this, $"WPS COM结果: {(app3 != null ? "成功" : "失败")}");
                if (app3 != null)
                {
                    ETLogManager.Info(this, $"应用程序名称: {app3.Name}");
                    ETLogManager.Info(this, $"启动路径: {app3.StartupPath}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Info(this, $"WPS COM测试失败: {ex.Message}");
            }

            // 测试4：VSTO方式
            ETLogManager.Info(this, "=== 测试4：VSTO方式 ===");
            try
            {
                var app4 = TryGetVstoApplication();
                ETLogManager.Info(this, $"VSTO方式结果: {(app4 != null ? "成功" : "失败")}");
                if (app4 != null)
                {
                    var excelApp = app4 as Microsoft.Office.Interop.Excel.Application;
                    if (excelApp != null)
                    {
                        ETLogManager.Info(this, $"应用程序名称: {excelApp.Name}");
                        ETLogManager.Info(this, $"启动路径: {excelApp.StartupPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"VSTO方式测试失败: {ex.Message}");
            }

            // 测试5：ETExcelExtensions.App方式
            ETLogManager.Info(this, "=== 测试5：ETExcelExtensions.App方式 ===");
            try
            {
                var app5 = ETExcelExtensions.App;
                ETLogManager.Info(this, $"ETExcelExtensions.App结果: {(app5 != null ? "成功" : "失败")}");
                if (app5 != null)
                {
                    ETLogManager.Info(this, $"应用程序名称: {app5.Name}");
                    ETLogManager.Info(this, $"启动路径: {app5.StartupPath}");
                    ETLogManager.Info(this, $"版本: {app5.Version}");
                    bool isWps = app5.StartupPath?.ToLower().Contains("wps") ?? false;
                    ETLogManager.Info(this, $"是否为WPS: {isWps}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"ETExcelExtensions.App测试失败: {ex.Message}");
            }

            // 测试6：HyExcelVsto.ThisAddIn.ExcelApplication方式
            ETLogManager.Info(this, "=== 测试6：HyExcelVsto.ThisAddIn.ExcelApplication方式 ===");
            try
            {
                var thisAddInType = System.Type.GetType("HyExcelVsto.ThisAddIn, HyExcelVsto");
                if (thisAddInType != null)
                {
                    var excelAppProperty = thisAddInType.GetProperty("ExcelApplication",
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (excelAppProperty != null)
                    {
                        var app6 = excelAppProperty.GetValue(null) as Microsoft.Office.Interop.Excel.Application;
                        ETLogManager.Info(this, $"HyExcelVsto.ExcelApplication结果: {(app6 != null ? "成功" : "失败")}");
                        if (app6 != null)
                        {
                            ETLogManager.Info(this, $"应用程序名称: {app6.Name}");
                            ETLogManager.Info(this, $"启动路径: {app6.StartupPath}");
                        }
                    }
                    else
                    {
                        ETLogManager.Info(this, "未找到ExcelApplication属性");
                    }
                }
                else
                {
                    ETLogManager.Info(this, "未找到HyExcelVsto.ThisAddIn类型");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"HyExcelVsto.ExcelApplication测试失败: {ex.Message}");
            }

            // 测试5：检查当前进程信息
            ETLogManager.Info(this, "=== 测试5：当前进程信息 ===");
            try
            {
                var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                ETLogManager.Info(this, $"当前进程名称: {currentProcess.ProcessName}");
                ETLogManager.Info(this, $"当前进程ID: {currentProcess.Id}");
                ETLogManager.Info(this, $"主模块文件名: {currentProcess.MainModule?.FileName ?? "Unknown"}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"进程信息获取失败: {ex.Message}");
            }

            ETLogManager.Info(this, "🔍 调试测试完成");
        }

        #endregion 公共方法

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETRangeSelectControl()
        {
            InitializeComponent();
            InitializeControlSettings();

            // 优先使用直接的WPS提供者（通过ETExcelExtensions.App获取）
            // 这是在HyHelper项目中最可靠的方式
            _excelProvider = new WpsDirectApplicationProvider();

            ETLogManager.Debug(this, "ETRangeSelectControl初始化完成，使用WPS直接提供者");
        }

        /// <summary>
        /// 初始化控件设置
        /// </summary>
        /// <remarks>设置控件的默认属性和样式</remarks>
        private void InitializeControlSettings()
        {
            try
            {
                // 设置文本框默认样式
                textBox地址.ForeColor = SystemColors.ControlLight;
                textBox地址.Text = "";

                // 设置控件默认大小
                this.Size = new System.Drawing.Size(200, 21);

                ETLogManager.Debug(this, "控件设置初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化控件设置失败", ex);
            }
        }

        #endregion 构造函数

        #region 事件处理

        /// <summary>
        /// 文本框获得焦点事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void textBox地址_Enter(object sender, EventArgs e)
        {
            try
            {
                // 优化字符串的处理，移除Switch语句，提高代码效率
                _previousAddress = textBox地址.Text.Trim();
                if (_previousAddress == "地址无效")
                {
                    textBox地址.Clear();
                }

                // 触发Enter事件
                EnterEvent?.Invoke(this, EventArgs.Empty);

                // 如果启用了回车即选择功能
                if (EnableEnterThenSelect)
                {
                    BeginSelectEvent?.Invoke(this, EventArgs.Empty);
                    ShowSelectionBox();
                }

                ETLogManager.Debug(this, $"文本框获得焦点，上次地址：{_previousAddress}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理文本框Enter事件失败", ex);
            }
        }

        /// <summary>
        /// 文本框失去焦点事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void textBox地址_Leave(object sender, EventArgs e)
        {
            try
            {
                string currentAddress = textBox地址.Text.Trim();
                if (currentAddress == _previousAddress)
                {
                    return; // 地址未变化，无需处理
                }

                // 尝试解析用户输入的地址
                try
                {
                    // 需要通过Globals.ThisAddIn或其他方式获取Excel Application 这里需要根据实际的Excel集成方式进行调整
                    var app = GetExcelApplication();
                    if (app != null)
                    {
                        SelectedRange = app.Range[currentAddress];
                        ETLogManager.Info(this, $"成功解析地址：{currentAddress}");
                    }
                    else
                    {
                        SelectedRange = null;
                        ETLogManager.Warning(this, "无法获取Excel应用程序实例");
                    }
                }
                catch (Exception parseEx)
                {
                    SelectedRange = null;
                    ETLogManager.Warning(this, $"地址解析失败：{currentAddress}，错误：{parseEx.Message}");
                }

                ValidateInput();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理文本框Leave事件失败", ex);
            }
        }

        /// <summary>
        /// 选择按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button选择_Click(object sender, EventArgs e)
        {
            try
            {
                var app = GetExcelApplication();
                if (app != null)
                {
                    _activeExcelIntPtr = new IntPtr(app.Hwnd);
                }

                BeginSelectEvent?.Invoke(this, EventArgs.Empty);
                HideParentFormIfNeeded();
                ShowSelectionBox();

                ETLogManager.Info(this, "用户点击选择按钮");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理选择按钮点击事件失败", ex);
                ShowParentFormIfNeeded(); // 确保在出错时恢复父窗体显示
            }
        }

        /// <summary>
        /// 控件大小变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ETRangeSelectControl_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                // 调整分割容器的分割位置，保持选择按钮的固定宽度
                if (splitContainer1 != null && this.Width > 25)
                {
                    splitContainer1.SplitterDistance = this.Width - 21;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理控件大小变化事件失败", ex);
            }
        }

        /// <summary>
        /// 分割容器大小变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void splitContainer1_Resize(object sender, EventArgs e)
        {
            try
            {
                // 保持选择按钮的固定宽度
                if (splitContainer1 != null && splitContainer1.Width > 25)
                {
                    splitContainer1.SplitterDistance = splitContainer1.Width - 21;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理分割容器大小变化事件失败", ex);
            }
        }

        #endregion 事件处理

        #region 私有方法

        /// <summary>
        /// 尝试获取VSTO应用程序实例
        /// </summary>
        /// <returns>VSTO应用程序实例，如果获取失败则返回null</returns>
        /// <remarks>
        /// 专门为HyHelper项目优化的VSTO应用程序获取方法
        /// 优先尝试HyExcelVsto.ThisAddIn.ThisAddInInstance.Application
        /// </remarks>
        private object TryGetVstoApplication()
        {
            try
            {
                ETLogManager.Debug(this, "🔍 开始尝试获取VSTO应用程序实例");

                // 策略1：直接尝试获取HyExcelVsto.ThisAddIn的静态实例
                ETLogManager.Debug(this, "策略1：尝试获取HyExcelVsto.ThisAddIn类型");
                var thisAddInType = System.Type.GetType("HyExcelVsto.ThisAddIn, HyExcelVsto");
                if (thisAddInType != null)
                {
                    ETLogManager.Debug(this, "✅ 成功获取HyExcelVsto.ThisAddIn类型");

                    // 尝试获取ThisAddInInstance静态字段
                    ETLogManager.Debug(this, "尝试获取ThisAddInInstance静态字段");
                    var instanceField = thisAddInType.GetField("ThisAddInInstance",
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (instanceField != null)
                    {
                        ETLogManager.Debug(this, "✅ 找到ThisAddInInstance字段");
                        var instance = instanceField.GetValue(null);
                        if (instance != null)
                        {
                            ETLogManager.Debug(this, "✅ ThisAddInInstance不为null");
                            var appProperty = thisAddInType.GetProperty("Application");
                            if (appProperty != null)
                            {
                                ETLogManager.Debug(this, "✅ 找到Application属性");
                                var app = appProperty.GetValue(instance);
                                if (app != null)
                                {
                                    ETLogManager.Debug(this, "✅ 通过HyExcelVsto.ThisAddIn.Application获取到VSTO应用程序实例");
                                    return app;
                                }
                                else
                                {
                                    ETLogManager.Debug(this, "❌ Application属性值为null");
                                }
                            }
                            else
                            {
                                ETLogManager.Debug(this, "❌ 未找到Application属性");
                            }
                        }
                        else
                        {
                            ETLogManager.Debug(this, "❌ ThisAddInInstance为null");
                        }
                    }
                    else
                    {
                        ETLogManager.Debug(this, "❌ 未找到ThisAddInInstance字段");
                    }

                    // 尝试获取ExcelApplication静态属性
                    ETLogManager.Debug(this, "尝试获取ExcelApplication静态属性");
                    var excelAppProperty = thisAddInType.GetProperty("ExcelApplication",
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (excelAppProperty != null)
                    {
                        ETLogManager.Debug(this, "✅ 找到ExcelApplication属性");
                        var app = excelAppProperty.GetValue(null);
                        if (app != null)
                        {
                            ETLogManager.Debug(this, "✅ 通过HyExcelVsto.ThisAddIn.ExcelApplication获取到VSTO应用程序实例");
                            return app;
                        }
                        else
                        {
                            ETLogManager.Debug(this, "❌ ExcelApplication属性值为null");
                        }
                    }
                    else
                    {
                        ETLogManager.Debug(this, "❌ 未找到ExcelApplication属性");
                    }
                }
                else
                {
                    ETLogManager.Debug(this, "❌ 未找到HyExcelVsto.ThisAddIn类型");
                }

                // 策略2：尝试通过标准的Globals.ThisAddIn方式
                ETLogManager.Debug(this, "策略2：尝试通过标准Globals.ThisAddIn方式");
                try
                {
                    var globalsType = System.Type.GetType("Microsoft.Office.Tools.Excel.Globals, Microsoft.Office.Tools.Excel.v4.0.Framework");
                    if (globalsType != null)
                    {
                        ETLogManager.Debug(this, "✅ 找到Globals类型");
                        var thisAddInProperty = globalsType.GetProperty("ThisAddIn");
                        if (thisAddInProperty != null)
                        {
                            ETLogManager.Debug(this, "✅ 找到ThisAddIn属性");
                            var thisAddIn = thisAddInProperty.GetValue(null);
                            if (thisAddIn != null)
                            {
                                ETLogManager.Debug(this, "✅ ThisAddIn不为null");
                                var applicationProperty = thisAddIn.GetType().GetProperty("Application");
                                if (applicationProperty != null)
                                {
                                    ETLogManager.Debug(this, "✅ 找到Application属性");
                                    var app = applicationProperty.GetValue(thisAddIn);
                                    if (app != null)
                                    {
                                        ETLogManager.Debug(this, "✅ 通过Globals.ThisAddIn.Application获取到VSTO应用程序实例");
                                        return app;
                                    }
                                    else
                                    {
                                        ETLogManager.Debug(this, "❌ Application属性值为null");
                                    }
                                }
                                else
                                {
                                    ETLogManager.Debug(this, "❌ 未找到Application属性");
                                }
                            }
                            else
                            {
                                ETLogManager.Debug(this, "❌ ThisAddIn为null");
                            }
                        }
                        else
                        {
                            ETLogManager.Debug(this, "❌ 未找到ThisAddIn属性");
                        }
                    }
                    else
                    {
                        ETLogManager.Debug(this, "❌ 未找到Globals类型");
                    }
                }
                catch (Exception globalsEx)
                {
                    ETLogManager.Debug(this, $"❌ Globals方式异常: {globalsEx.Message}");
                }

                ETLogManager.Debug(this, "❌ 所有VSTO获取策略都失败");
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, $"尝试获取VSTO应用程序实例时发生错误: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        /// <remarks>通过设置的Excel应用程序提供者获取Excel实例 支持不同的Excel集成环境（VSTO、COM互操作等）</remarks>
        private Microsoft.Office.Interop.Excel.Application GetExcelApplication()
        {
            try
            {
                return _excelProvider?.GetExcelApplication();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "获取Excel应用程序实例失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 显示Excel范围选择对话框
        /// </summary>
        /// <remarks>封装InputBox相关的逻辑到一个方法中，增强WPS兼容性</remarks>
        private void ShowSelectionBox()
        {
            try
            {
                ETLogManager.Debug(this, "开始显示Excel选择对话框");

                var app = GetExcelApplication();
                if (app == null)
                {
                    ETLogManager.Error(this, "🚨 无法获取Excel应用程序实例，无法显示选择对话框");
                    ETLogManager.Error(this, "请检查：1) Excel/WPS是否正常运行 2) VSTO插件是否正确加载");

                    // 显示用户友好的错误提示
                    System.Windows.Forms.MessageBox.Show(
                        "无法连接到Excel/WPS应用程序。\n\n请确保：\n1. Excel或WPS正在运行\n2. 当前工作簿已打开\n3. VSTO插件已正确加载",
                        "连接错误",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Warning);
                    return;
                }

                // 记录成功获取应用程序的信息
                try
                {
                    string appName = app.Name ?? "Unknown";
                    string appVersion = app.Version ?? "Unknown";
                    bool isWps = app.StartupPath?.ToLower().Contains("wps") ?? false;
                    ETLogManager.Info(this, $"✅ 成功获取应用程序实例 - 名称: {appName}, 版本: {appVersion}, WPS环境: {isWps}");
                }
                catch
                {
                    ETLogManager.Info(this, "✅ 成功获取应用程序实例（无法获取详细信息）");
                }

                // 调用Excel的InputBox进行范围选择
                ETLogManager.Debug(this, "调用InputBox进行范围选择");
                dynamic input = app.InputBox(
                    InputPromptText,
                    "选择Excel范围",
                    Type: 8); // Type=8表示Range类型

                // 检查用户是否选择了有效范围
                SelectedRange = input is Range range ? range : null;

                ValidateInput();

                ETLogManager.Info(this, $"选择对话框完成，结果：{(SelectedRange != null ? "成功" : "取消或无效")}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "显示选择对话框失败", ex);
                ETLogManager.Error(this, $"错误详情: {ex.Message}");
                ETLogManager.Error(this, $"堆栈跟踪: {ex.StackTrace}");

                SelectedRange = null;
                ValidateInput();
            }
        }

        /// <summary>
        /// 验证并处理用户输入
        /// </summary>
        /// <remarks>验证并处理用户输入（代码重构）</remarks>
        private void ValidateInput()
        {
            try
            {
                if (SelectedRange != null)
                {
                    // 选择有效，更新显示
                    textBox地址.Text = FullSelectedAddress;
                    textBox地址.ForeColor = SystemColors.WindowText;
                    ETLogManager.Info(this, $"选择有效范围：{FullSelectedAddress}");
                }
                else
                {
                    // 选择无效，显示错误状态
                    textBox地址.Text = "地址无效";
                    textBox地址.ForeColor = SystemColors.ControlLight;
                    ETLogManager.Warning(this, "选择的范围无效");
                }

                // 更新上次地址记录
                _previousAddress = textBox地址.Text;

                // 触发选择完成事件
                SelectedEvent?.Invoke(SelectedRange, EventArgs.Empty);

                // 恢复Excel窗口和父窗体
                ActiveExcelApp();
                ShowParentFormIfNeeded();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "验证输入失败", ex);
            }
        }

        /// <summary>
        /// 内部验证方法，只触发事件，不执行UI操作
        /// </summary>
        /// <remarks>用于程序化设置SelectedRange时触发事件，避免不必要的UI操作</remarks>
        private void ValidateInputInternal()
        {
            try
            {
                // 只触发选择完成事件，不执行其他UI操作
                SelectedEvent?.Invoke(SelectedRange, EventArgs.Empty);
                ETLogManager.Debug(this, $"内部触发SelectedEvent事件：{SelectedRange?.GetFullAddress() ?? "null"}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "内部验证失败", ex);
            }
        }

        /// <summary>
        /// 激活Excel应用程序窗口
        /// </summary>
        /// <remarks>将Excel窗口置于前台</remarks>
        private void ActiveExcelApp()
        {
            try
            {
                if (_activeExcelIntPtr != IntPtr.Zero)
                {
                    // 这里可以使用Windows API将Excel窗口置于前台 具体实现可能需要引用user32.dll的SetForegroundWindow方法
                    ETLogManager.Debug(this, "尝试激活Excel应用程序窗口");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "激活Excel应用程序失败", ex);
            }
        }

        /// <summary>
        /// 根据需要隐藏父窗体
        /// </summary>
        private void HideParentFormIfNeeded()
        {
            if (!HideParentForm || ParentForm == null)
            {
                return;
            }

            try
            {
                _parentFormHeight = ParentForm.Height;
                ParentForm.Height = 0;
                ETLogManager.Debug(this, $"隐藏父窗体，原高度：{_parentFormHeight}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "隐藏父窗体失败", ex);
            }
        }

        /// <summary>
        /// 根据需要显示父窗体
        /// </summary>
        private void ShowParentFormIfNeeded()
        {
            if (ParentForm == null)
            {
                return;
            }

            try
            {
                if (_parentFormHeight > 0)
                {
                    ParentForm.Height = _parentFormHeight;
                    ETLogManager.Debug(this, $"恢复父窗体显示，高度：{_parentFormHeight}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "显示父窗体失败", ex);
            }
        }

        /// <summary>
        /// 设置选中的Range对象
        /// </summary>
        /// <param name="value">新的Range对象</param>
        /// <param name="triggerEvent">是否触发SelectedEvent事件，默认为true</param>
        private void SetSelectedRange(Range value, bool triggerEvent = true)
        {
            try
            {
                _range = value;
                UpdateTextBoxAddress(value);

                // 如果需要触发事件，则调用ValidateInputInternal来触发SelectedEvent
                if (triggerEvent)
                {
                    ValidateInputInternal();
                }

                ETLogManager.Debug(this, $"设置选中范围：{value?.GetFullAddress() ?? "null"}，触发事件：{triggerEvent}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置选中范围失败", ex);
            }
        }

        /// <summary>
        /// 更新文本框地址显示
        /// </summary>
        /// <param name="value">Range对象</param>
        /// <remarks>根据Range对象更新文本框的显示内容和颜色</remarks>
        private void UpdateTextBoxAddress(Range value)
        {
            try
            {
                if (value == null)
                {
                    textBox地址.Text = string.Empty;
                    textBox地址.ForeColor = SystemColors.ControlLight;
                }
                else
                {
                    textBox地址.Text = value.GetFullAddress();
                    textBox地址.ForeColor = SystemColors.WindowText;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "更新文本框地址显示失败", ex);
                textBox地址.Text = "地址无效";
                textBox地址.ForeColor = SystemColors.ControlLight;
            }
        }

        #endregion 私有方法
    }
}