using System;
using System.Collections.Generic;

namespace ExtensionsTools.ETAIv2.Models
{
    /// <summary>
    /// AI响应结果
    /// </summary>
    public class AIResponse
    {
        public string RequestId { get; set; }               // 请求ID
        public bool Success { get; set; }                   // 是否成功
        public string ErrorMessage { get; set; }            // 错误信息
        public List<GroupResult> Results { get; set; }      // 结果列表
        public TimeSpan ProcessingTime { get; set; }        // 处理时间
        public string ApiType { get; set; }                 // 使用的API类型
        public Dictionary<string, object> Metadata { get; set; } // 元数据
        public DateTime CompletedAt { get; set; }           // 完成时间
        public int TotalTokensUsed { get; set; }            // 总令牌使用量

        public AIResponse()
        {
            Results = new List<GroupResult>();
            Metadata = new Dictionary<string, object>();
            CompletedAt = DateTime.Now;
        }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        public static AIResponse CreateSuccess(string requestId, List<GroupResult> results, string apiType = "chat")
        {
            return new AIResponse
            {
                RequestId = requestId,
                Success = true,
                Results = results ?? new List<GroupResult>(),
                ApiType = apiType
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        public static AIResponse CreateFailure(string requestId, string errorMessage, string apiType = "chat")
        {
            return new AIResponse
            {
                RequestId = requestId,
                Success = false,
                ErrorMessage = errorMessage,
                Results = new List<GroupResult>(),
                ApiType = apiType
            };
        }
    }

    /// <summary>
    /// 组处理结果
    /// </summary>
    public class GroupResult
    {
        public string GroupId { get; set; }                 // 组ID
        public Dictionary<string, object> Values { get; set; } // 回填值
        public string ProcessingInfo { get; set; }          // 处理信息
        public float Confidence { get; set; }               // 置信度
        public List<SourceInfo> Sources { get; set; }       // 数据来源信息
        public DateTime ProcessedAt { get; set; }           // 处理时间
        public int TokensUsed { get; set; }                 // 使用的令牌数

        public GroupResult()
        {
            Values = new Dictionary<string, object>();
            Sources = new List<SourceInfo>();
            ProcessedAt = DateTime.Now;
            Confidence = 1.0f;
        }

        /// <summary>
        /// 添加回填值
        /// </summary>
        public void AddValue(string cellAddress, object value)
        {
            if (!string.IsNullOrEmpty(cellAddress) && value != null)
            {
                Values[cellAddress] = value;
            }
        }

        /// <summary>
        /// 添加数据来源
        /// </summary>
        public void AddSource(string type, string reference, string fileId = null, string fileName = null)
        {
            Sources.Add(new SourceInfo
            {
                Type = type,
                Reference = reference,
                FileId = fileId,
                FileName = fileName
            });
        }
    }

    /// <summary>
    /// 数据来源信息
    /// </summary>
    public class SourceInfo
    {
        public string Type { get; set; }                    // 来源类型
        public string Reference { get; set; }               // 引用信息
        public string FileId { get; set; }                  // 文件ID
        public string FileName { get; set; }                // 文件名
        public DateTime CreatedAt { get; set; }             // 创建时间

        public SourceInfo()
        {
            CreatedAt = DateTime.Now;
        }
    }

    /// <summary>
    /// 批处理响应（兼容现有接口）
    /// </summary>
    public class BatchProcessResponse
    {
        public object Response { get; set; }                // 响应对象
        public bool IsQuestionRow { get; set; }             // 是否为问题行
        public object RequestJson { get; set; }             // 请求JSON
        public bool Success { get; set; }                   // 是否成功
        public string ErrorMessage { get; set; }            // 错误信息

        /// <summary>
        /// 从AIResponse转换
        /// </summary>
        public static BatchProcessResponse FromAIResponse(AIResponse aiResponse)
        {
            return new BatchProcessResponse
            {
                Response = aiResponse,
                IsQuestionRow = true,
                Success = aiResponse.Success,
                ErrorMessage = aiResponse.ErrorMessage,
                RequestJson = new
                {
                    requestId = aiResponse.RequestId,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    apiType = aiResponse.ApiType
                }
            };
        }
    }
}
