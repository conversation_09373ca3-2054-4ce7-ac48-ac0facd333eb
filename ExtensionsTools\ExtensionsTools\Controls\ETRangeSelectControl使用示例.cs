using System;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using ExtensionsTools.ETExcel.Controls;

namespace ExtensionsTools.ETExcel.Controls.Examples
{
    /// <summary>
    /// ETRangeSelectControl 使用示例
    /// 演示如何在不同环境中使用 ETRangeSelectControl
    /// </summary>
    public partial class ETRangeSelectControlExample : Form
    {
        private ETRangeSelectControl rangeControl;
        private Button btnGetRange;
        private Button btnSetRange;
        private TextBox txtResult;

        public ETRangeSelectControlExample()
        {
            InitializeComponent();
            SetupRangeControl();
        }

        /// <summary>
        /// 设置范围选择控件
        /// </summary>
        private void SetupRangeControl()
        {
            // 创建控件实例
            rangeControl = new ETRangeSelectControl();
            rangeControl.Location = new System.Drawing.Point(12, 12);
            rangeControl.Size = new System.Drawing.Size(300, 21);
            rangeControl.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            // 设置Excel应用程序提供者
            // 方式1：VSTO环境（需要Globals.ThisAddIn可用）
            // rangeControl.SetExcelApplicationProvider(
            //     new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));

            // 方式2：COM互操作环境（使用默认提供者）
            // rangeControl 会自动使用 DefaultExcelApplicationProvider

            // 方式3：自定义提供者
            rangeControl.SetExcelApplicationProvider(new CustomExcelApplicationProvider());

            // 设置控件属性
            rangeControl.InputPromptText = "请选择Excel范围：";
            rangeControl.HideParentForm = true;
            rangeControl.EnableEnterThenSelect = false;

            // 绑定事件
            rangeControl.SelectedEvent += RangeControl_SelectedEvent;
            rangeControl.BeginSelectEvent += RangeControl_BeginSelectEvent;
            rangeControl.EnterEvent += RangeControl_EnterEvent;

            // 添加到窗体
            this.Controls.Add(rangeControl);
        }

        /// <summary>
        /// 范围选择完成事件
        /// </summary>
        private void RangeControl_SelectedEvent(object sender, EventArgs e)
        {
            try
            {
                var range = rangeControl.SelectedRange;
                if (range != null)
                {
                    txtResult.Text = $"选择的范围：{range.Address}\r\n" +
                                   $"工作表：{range.Worksheet.Name}\r\n" +
                                   $"行数：{range.Rows.Count}\r\n" +
                                   $"列数：{range.Columns.Count}\r\n" +
                                   $"完整地址：{rangeControl.FullSelectedAddress}";
                }
                else
                {
                    txtResult.Text = "未选择有效范围";
                }
            }
            catch (Exception ex)
            {
                txtResult.Text = $"处理选择事件时出错：{ex.Message}";
            }
        }

        /// <summary>
        /// 开始选择事件
        /// </summary>
        private void RangeControl_BeginSelectEvent(object sender, EventArgs e)
        {
            txtResult.Text = "正在选择范围...";
        }

        /// <summary>
        /// 文本框获得焦点事件
        /// </summary>
        private void RangeControl_EnterEvent(object sender, EventArgs e)
        {
            txtResult.Text = "范围选择控件获得焦点";
        }

        /// <summary>
        /// 获取当前选择的范围
        /// </summary>
        private void BtnGetRange_Click(object sender, EventArgs e)
        {
            try
            {
                var range = rangeControl.SelectedRange;
                if (range != null)
                {
                    MessageBox.Show($"当前选择的范围：{range.Address}", "范围信息", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("当前未选择任何范围", "范围信息", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取范围时出错：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 程序化设置范围
        /// </summary>
        private void BtnSetRange_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取Excel应用程序
                var app = GetExcelApplication();
                if (app == null)
                {
                    MessageBox.Show("无法获取Excel应用程序实例", "错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取活动工作表的A1:C10范围
                var activeSheet = app.ActiveSheet as Worksheet;
                if (activeSheet != null)
                {
                    var range = activeSheet.Range["A1:C10"];
                    
                    // 设置范围并触发事件
                    rangeControl.SetSelectedRangeWithEvent(range, true);
                    
                    MessageBox.Show("已设置范围为 A1:C10", "设置完成", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("无法获取活动工作表", "错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置范围时出错：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        private Microsoft.Office.Interop.Excel.Application GetExcelApplication()
        {
            try
            {
                // 方式1：通过COM互操作获取活动Excel实例
                return (Microsoft.Office.Interop.Excel.Application)
                    System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");

                // 方式2：在VSTO环境中
                // return Globals.ThisAddIn.Application;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 初始化窗体控件
        /// </summary>
        private void InitializeComponent()
        {
            this.btnGetRange = new Button();
            this.btnSetRange = new Button();
            this.txtResult = new TextBox();
            this.SuspendLayout();

            // btnGetRange
            this.btnGetRange.Location = new System.Drawing.Point(12, 50);
            this.btnGetRange.Size = new System.Drawing.Size(100, 30);
            this.btnGetRange.Text = "获取范围";
            this.btnGetRange.UseVisualStyleBackColor = true;
            this.btnGetRange.Click += new EventHandler(this.BtnGetRange_Click);

            // btnSetRange
            this.btnSetRange.Location = new System.Drawing.Point(130, 50);
            this.btnSetRange.Size = new System.Drawing.Size(100, 30);
            this.btnSetRange.Text = "设置范围";
            this.btnSetRange.UseVisualStyleBackColor = true;
            this.btnSetRange.Click += new EventHandler(this.BtnSetRange_Click);

            // txtResult
            this.txtResult.Location = new System.Drawing.Point(12, 100);
            this.txtResult.Multiline = true;
            this.txtResult.ScrollBars = ScrollBars.Vertical;
            this.txtResult.Size = new System.Drawing.Size(360, 200);
            this.txtResult.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // Form
            this.ClientSize = new System.Drawing.Size(384, 321);
            this.Controls.Add(this.btnGetRange);
            this.Controls.Add(this.btnSetRange);
            this.Controls.Add(this.txtResult);
            this.Text = "ETRangeSelectControl 使用示例";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }

    /// <summary>
    /// 自定义Excel应用程序提供者示例
    /// </summary>
    public class CustomExcelApplicationProvider : IExcelApplicationProvider
    {
        public Microsoft.Office.Interop.Excel.Application GetExcelApplication()
        {
            try
            {
                // 自定义的Excel应用程序获取逻辑
                // 这里可以实现特定的获取策略
                return (Microsoft.Office.Interop.Excel.Application)
                    System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
            }
            catch
            {
                return null;
            }
        }
    }
}
