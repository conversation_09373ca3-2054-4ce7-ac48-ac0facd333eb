﻿namespace HyExcelVsto.Module.WX
{
    partial class frm51Helper
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.button上传预算 = new System.Windows.Forms.Button();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.button下载交付列表并更新 = new System.Windows.Forms.Button();
            this.button下载任务管理并更新 = new System.Windows.Forms.Button();
            this.button空白表模 = new System.Windows.Forms.Button();
            this.button设置存储目录 = new System.Windows.Forms.Button();
            this.button上传安全交底 = new System.Windows.Forms.Button();
            this.button上传图纸 = new System.Windows.Forms.Button();
            this.button批量换点 = new System.Windows.Forms.Button();
            this.button一键审批通过 = new System.Windows.Forms.Button();
            this.button登录 = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // textBoxLog
            // 
            this.textBoxLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBoxLog.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxLog.Location = new System.Drawing.Point(0, 0);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Size = new System.Drawing.Size(982, 418);
            this.textBoxLog.TabIndex = 1;
            this.textBoxLog.Text = "## 登录过程可能不能及时获取到信息，有输出账号姓名才算登录成功。\r\n## 可以通过生成空白表模创建模板表\r\n## 注意事项：\r\n\t- 列顺序固定，不能变更\r\n\t" +
    "- 只能选择数据区域，不能包含表头，交付编号为空将跳过\r\n\r\n----------------------------------------";
            // 
            // button上传预算
            // 
            this.button上传预算.Location = new System.Drawing.Point(212, 12);
            this.button上传预算.Name = "button上传预算";
            this.button上传预算.Size = new System.Drawing.Size(94, 33);
            this.button上传预算.TabIndex = 3;
            this.button上传预算.Text = "上传预算";
            this.button上传预算.UseVisualStyleBackColor = true;
            this.button上传预算.Click += new System.EventHandler(this.button上传预算_Click);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer1.IsSplitterFixed = true;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.button下载交付列表并更新);
            this.splitContainer1.Panel1.Controls.Add(this.button下载任务管理并更新);
            this.splitContainer1.Panel1.Controls.Add(this.button空白表模);
            this.splitContainer1.Panel1.Controls.Add(this.button设置存储目录);
            this.splitContainer1.Panel1.Controls.Add(this.button上传安全交底);
            this.splitContainer1.Panel1.Controls.Add(this.button上传图纸);
            this.splitContainer1.Panel1.Controls.Add(this.button批量换点);
            this.splitContainer1.Panel1.Controls.Add(this.button一键审批通过);
            this.splitContainer1.Panel1.Controls.Add(this.button上传预算);
            this.splitContainer1.Panel1.Controls.Add(this.button登录);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.textBoxLog);
            this.splitContainer1.Size = new System.Drawing.Size(982, 517);
            this.splitContainer1.SplitterDistance = 95;
            this.splitContainer1.TabIndex = 4;
            // 
            // button下载交付列表并更新
            // 
            this.button下载交付列表并更新.Location = new System.Drawing.Point(12, 51);
            this.button下载交付列表并更新.Name = "button下载交付列表并更新";
            this.button下载交付列表并更新.Size = new System.Drawing.Size(194, 33);
            this.button下载交付列表并更新.TabIndex = 5;
            this.button下载交付列表并更新.Text = "下载 交付列表 并更新51台账";
            this.button下载交付列表并更新.UseVisualStyleBackColor = true;
            this.button下载交付列表并更新.Click += new System.EventHandler(this.button下载交付列表并更新_Click);
            // 
            // button下载任务管理并更新
            // 
            this.button下载任务管理并更新.Location = new System.Drawing.Point(212, 51);
            this.button下载任务管理并更新.Name = "button下载任务管理并更新";
            this.button下载任务管理并更新.Size = new System.Drawing.Size(194, 33);
            this.button下载任务管理并更新.TabIndex = 4;
            this.button下载任务管理并更新.Text = "下载 任务管理 并更新51台账";
            this.button下载任务管理并更新.UseVisualStyleBackColor = true;
            this.button下载任务管理并更新.Click += new System.EventHandler(this.button下载任务管理并更新_Click);
            // 
            // button空白表模
            // 
            this.button空白表模.Location = new System.Drawing.Point(876, 12);
            this.button空白表模.Name = "button空白表模";
            this.button空白表模.Size = new System.Drawing.Size(94, 33);
            this.button空白表模.TabIndex = 3;
            this.button空白表模.Text = "空白表模";
            this.button空白表模.UseVisualStyleBackColor = true;
            this.button空白表模.Click += new System.EventHandler(this.button空白表模_Click);
            //
            // button设置存储目录
            //
            this.button设置存储目录.Location = new System.Drawing.Point(876, 51);
            this.button设置存储目录.Name = "button设置存储目录";
            this.button设置存储目录.Size = new System.Drawing.Size(94, 33);
            this.button设置存储目录.TabIndex = 7;
            this.button设置存储目录.Text = "设置存储目录";
            this.button设置存储目录.UseVisualStyleBackColor = true;
            this.button设置存储目录.Click += new System.EventHandler(this.button设置存储目录_Click);
            //
            // button上传安全交底
            // 
            this.button上传安全交底.Location = new System.Drawing.Point(112, 12);
            this.button上传安全交底.Name = "button上传安全交底";
            this.button上传安全交底.Size = new System.Drawing.Size(94, 33);
            this.button上传安全交底.TabIndex = 3;
            this.button上传安全交底.Text = "上传设计交底";
            this.button上传安全交底.UseVisualStyleBackColor = true;
            this.button上传安全交底.Click += new System.EventHandler(this.button上传设计交底_Click);
            // 
            // button上传图纸
            // 
            this.button上传图纸.Location = new System.Drawing.Point(12, 12);
            this.button上传图纸.Name = "button上传图纸";
            this.button上传图纸.Size = new System.Drawing.Size(94, 33);
            this.button上传图纸.TabIndex = 3;
            this.button上传图纸.Text = "上传图纸";
            this.button上传图纸.UseVisualStyleBackColor = true;
            this.button上传图纸.Click += new System.EventHandler(this.button上传图纸_Click);
            // 
            // button批量换点
            // 
            this.button批量换点.Location = new System.Drawing.Point(312, 12);
            this.button批量换点.Name = "button批量换点";
            this.button批量换点.Size = new System.Drawing.Size(94, 33);
            this.button批量换点.TabIndex = 3;
            this.button批量换点.Text = "批量换点";
            this.button批量换点.UseVisualStyleBackColor = true;
            this.button批量换点.Click += new System.EventHandler(this.button批量换点_Click);
            // 
            // button一键审批通过
            // 
            this.button一键审批通过.Location = new System.Drawing.Point(412, 12);
            this.button一键审批通过.Name = "button一键审批通过";
            this.button一键审批通过.Size = new System.Drawing.Size(94, 33);
            this.button一键审批通过.TabIndex = 3;
            this.button一键审批通过.Text = "一键审批";
            this.button一键审批通过.UseVisualStyleBackColor = true;
            this.button一键审批通过.Click += new System.EventHandler(this.button一键审批通过_Click);
            // 
            // button登录
            // 
            this.button登录.Location = new System.Drawing.Point(776, 12);
            this.button登录.Name = "button登录";
            this.button登录.Size = new System.Drawing.Size(94, 33);
            this.button登录.TabIndex = 6;
            this.button登录.Text = "登录";
            this.button登录.UseVisualStyleBackColor = true;
            this.button登录.Click += new System.EventHandler(this.button登录_Click);
            // 
            // frm51Helper
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(982, 517);
            this.Controls.Add(this.splitContainer1);
            this.Name = "frm51Helper";
            this.Text = "51Helper";
            this.Load += new System.EventHandler(this.frm51Helper_Load);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.TextBox textBoxLog;
        private System.Windows.Forms.Button button上传预算;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.Button button上传图纸;
        private System.Windows.Forms.Button button上传安全交底;
        private System.Windows.Forms.Button button一键审批通过;
        private System.Windows.Forms.Button button空白表模;
        private System.Windows.Forms.Button button批量换点;
        private System.Windows.Forms.Button button下载任务管理并更新;
        private System.Windows.Forms.Button button下载交付列表并更新;
        private System.Windows.Forms.Button button登录;
        private System.Windows.Forms.Button button设置存储目录;
    }
}