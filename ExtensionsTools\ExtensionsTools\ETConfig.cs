﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;

namespace ET
{
    /// <summary>
    /// ET全局配置类
    /// </summary>
    public static class ETConfig
    {
        /// <summary>
        /// INI配置文件名
        /// </summary>
        public const string INI_FILE_NAME = "ETConfig.ini";

        /// <summary>
        /// 用户组信息键名
        /// </summary>
        public const string USER_GROUPS_KEY = "UserGroups";

        /// <summary>
        /// SMB服务器地址键名
        /// </summary>
        public const string SMB_SERVER_ADDRESS_KEY = "SmbServerAddress";

        /// <summary>
        /// 软件配置节名
        /// </summary>
        public const string CONFIG_SECTION = "Software";

        /// <summary>
        /// 通用配置节名，用于存储不需要区分用户的配置项
        /// </summary>
        public const string COMMON_SECTION = "Common";

        /// <summary>
        /// 配置文件夹名称
        /// </summary>
        public const string CONFIG_FOLDER_NAME = "config";

        /// <summary>
        /// 获取INI配置文件完整路径
        /// </summary>
        /// <returns>INI配置文件路径</returns>
        public static string GetETConfigIniFilePath()
        {
            return GetConfigDirectory(INI_FILE_NAME, ".et");
        }

        /// <summary>
        /// 获取当前Addin的配置文件路径
        /// </summary>
        /// <param name="configFileName">配置文件全名</param>
        /// <param name="subDirectory">子目录名称（可选）</param>
        /// <returns>处理后的配置文件完整路径（位于config目录或其子目录下）</returns>
        public static string GetConfigDirectory(string configFileName = null, string subDirectory = null)
        {
            if (string.IsNullOrEmpty(configFileName)) configFileName = string.Empty;
            return ETFile.ApplicationPath(configFileName, PathType.Config, subDirectory);
        }

        /// <summary>
        /// 将配置文件内容读取到字典中
        /// </summary>
        /// <param name="fileName">配置文件名</param>
        /// <returns>字典对象</returns>
        public static Dictionary<string, string[]> ConfigFileToDictionary(string fileName)
        {
            Dictionary<string, List<string>> tempResult = [];
            string configPath = GetConfigDirectory(fileName);

            if (File.Exists(configPath))
            {
                try
                {
                    string[] lines = File.ReadAllLines(configPath, Encoding.UTF8);
                    string currentKey = string.Empty;

                    foreach (string line in lines)
                    {
                        string trimmedLine = line.Trim();
                        if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#") || trimmedLine.StartsWith("//"))
                            continue;

                        if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                        {
                            currentKey = trimmedLine.Substring(1, trimmedLine.Length - 2);
                            if (!tempResult.ContainsKey(currentKey))
                            {
                                tempResult[currentKey] = [];
                            }
                        }
                        else if (!string.IsNullOrEmpty(currentKey))
                        {
                            tempResult[currentKey].Add(trimmedLine);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"读取配置文件时出错: {ex.Message}");
                }
            }

            // 转换为返回类型 Dictionary<string, string[]>
            Dictionary<string, string[]> result = [];
            foreach (KeyValuePair<string, List<string>> kvp in tempResult)
            {
                result[kvp.Key] = kvp.Value.ToArray();
            }

            return result;
        }

        /// <summary>
        /// 打开配置文件
        /// </summary>
        /// <param name="fileName">配置文件名</param>
        /// <returns>是否成功打开</returns>
        public static bool OpenConfigFile(string fileName)
        {
            string configPath = GetConfigDirectory(fileName);
            if (File.Exists(configPath))
            {
                try
                {
                    Process.Start(configPath);
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"打开配置文件时出错: {ex.Message}");
                }
            }
            else
            {
                try
                {
                    File.WriteAllText(configPath, "# 配置文件\r\n", Encoding.UTF8);
                    Process.Start(configPath);
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"创建并打开配置文件时出错: {ex.Message}");
                }
            }

            return false;
        }
    }
}