using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using ET;
using HyExcelVsto.Module.Common.StationDataProcessor.Core;
using HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces;
using ET.Controls;
using HyExcelVsto.Module.Common.StationDataProcessor;
using ExtensionsTools;
using System.IO;

// 类型别名，解决命名空间冲突
using WinFormsAction = System.Action;
using WinFormsButton = System.Windows.Forms.Button;
using WinFormsCheckBox = System.Windows.Forms.CheckBox;
using WinFormsGroupBox = System.Windows.Forms.GroupBox;
using WinFormsLabel = System.Windows.Forms.Label;
using DrawingPoint = System.Drawing.Point;
using DrawingSize = System.Drawing.Size;

using System.Linq;
using System.Diagnostics;

// 类型别名，简化长类型名称
using CoreProgressEventArgs = HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProgressEventArgs;

namespace HyExcelVsto.Module.WX.StationDataProcessor
{
    /// <summary>
    /// 基站数据处理器独立窗体 提供用户友好的界面进行基站数据处理操作
    /// </summary>
    public partial class StationDataProcessorForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 当前工作表
        /// </summary>
        private Worksheet _currentWorksheet;

        /// <summary>
        /// 处理是否正在进行
        /// </summary>
        private bool _isProcessing = false;

        /// <summary>
        /// 处理是否成功完成（用于控制进度条显示）
        /// </summary>
        private bool _processingCompletedSuccessfully = false;

        /// <summary>
        /// 当前选择的配置文件名称
        /// </summary>
        private string _currentConfigFileName = "StationDataProcessor_4G.config";

        #endregion 私有字段

        #region 额外控件字段

        private WinFormsLabel lblCurrentStep;
        private WinFormsLabel lblStatus;

        #endregion 额外控件字段

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StationDataProcessorForm()
        {
            InitializeComponent();
            InitializeAdditionalControls();
            LoadConfigFileList();
            SetDefaultDataRange();

            ETLogManager.Info(this, "StationDataProcessorForm初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化额外控件
        /// </summary>
        private void InitializeAdditionalControls()
        {
            // 当前步骤标签 - 调整位置，因为移除了处理选项组合框
            this.lblCurrentStep = new WinFormsLabel();
            this.lblCurrentStep.Text = "准备就绪";
            this.lblCurrentStep.Location = new DrawingPoint(12, 120);
            this.lblCurrentStep.Size = new DrawingSize(760, 20);
            this.lblCurrentStep.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            // 状态标签 - 调整位置
            this.lblStatus = new WinFormsLabel();
            this.lblStatus.Text = "就绪";
            this.lblStatus.Location = new DrawingPoint(120, 176);
            this.lblStatus.Size = new DrawingSize(200, 20);

            // 添加额外控件到窗体
            this.Controls.Add(this.lblCurrentStep);
            this.Controls.Add(this.lblStatus);

            // 设置ETRangeSelectControl的Excel应用程序提供者
            etRangeSelectControl.SetExcelApplicationProvider(
                new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));

            // 绑定事件
            this.etRangeSelectControl.SelectedEvent += EtRangeSelectControl_SelectedEvent;
        }

        /// <summary>
        /// 设置默认数据区域
        /// </summary>
        /// <remarks>窗体加载时自动设置etRangeSelectControl的值为当前工作表的有效数据区域 使用Excel的UsedRange属性获取有效数据区域</remarks>
        private void SetDefaultDataRange()
        {
            try
            {
                // 获取当前活动工作表
                var activeSheet = Globals.ThisAddIn.Application.ActiveSheet as Worksheet;
                if (activeSheet == null)
                {
                    ETLogManager.Warning(this, "未找到活动工作表，无法设置默认数据区域");
                    return;
                }

                // 获取工作表的有效数据区域
                Range usedRange = activeSheet.UsedRange;
                if (usedRange == null)
                {
                    ETLogManager.Warning(this, $"工作表 '{activeSheet.Name}' 没有有效数据区域");
                    return;
                }

                // 使用ETRangeSelectControl的新方法设置默认值，并自动触发事件 这样可以确保_currentWorksheet被正确设置，并且界面状态得到更新
                etRangeSelectControl.SetSelectedRangeWithEvent(usedRange, true);

                ETLogManager.Info(this, $"已设置默认数据区域：工作表 '{activeSheet.Name}'，范围 '{usedRange.Address}'");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "设置默认数据区域失败", ex);
                // 不抛出异常，允许窗体正常加载
            }
        }

        /// <summary>
        /// 加载配置文件列表
        /// </summary>
        private void LoadConfigFileList()
        {
            try
            {
                // 使用Helper类获取配置文件列表
                var configFiles = StationDataProcessorHelper.GetAvailableConfigFiles();

                ETLogManager.Info(this, $"找到 {configFiles.Count} 个配置文件");

                // 清空并填充下拉列表
                cmbConfigFiles.Items.Clear();
                cmbConfigFiles.Items.AddRange(configFiles.ToArray());

                // 设置默认选择
                string defaultConfigName = Path.GetFileNameWithoutExtension(_currentConfigFileName);
                if (configFiles.Contains(defaultConfigName, StringComparer.OrdinalIgnoreCase))
                {
                    cmbConfigFiles.SelectedItem = defaultConfigName;
                    _currentConfigFileName = defaultConfigName;
                }
                else if (configFiles.Count > 0)
                {
                    cmbConfigFiles.SelectedIndex = 0;
                    _currentConfigFileName = configFiles[0];
                }

                ETLogManager.Info(this, $"当前选择的配置文件：{_currentConfigFileName}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "加载配置文件列表失败", ex);
                MessageBox.Show($"加载配置文件列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 范围选择事件处理
        /// </summary>
        private void EtRangeSelectControl_SelectedEvent(object sender, EventArgs e)
        {
            try
            {
                var selectedRange = etRangeSelectControl.SelectedRange;
                if (selectedRange != null)
                {
                    _currentWorksheet = selectedRange.Worksheet;
                    etLogDisplayControl.WriteInfo($"已选择工作表：{_currentWorksheet.Name}，范围：{selectedRange.Address}");

                    // 更新按钮状态
                    btnProcess.Enabled = !_isProcessing;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "处理范围选择事件失败", ex);
                etLogDisplayControl.WriteError("选择范围失败", ex);
            }
        }

        /// <summary>
        /// 开始处理按钮点击事件
        /// </summary>
        private async void BtnProcess_Click(object sender, EventArgs e)
        {
            if (_isProcessing)
            {
                ShowUserFriendlyMessage("数据处理正在进行中，请稍候...", "提示", MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 创建线程安全的委托
                var messageHandler = StationDataProcessorHelper.CreateThreadSafeMessageHandler(this);

                // 基本验证：确保有工作表和数据范围
                if (!StationDataProcessorHelper.ValidateBasicWorksheetAndRange(_currentWorksheet, messageHandler))
                {
                    etRangeSelectControl.Focus();
                    FlashControl(etRangeSelectControl);
                    return;
                }

                if (etRangeSelectControl.SelectedRange == null)
                {
                    messageHandler?.Invoke("请选择有效的数据范围", "数据验证", MessageBoxIcon.Warning);
                    etRangeSelectControl.Focus();
                    FlashControl(etRangeSelectControl);
                    return;
                }

                // 确认处理操作
                if (!StationDataProcessorHelper.ConfirmProcessingOperation(etRangeSelectControl.SelectedRange, this))
                {
                    etLogDisplayControl.WriteInfo("用户取消了处理操作");
                    return;
                }

                // 开始处理（详细验证在 ProcessStationDataAsync 中进行）
                await StartProcessingAsync();
            }
            catch (Exception ex)
            {
                HandleProcessingException(ex);
            }
            finally
            {
                // 只有在处理未成功完成时才重置状态（避免隐藏成功完成时的进度条）
                if (!_processingCompletedSuccessfully)
                {
                    SetProcessingState(false);
                }
                // 重置成功完成标志，为下次处理做准备
                _processingCompletedSuccessfully = false;
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_isProcessing)
            {
                // 取消正在进行的处理
                try
                {
                    SetProcessingState(false);
                    etLogDisplayControl.WriteWarning("用户取消了处理操作");
                    ETLogManager.Warning(this, "用户取消了数据处理操作");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(this, "取消处理时发生错误", ex);
                    etLogDisplayControl.WriteError("取消处理失败", ex);
                }
            }
            else
            {
                this.Close();
            }
        }

        /// <summary>
        /// 配置文件选择变更事件处理
        /// </summary>
        private void CmbConfigFiles_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbConfigFiles.SelectedItem != null)
                {
                    string selectedConfigFile = cmbConfigFiles.SelectedItem.ToString();
                    if (selectedConfigFile != _currentConfigFileName)
                    {
                        _currentConfigFileName = selectedConfigFile;
                        ETLogManager.Info(this, $"配置文件已切换为：{_currentConfigFileName}");
                        etLogDisplayControl.WriteInfo($"已切换到配置文件：{_currentConfigFileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "切换配置文件失败", ex);
                MessageBox.Show($"切换配置文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开配置目录按钮点击事件处理
        /// </summary>
        private void BtnOpenConfigDir_Click(object sender, EventArgs e)
        {
            try
            {
                StationDataProcessorHelper.OpenConfigDirectory();
                etLogDisplayControl.WriteInfo("已打开配置目录");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开配置目录失败", ex);
                MessageBox.Show($"打开配置目录失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件处理

        #region 用户体验优化方法

        /// <summary>
        /// 显示用户友好的消息框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="icon">图标</param>
        private void ShowUserFriendlyMessage(string message, string title, MessageBoxIcon icon)
        {
            // 确保在UI线程中显示消息
            if (this.InvokeRequired)
            {
                this.Invoke(new WinFormsAction(() => ShowUserFriendlyMessage(message, title, icon)));
                return;
            }

            MessageBox.Show(this, message, title, MessageBoxButtons.OK, icon);
        }

        /// <summary>
        /// 闪烁控件以提示用户注意
        /// </summary>
        /// <param name="control">要闪烁的控件</param>
        private async void FlashControl(Control control)
        {
            if (control == null) return;

            var originalBackColor = control.BackColor;
            var flashColor = Color.LightYellow;

            try
            {
                // 闪烁3次
                for (int i = 0; i < 3; i++)
                {
                    control.BackColor = flashColor;
                    await Task.Delay(200);
                    control.BackColor = originalBackColor;
                    await Task.Delay(200);
                }
            }
            catch
            {
                // 确保恢复原始颜色
                control.BackColor = originalBackColor;
            }
        }

        /// <summary>
        /// 处理处理结果
        /// </summary>
        /// <param name="result">处理结果</param>
        private void HandleProcessingResult(HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessResult result)
        {
            // 确保UI操作在UI线程中执行
            if (InvokeRequired)
            {
                BeginInvoke(new Action<HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessResult>(HandleProcessingResult), result);
                return;
            }

            // 使用Helper类生成UI信息
            var uiInfo = StationDataProcessorHelper.GenerateProcessingUIInfo(result);

            // 更新UI
            progressBar.Value = uiInfo.ProgressValue;
            lblProgress.Text = uiInfo.ProgressText;
            lblStatus.Text = uiInfo.StatusText;
            etLogDisplayControl.WriteInfo(uiInfo.LogMessage);
            ETLogManager.Info(uiInfo.LogMessage, "StationDataProcessor");

            if (uiInfo.Success)
            {
                // 标记处理成功完成
                _processingCompletedSuccessfully = true;
                // 设置处理状态为完成，但保持进度条可见
                SetProcessingState(false, true);
            }

            // 播放提示音
            if (uiInfo.PlaySound && uiInfo.SoundToPlay != null)
            {
                uiInfo.SoundToPlay.Play();
            }

            // 显示用户消息
            ShowUserFriendlyMessage(uiInfo.UserMessage, uiInfo.UserTitle, uiInfo.UserIcon);
        }

        /// <summary>
        /// 处理处理异常
        /// </summary>
        /// <param name="ex">异常</param>
        private void HandleProcessingException(Exception ex)
        {
            // 确保UI操作在UI线程中执行
            if (InvokeRequired)
            {
                BeginInvoke(new Action<Exception>(HandleProcessingException), ex);
                return;
            }

            // 使用Helper类生成异常UI信息
            var uiInfo = StationDataProcessorHelper.GenerateExceptionUIInfo(ex);

            // 更新UI
            lblStatus.Text = uiInfo.StatusText;
            etLogDisplayControl.WriteError(uiInfo.LogMessage, ex);
            ETLogManager.Error("StationDataProcessor", uiInfo.LogMessage, ex);

            // 播放提示音
            if (uiInfo.PlaySound && uiInfo.SoundToPlay != null)
            {
                uiInfo.SoundToPlay.Play();
            }

            // 显示用户消息
            ShowUserFriendlyMessage(uiInfo.UserMessage, uiInfo.UserTitle, uiInfo.UserIcon);
        }

        #endregion 用户体验优化方法

        #region 处理方法

        /// <summary>
        /// 开始异步处理
        /// </summary>
        private async Task StartProcessingAsync()
        {
            // 重置成功完成标志
            _processingCompletedSuccessfully = false;

            SetProcessingState(true);
            etLogDisplayControl.Clear();
            etLogDisplayControl.WriteInfo("🚀 === 开始处理基站数据 ===");

            try
            {
                // 创建线程安全的委托
                var logWriter = StationDataProcessorHelper.CreateThreadSafeLogWriter(etLogDisplayControl, this);

                // 创建线程安全的进度处理器
                var progressHandler = StationDataProcessorHelper.CreateThreadSafeProgressHandler(
                    this, progressBar, lblProgress, lblCurrentStep, lblStatus, etLogDisplayControl);
                var progressReporter = new Progress<CoreProgressEventArgs>(progressHandler);

                // 使用Helper类执行处理
                var result = await StationDataProcessorHelper.ExecuteProcessingAsync(
                    _currentWorksheet,
                    etRangeSelectControl.SelectedRange,
                    _currentConfigFileName,
                    logWriter,
                    progressReporter);

                // 处理结果
                HandleProcessingResult(result);
            }
            catch (Exception ex)
            {
                HandleProcessingException(ex);
            }
        }

        /// <summary>
        /// 设置处理状态
        /// </summary>
        /// <param name="isProcessing">是否正在处理</param>
        /// <param name="keepProgressVisible">是否保持进度条可见（用于处理完成时）</param>
        private void SetProcessingState(bool isProcessing, bool keepProgressVisible = false)
        {
            // 确保在UI线程中执行
            if (this.InvokeRequired)
            {
                this.Invoke(new WinFormsAction(() => SetProcessingState(isProcessing, keepProgressVisible)));
                return;
            }

            _isProcessing = isProcessing;

            // 更新按钮状态
            btnProcess.Enabled = !isProcessing && _currentWorksheet != null;
            btnProcess.Text = isProcessing ? "处理中..." : "开始处理";
            btnCancel.Text = isProcessing ? "停止" : "取消";
            btnCancel.Enabled = true;

            // 更新控件状态
            etRangeSelectControl.Enabled = !isProcessing;

            // 更新进度显示
            if (isProcessing)
            {
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                lblProgress.Text = "准备中...";
                lblCurrentStep.Text = "";
                lblStatus.Text = "🔄 正在处理数据...";

                // 改变窗体标题显示处理状态
                this.Text = "基站数据处理器 - 处理中...";
            }
            else
            {
                // 如果需要保持进度条可见（处理完成时），则不隐藏进度条
                if (!keepProgressVisible)
                {
                    progressBar.Visible = false;
                    progressBar.Style = ProgressBarStyle.Blocks;
                    progressBar.Value = 0;
                    lblProgress.Text = "0%";
                    lblStatus.Text = "就绪";

                    // 恢复窗体标题
                    this.Text = "基站数据处理器";
                }
                else
                {
                    // 保持进度条可见，显示完成状态
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Blocks;
                    // 进度条值和标签文本保持当前状态（应该是100%）

                    // 恢复窗体标题
                    this.Text = "基站数据处理器";
                }

                lblCurrentStep.Text = "";
            }

            // 刷新界面
            this.Refresh();
        }

        #endregion 处理方法

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "StationDataProcessorForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}
