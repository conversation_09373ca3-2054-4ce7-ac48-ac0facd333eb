using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ET.AI.Services
{
    /// <summary>
    /// API密钥管理器，用于管理和分配多个API密钥
    /// </summary>
    /// <remarks>
    /// 该管理器提供以下功能：
    /// - 多密钥轮询分配
    /// - 单密钥并发请求控制
    /// - 每分钟请求次数限制
    /// - 自动密钥状态监控
    /// - 线程安全的密钥分配
    /// </remarks>
    public class APIKeyManager
    {
        /// <summary>
        /// 表示单个API密钥的状态信息
        /// </summary>
        /// <remarks>
        /// 包含密钥的所有运行时状态，包括：
        /// - 密钥字符串
        /// - 并发请求计数
        /// - 请求时间戳记录
        /// - 线程同步对象
        /// </remarks>
        class KeyState
        {
            /// <summary>
            /// API密钥字符串
            /// </summary>
            public string ApiKey { get; set; }

            /// <summary>
            /// 当前正在处理的并发请求数
            /// </summary>
            public int CurrentConcurrentRequests { get; set; }

            /// <summary>
            /// 记录最近一分钟内的请求时间戳队列
            /// </summary>
            /// <remarks>
            /// 用于实现滑动窗口的请求频率限制
            /// </remarks>
            public Queue<DateTime> RequestTimestamps { get; set; } = new Queue<DateTime>();

            /// <summary>
            /// 用于确保线程安全的锁对象
            /// </summary>
            public object LockObject { get; } = new object();
        }

        readonly List<KeyState> _keyStates;
        readonly int _maxConcurrentRequestsPerKey;
        readonly int _maxRequestsPerMinute;
        int _currentKeyIndex = 0;
        readonly object _rotationLock = new object();

        /// <summary>
        /// 初始化API密钥管理器的新实例
        /// </summary>
        /// <param name="apiKeys">要管理的API密钥列表</param>
        /// <param name="maxConcurrentRequestsPerKey">每个密钥允许的最大并发请求数</param>
        /// <param name="maxRequestsPerMinute">每个密钥每分钟允许的最大请求数</param>
        /// <exception cref="ArgumentException">当API密钥列表为空或为null时抛出</exception>
        /// <remarks>
        /// 构造函数会：
        /// 1. 验证输入参数的有效性
        /// 2. 初始化所有密钥的状态对象
        /// 3. 设置并发和频率限制参数
        /// </remarks>
        public APIKeyManager(List<string> apiKeys, int maxConcurrentRequestsPerKey, int maxRequestsPerMinute)
        {
            if (apiKeys == null || apiKeys.Count == 0)
                throw new ArgumentException("API密钥列表不能为空", nameof(apiKeys));

            _maxConcurrentRequestsPerKey = maxConcurrentRequestsPerKey;
            _maxRequestsPerMinute = maxRequestsPerMinute;
            _keyStates = apiKeys.Select(key => new KeyState { ApiKey = key }).ToList();
        }

        /// <summary>
        /// 获取下一个可用的API密钥
        /// </summary>
        /// <returns>返回一个元组，包含可用的API密钥和用于释放该密钥的Action委托</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 轮询所有密钥直到找到可用的密钥
        /// 2. 自动处理并发限制和频率限制
        /// 3. 提供释放密钥的机制
        /// 4. 在无可用密钥时自动等待重试
        /// </remarks>
        public async Task<(string ApiKey, Action ReleaseKey)> GetNextAvailableKey()
        {
            while (true)
            {
                // 轮询所有密钥
                for (int i = 0; i < _keyStates.Count; i++)
                {
                    KeyState keyState;
                    lock (_rotationLock)
                    {
                        _currentKeyIndex = (_currentKeyIndex + 1) % _keyStates.Count;
                        keyState = _keyStates[_currentKeyIndex];
                    }

                    lock (keyState.LockObject)
                    {
                        // 检查并发限制
                        if (keyState.CurrentConcurrentRequests >= _maxConcurrentRequestsPerKey)
                            continue;

                        // 清理过期的请求时间戳
                        DateTime oneMinuteAgo = DateTime.Now.AddMinutes(-1);
                        while (keyState.RequestTimestamps.Count > 0 && keyState.RequestTimestamps.Peek() < oneMinuteAgo)
                        {
                            keyState.RequestTimestamps.Dequeue();
                        }

                        // 检查流量限制
                        if (keyState.RequestTimestamps.Count >= _maxRequestsPerMinute)
                            continue;

                        // 增加并发计数和添加时间戳
                        keyState.CurrentConcurrentRequests++;
                        keyState.RequestTimestamps.Enqueue(DateTime.Now);

                        // 创建释放函数
                        Action releaseKey = () =>
                        {
                            lock (keyState.LockObject)
                            {
                                keyState.CurrentConcurrentRequests = Math.Max(0, keyState.CurrentConcurrentRequests - 1);
                            }
                        };

                        return (keyState.ApiKey, releaseKey);
                    }
                }

                // 如果没有可用的密钥，等待一段时间后重试
                await Task.Delay(100);
            }
        }

        /// <summary>
        /// 获取指定API密钥的当前使用状态
        /// </summary>
        /// <param name="apiKey">要查询的API密钥</param>
        /// <returns>返回一个元组，包含当前并发请求数和最近一分钟的请求数</returns>
        /// <remarks>
        /// 返回的元组中：
        /// - CurrentConcurrent: 表示当前正在处理的并发请求数
        /// - RequestsPerMinute: 表示最近一分钟内的请求总数
        /// 如果指定的密钥不存在，返回(0, 0)
        /// </remarks>
        public (int CurrentConcurrent, int RequestsPerMinute) GetKeyStatus(string apiKey)
        {
            KeyState keyState = _keyStates.FirstOrDefault(k => k.ApiKey == apiKey);
            if (keyState == null)
                return (0, 0);

            lock (keyState.LockObject)
            {
                // 清理过期的请求时间戳
                DateTime oneMinuteAgo = DateTime.Now.AddMinutes(-1);
                while (keyState.RequestTimestamps.Count > 0 && keyState.RequestTimestamps.Peek() < oneMinuteAgo)
                {
                    keyState.RequestTimestamps.Dequeue();
                }

                return (keyState.CurrentConcurrentRequests, keyState.RequestTimestamps.Count);
            }
        }
    }
}