# 51Helper - 设置存储目录功能说明

## 📋 功能概述

在51Helper窗体的空白表模按钮下方新增了"设置存储目录"按钮，用于配置51Helper相关文件的存储路径。

## 🎯 功能位置

- **窗体**: frm51Helper
- **按钮位置**: 空白表模按钮下方 (876, 51)
- **按钮名称**: 设置存储目录

## 🔧 实现细节

### 界面修改
- **文件**: `frm51Helper.Designer.cs`
- **新增控件**: `button设置存储目录`
- **布局**: 位于空白表模按钮正下方，保持相同的宽度和样式

### 功能实现
- **文件**: `frm51Helper2.cs`
- **方法**: `设置存储目录()`
- **事件处理**: `button设置存储目录_Click()`

## 📁 配置文件管理

### 配置文件路径
```csharp
string configPath = ETConfig.GetConfigDirectory("config.ini");
```

### 配置节和键
- **节名**: `51Helper`
- **键名**: `saveFilePath`
- **默认值**: 空字符串

### 读取配置示例
```csharp
ETIniFile config = new(configPath);
string saveFolder = config.GetValue("51Helper", "saveFilePath", string.Empty);
```

## 🎨 用户交互流程

1. **点击按钮**: 用户点击"设置存储目录"按钮
2. **显示输入对话框**: 弹出ETInputDialog输入对话框
3. **当前路径**: 如果已配置路径，自动显示在输入框中作为默认值
4. **输入路径**: 用户直接输入或修改存储目录路径
5. **路径验证**: 系统验证路径格式并自动创建不存在的目录
6. **保存配置**: 将输入的路径保存到config.ini文件
7. **反馈信息**: 在日志区域显示设置结果
8. **通知提示**: 显示成功或失败的通知

## ✅ 功能特性

### 路径验证
- 验证选择的路径是否存在
- 自动创建不存在的目录（通过Directory.Exists检查）

### 用户友好
- 支持显示当前已配置的路径
- 提供清晰的操作反馈
- 支持取消操作

### 错误处理
- 完善的异常捕获和处理
- 详细的错误日志记录
- 用户友好的错误提示

## 🔗 与现有功能的集成

### 配置读取兼容性
新功能完全兼容现有的配置读取代码，如`Dx51HelpTask2.cs`中的实现：

```csharp
// 从配置文件获取保存路径
string configPath = ETConfig.GetConfigDirectory("config.ini");
if (!File.Exists(configPath))
{
    return (false, "配置文件不存在");
}

ETIniFile config = new(configPath);
string saveFolder = config.GetValue("51Helper", "saveFilePath", string.Empty);
```

### 依赖组件
- `ETConfig`: 配置文件路径管理
- `ETIniFile`: INI文件读写操作
- `ETLogManager`: 日志记录
- `ETNotificationHelper`: 通知提示
- `ET.Controls.ETInputDialog`: 通用输入对话框

## 🎯 使用说明

1. 打开51Helper窗体
2. 找到右上角的"设置存储目录"按钮（位于"空白表模"按钮下方）
3. 点击按钮打开文件夹选择对话框
4. 选择合适的存储目录
5. 点击确定保存配置
6. 查看日志区域的反馈信息

## 📝 注意事项

- 配置文件路径: `config/config.ini`
- 配置节: `[51Helper]`
- 配置键: `saveFilePath`
- 建议选择有足够空间的目录作为存储路径
- 设置后的路径将被所有51Helper相关功能使用
