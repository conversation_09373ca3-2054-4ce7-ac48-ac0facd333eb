using System;
using System.Drawing;
using System.Windows.Forms;
using ET.Controls;

namespace HyExcelVsto.Module.WX.StationDataProcessor
{
    partial class StationDataProcessorForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
                ReleaseCustomResources();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.etRangeSelectControl = new ET.Controls.ETRangeSelectControl();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblProgress = new System.Windows.Forms.Label();
            this.btnProcess = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.etLogDisplayControl = new ET.Controls.ETLogDisplayControl();
            this.lblConfigFile = new System.Windows.Forms.Label();
            this.cmbConfigFiles = new System.Windows.Forms.ComboBox();
            this.btnOpenConfigDir = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // etRangeSelectControl
            // 
            this.etRangeSelectControl.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etRangeSelectControl.EnableEnterThenSelect = false;
            this.etRangeSelectControl.HideParentForm = true;
            this.etRangeSelectControl.InputPromptText = "请选择Excel范围：";
            this.etRangeSelectControl.Location = new System.Drawing.Point(12, 52);
            this.etRangeSelectControl.Margin = new System.Windows.Forms.Padding(2);
            this.etRangeSelectControl.Name = "etRangeSelectControl";
            this.etRangeSelectControl.SelectedRange = null;
            this.etRangeSelectControl.Size = new System.Drawing.Size(580, 25);
            this.etRangeSelectControl.TabIndex = 0;
            // 
            // progressBar
            // 
            this.progressBar.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBar.Location = new System.Drawing.Point(12, 104);
            this.progressBar.Margin = new System.Windows.Forms.Padding(2);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(429, 18);
            this.progressBar.TabIndex = 2;
            // 
            // lblProgress
            // 
            this.lblProgress.AutoSize = true;
            this.lblProgress.Location = new System.Drawing.Point(12, 128);
            this.lblProgress.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lblProgress.Name = "lblProgress";
            this.lblProgress.Size = new System.Drawing.Size(53, 12);
            this.lblProgress.TabIndex = 3;
            this.lblProgress.Text = "准备就绪";
            // 
            // btnProcess
            // 
            this.btnProcess.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnProcess.Location = new System.Drawing.Point(451, 101);
            this.btnProcess.Margin = new System.Windows.Forms.Padding(2);
            this.btnProcess.Name = "btnProcess";
            this.btnProcess.Size = new System.Drawing.Size(85, 24);
            this.btnProcess.TabIndex = 4;
            this.btnProcess.Text = "开始处理";
            this.btnProcess.UseVisualStyleBackColor = true;
            this.btnProcess.Click += new System.EventHandler(this.BtnProcess_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(539, 101);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(2);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(56, 24);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // etLogDisplayControl
            // 
            this.etLogDisplayControl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etLogDisplayControl.AutoScrollToBottom = true;
            this.etLogDisplayControl.CurrentLogLevel = ET.Controls.ETLogDisplayControl.LogLevel.Info;
            this.etLogDisplayControl.CustomInitialMessage = null;
            this.etLogDisplayControl.Location = new System.Drawing.Point(12, 144);
            this.etLogDisplayControl.LogBackColor = System.Drawing.Color.White;
            this.etLogDisplayControl.LogFont = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.etLogDisplayControl.LogForeColor = System.Drawing.Color.Black;
            this.etLogDisplayControl.Margin = new System.Windows.Forms.Padding(2);
            this.etLogDisplayControl.MaxLogLines = 1000;
            this.etLogDisplayControl.Name = "etLogDisplayControl";
            this.etLogDisplayControl.ShowInitialMessage = true;
            this.etLogDisplayControl.ShowLogLevel = true;
            this.etLogDisplayControl.ShowTimestamp = true;
            this.etLogDisplayControl.Size = new System.Drawing.Size(580, 336);
            this.etLogDisplayControl.TabIndex = 6;
            // 
            // lblConfigFile
            // 
            this.lblConfigFile.AutoSize = true;
            this.lblConfigFile.Location = new System.Drawing.Point(12, 24);
            this.lblConfigFile.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lblConfigFile.Name = "lblConfigFile";
            this.lblConfigFile.Size = new System.Drawing.Size(65, 12);
            this.lblConfigFile.TabIndex = 7;
            this.lblConfigFile.Text = "配置文件：";
            // 
            // cmbConfigFiles
            // 
            this.cmbConfigFiles.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbConfigFiles.FormattingEnabled = true;
            this.cmbConfigFiles.Location = new System.Drawing.Point(81, 21);
            this.cmbConfigFiles.Margin = new System.Windows.Forms.Padding(2);
            this.cmbConfigFiles.Name = "cmbConfigFiles";
            this.cmbConfigFiles.Size = new System.Drawing.Size(360, 20);
            this.cmbConfigFiles.TabIndex = 8;
            this.cmbConfigFiles.SelectedIndexChanged += new System.EventHandler(this.CmbConfigFiles_SelectedIndexChanged);
            // 
            // btnOpenConfigDir
            // 
            this.btnOpenConfigDir.Location = new System.Drawing.Point(451, 20);
            this.btnOpenConfigDir.Margin = new System.Windows.Forms.Padding(2);
            this.btnOpenConfigDir.Name = "btnOpenConfigDir";
            this.btnOpenConfigDir.Size = new System.Drawing.Size(144, 24);
            this.btnOpenConfigDir.TabIndex = 9;
            this.btnOpenConfigDir.Text = "打开配置目录";
            this.btnOpenConfigDir.UseVisualStyleBackColor = true;
            this.btnOpenConfigDir.Click += new System.EventHandler(this.BtnOpenConfigDir_Click);
            // 
            // StationDataProcessorForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(604, 492);
            this.Controls.Add(this.etLogDisplayControl);
            this.Controls.Add(this.etRangeSelectControl);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.lblProgress);
            this.Controls.Add(this.btnProcess);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.lblConfigFile);
            this.Controls.Add(this.cmbConfigFiles);
            this.Controls.Add(this.btnOpenConfigDir);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MinimumSize = new System.Drawing.Size(620, 530);
            this.Name = "StationDataProcessorForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "基站数据处理器";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private ET.Controls.ETRangeSelectControl etRangeSelectControl;
        private ProgressBar progressBar;
        private Label lblProgress;
        private Button btnProcess;
        private Button btnCancel;
        private ET.Controls.ETLogDisplayControl etLogDisplayControl;
        private Label lblConfigFile;
        private ComboBox cmbConfigFiles;
        private Button btnOpenConfigDir;
    }
}
