﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{163811E3-BCFB-4E4D-9D21-59068851D958}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ExtensionsTools</RootNamespace>
    <AssemblyName>ExtensionsTools</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>5</WarningLevel>
    <LangVersion>latest</LangVersion>
    <RegisterForComInterop>false</RegisterForComInterop>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>ET_TemporaryKey.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <!--<PropertyGroup>
    <AssemblyOriginatorKeyFile>ET_TemporaryKey.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>-->
  <ItemGroup>
    <Reference Include="Aliyun.OSS, Version=2.14.1.0, Culture=neutral, PublicKeyToken=0ad4175f0dac0b9b, processorArchitecture=MSIL">
      <HintPath>..\packages\Aliyun.OSS.SDK.2.14.1\lib\net461\Aliyun.OSS.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.6\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.PowerPoint, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Visio, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Word, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Tools.Excel, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Excel.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Tools.Word, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Word.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.WebView2.Core, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.WinForms, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Wpf, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.2.6.0\lib\netstandard2.0\NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="OpenAI, Version=2.1.0.0, Culture=neutral, PublicKeyToken=b4187f3e65366280, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenAI.2.1.0\lib\netstandard2.0\OpenAI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="SharpCompress, Version=0.40.0.0, Culture=neutral, PublicKeyToken=afb0a02973931d96, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpCompress.0.40.0\lib\net48\SharpCompress.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.1\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.4.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.4.1\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=9.0.0.5, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.9.0.5\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.5\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.6\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.3\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.9.0.6\lib\net462\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.1\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.6\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.6\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CSharp.4.7.0\lib\net462\Microsoft.CSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="ZstdSharp, Version=0.8.5.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.8.5\lib\net462\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common.Utility\AES.cs" />
    <Compile Include="Common.Utility\class\IntPtrEnumHelper.cs" />
    <Compile Include="Common.Utility\class\struct.cs" />
    <Compile Include="Common.Utility\class\Win32Window.cs" />
    <Compile Include="Common.Utility\FileOperate.cs" />
    <Compile Include="Common.Utility\HWInfo.cs" />
    <Compile Include="Common.Utility\ValidatorHelper.cs" />
    <Compile Include="Common.Utility\系统\Enums.cs" />
    <Compile Include="Common.Utility\系统\Structs.cs" />
    <Compile Include="Common.Utility\系统\Win32API.cs.cs" />
    <Compile Include="ExtensionsTools\Controls\ETInputDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETInputDialog.Designer.cs">
      <DependentUpon>ETInputDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETAIv2\AIExcelAssistant.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Constants\AIConstants.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Models\AIDataModels.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Models\AIRequestModels.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Models\AIResponseModels.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Services\Core\AIClient.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Utils\AILogger.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs" />
    <Compile Include="ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs" />
    <Compile Include="ExtensionsTools\ETDateHelper.cs" />
    <Compile Include="ExtensionsTools\Controls\ETLogDisplayControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs">
      <DependentUpon>ETLogDisplayControl.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETRangeSelectControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs">
      <DependentUpon>ETRangeSelectControl.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\IExcelApplicationProvider.cs" />
    <Compile Include="ExtensionsTools\ETGeographic\ETPolygonUtil.cs" />
    <Compile Include="ExtensionsTools\ETInitializer.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETAboutLicenseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETAboutLicenseForm.Designer.cs">
      <DependentUpon>ETAboutLicenseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETControlMappingGenerator.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETControlMappingManagerBase.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETControlPermissionManager.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.Designer.cs">
      <DependentUpon>ETLicenseAdminLoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseController.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseCryptoService.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseEntityForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseEntityForm.Designer.cs">
      <DependentUpon>ETLicenseEntityForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseGenerator.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseGeneratorForm.Designer.cs">
      <DependentUpon>ETLicenseGeneratorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseManager.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseManagerBase.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseModels.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseProvider.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLicenseVerifier.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLocationService.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETLocationTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETLocationTestForm.Designer.cs">
      <DependentUpon>ETLocationTestForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETMachineCodeProvider.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETOssBrowserForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETOssBrowserForm.Designer.cs">
      <DependentUpon>ETOssBrowserForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETOssConfig.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETOssConfigForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETOssConfigForm.Designer.cs">
      <DependentUpon>ETOssConfigForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETOssService.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETPermissionManager.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETPermissionNamesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETPermissionNamesForm.Designer.cs">
      <DependentUpon>ETPermissionNamesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs" />
    <Compile Include="ExtensionsTools\ETLicense\ETUIPermissionManager.cs" />
    <Compile Include="ExtensionsTools\ETLicense\Extensions\ETDictionaryExtensions.cs" />
    <Compile Include="ExtensionsTools\ETLicense\InputBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs">
      <DependentUpon>ETLoginWebBrowser.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs" />
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs" />
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\WebView2DiagnosticTool.cs" />
    <Compile Include="ExtensionsTools\ETLoginWebBrowser\WebView2Helper.cs" />
    <Compile Include="ExtensionsTools\ETSectionConfigReader.cs" />
    <Compile Include="ExtensionsTools\ETTextCrypto.cs" />
    <Compile Include="ExtensionsTools\ETTools\ETFileAnalyzer.cs" />
    <Compile Include="ExtensionsTools\ETAutoCollapseWindowBehavior.cs" />
    <Compile Include="ExtensionsTools\Controls\ETAutoResetLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\ETCommunicationService\ETCommunicationService.cs" />
    <Compile Include="ExtensionsTools\ETExcel\FormManager\Enums\XlFormPosition.cs" />
    <Compile Include="ExtensionsTools\ETException.cs" />
    <Compile Include="ExtensionsTools\ETExcel\FormManager\HHFormManager.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Initialize.cs" />
    <Compile Include="ExtensionsTools\ETTools\ETFileCopier.cs" />
    <Compile Include="ExtensionsTools\ETIniFile.cs" />
    <Compile Include="ExtensionsTools\ETFormater.cs" />
    <Compile Include="ExtensionsTools\ETStock\AkToolsClient.cs" />
    <Compile Include="ExtensionsTools\ETStock\AkToolsClientStockDataCrawler.cs" />
    <Compile Include="ExtensionsTools\ETStock\StockHelper.cs" />
    <Compile Include="ExtensionsTools\ETStock\StockSoftwareCommand.cs" />
    <Compile Include="ExtensionsTools\ETString2.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_APP.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Shape_Search.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_ShapeBase.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Document.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Shape2.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_图衔图框.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Excel.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Other.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Page.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_PDF.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Shape.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Struct.cs" />
    <Compile Include="ExtensionsTools\ETVisio\ETVisioHelper2013_Text.cs" />
    <Compile Include="ExtensionsTools\ETExcel\FormManager\Interfaces\IExcelSelectionChangeNotify.cs" />
    <Compile Include="ExtensionsTools\ETLogManager.cs" />
    <Compile Include="ExtensionsTools\ETGeographic\ETGpsConvertUtil.cs" />
    <Compile Include="ExtensionsTools\ETConfig.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelAutoUpdate.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelConfig.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_AppControl.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_DisplayNamesShapes.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_HiddenComments.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_InputDialog.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Jump.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Other.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_RangeOperations.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_RowColumnNumbers.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_StringProcessing.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Struct.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_TagOperations.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_TextSearch.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_Validation.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetProperties.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetProtection.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_WPS.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_单元格填写.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_填写辅助操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_外部连接操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_字符处理操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_工作簿操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_工作表操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_常用操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_排序操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_格式操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_筛选操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_表格操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_转换操作.cs" />
    <Compile Include="ExtensionsTools\ETExcel\ETExcelExtensions_页面设置操作.cs" />
    <Compile Include="ExtensionsTools\ETFile.cs" />
    <Compile Include="ExtensionsTools\ETForm.cs" />
    <Compile Include="ExtensionsTools\ETStringPrefixSuffixProcessor.cs" />
    <Compile Include="ExtensionsTools\WinAPI_form.cs" />
    <Compile Include="ExtensionsTools\ETGeographic\ETGPS.cs" />
    <Compile Include="ExtensionsTools\ETNet.cs" />
    <Compile Include="ExtensionsTools\ETPowerPoint\HHPowerPoint.cs" />
    <Compile Include="ExtensionsTools\ETString.cs" />
    <Compile Include="ExtensionsTools\ETTime.cs" />
    <Compile Include="ExtensionsTools\ETWord\ETWord.cs" />
    <Compile Include="ExtensionsTools\ETTools\ETNotificationHelper.cs" />
    <Compile Include="ExtensionsTools\Controls\HHUcDirectorySelect.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs">
      <DependentUpon>HHUcDirectorySelect.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETUcFileSelect.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ExtensionsTools\Controls\ETUcFileSelect.Designer.cs">
      <DependentUpon>ETUcFileSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsTools\ETVisio\ETVisioMonitor.cs" />
    <Compile Include="ExtensionsTools\ETVisio\HHVisioToPdfConverter.cs" />
    <Compile Include="ExtensionsTools\WinAPI.cs" />
    <Compile Include="ExtensionsTools\ETTools\ETWxPusherService.cs" />
    <Compile Include="ExtensionsTools\ETMail.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ExtensionsTools\Controls\HHUcDirectorySelect.resx">
      <DependentUpon>HHUcDirectorySelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\ETLicense\ETAboutLicenseForm.resx">
      <DependentUpon>ETAboutLicenseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.resx">
      <DependentUpon>ETLicenseAdminLoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\ETLicense\ETLicenseGeneratorForm.resx">
      <DependentUpon>ETLicenseGeneratorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\ETLicense\ETLocationTestForm.resx">
      <DependentUpon>ETLocationTestForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.resx">
      <DependentUpon>ETLoginWebBrowser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionsTools\Controls\ETUcFileSelect.resx">
      <DependentUpon>ETUcFileSelect.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="ET_TemporaryKey.pfx" />
    <None Include="ExtensionsTools\ETAIv2\AIExcel.md" />
    <None Include="ExtensionsTools\ETAIv2\README.md" />
    <None Include="ExtensionsTools\Controls\README.md" />
    <None Include="ExtensionsTools\ETLoginWebBrowser\README.md" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ExtensionsTools\ETAPI\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets'))" />
    <Error Condition="!Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets" Condition="Exists('..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets')" />
  <Import Project="..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
</Project>