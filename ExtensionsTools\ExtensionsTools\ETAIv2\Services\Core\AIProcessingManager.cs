using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ExtensionsTools.ETAIv2.Models;
using ExtensionsTools.ETAIv2.Interfaces;
using ExtensionsTools.ETAIv2.Constants;
using ExtensionsTools.ETAIv2.Exceptions;
using ExtensionsTools.ETAIv2.Utils;

namespace ExtensionsTools.ETAIv2.Services.Core
{
    /// <summary>
    /// AI处理管理器 - 核心协调类
    /// </summary>
    public class AIProcessingManager : IAIProcessingManager
    {
        private readonly IAIDataExtractor _dataExtractor;
        private readonly IAIFileProcessor _fileProcessor;
        private readonly IAIClient _aiClient;
        private readonly IAIResultFiller _resultFiller;
        private readonly IAILogger _logger;
        private readonly IAIErrorHandler _errorHandler;
        private readonly List<CancellationTokenSource> _activeCancellationTokens;

        public AIProcessingManager(
            IAIDataExtractor dataExtractor = null,
            IAIFileProcessor fileProcessor = null,
            IAIClient aiClient = null,
            IAIResultFiller resultFiller = null,
            IAILogger logger = null,
            IAIErrorHandler errorHandler = null)
        {
            _logger = logger ?? new AILogger();
            _errorHandler = errorHandler ?? new AIErrorHandler(_logger);
            _dataExtractor = dataExtractor ?? new AIDataExtractor(_logger);
            _fileProcessor = fileProcessor ?? new AIFileProcessor(_logger);
            _aiClient = aiClient ?? new AIClient(_logger, _errorHandler);
            _resultFiller = resultFiller ?? new AIResultFiller(_logger);
            _activeCancellationTokens = new List<CancellationTokenSource>();
        }

        /// <summary>
        /// 处理AI请求
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<AIResponse> ProcessAsync(
            AIDataSourceConfig config,
            IProgress<ProcessingProgress> progress = null,
            CancellationToken cancellationToken = default)
        {
            var requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            var startTime = DateTime.Now;
            
            try
            {
                _logger.LogInfo($"开始AI处理流程，请求ID: {requestId}");
                progress?.Report(new ProcessingProgress("开始处理...", 0));

                // 注册取消令牌
                RegisterCancellationToken(cancellationToken);

                // 第1步：提取数据组
                progress?.Report(new ProcessingProgress("正在提取数据...", 10));
                var dataGroups = await ExtractDataGroupsAsync(config, cancellationToken);
                
                if (!dataGroups.Any())
                {
                    var emptyResponse = AIResponse.CreateFailure(requestId, "没有提取到有效数据");
                    return emptyResponse;
                }

                _logger.LogInfo($"提取到 {dataGroups.Count} 个数据组");

                // 第2步：处理文件
                progress?.Report(new ProcessingProgress("正在处理文件...", 25));
                await ProcessFilesAsync(dataGroups, config, cancellationToken);

                // 第3步：构建AI请求并发送
                var aiRequest = BuildAIRequest(requestId, dataGroups, config);
                var aiResponse = await SendAIRequestAsync(aiRequest, progress, cancellationToken);

                if (!aiResponse.Success)
                {
                    return aiResponse;
                }

                // 第5步：回填结果
                var batchCount = aiResponse.Metadata.ContainsKey("batchCount") ? (int)aiResponse.Metadata["batchCount"] : 1;
                if (batchCount > 1)
                {
                    progress?.Report(new ProcessingProgress($"正在回填 {batchCount} 个批次的结果...", 80));
                }
                else
                {
                    progress?.Report(new ProcessingProgress("正在回填结果...", 80));
                }
                await FillResultsAsync(aiResponse, config, cancellationToken);

                // 第6步：清理资源
                progress?.Report(new ProcessingProgress("正在清理资源...", 95));
                await CleanupResourcesAsync(dataGroups, config);

                // 完成
                aiResponse.ProcessingTime = DateTime.Now - startTime;
                progress?.Report(new ProcessingProgress("处理完成", 100));
                
                _logger.LogPerformance("AI处理流程", aiResponse.ProcessingTime, 
                    new { RequestId = requestId, GroupCount = dataGroups.Count });
                
                return aiResponse;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo($"AI处理流程已取消，请求ID: {requestId}");
                return AIResponse.CreateFailure(requestId, "操作已取消");
            }
            catch (Exception ex)
            {
                var processingTime = DateTime.Now - startTime;
                _logger.LogError($"AI处理流程失败，请求ID: {requestId}, 耗时: {processingTime.TotalSeconds:F2}秒", ex);
                
                var wrappedException = _errorHandler.WrapException(ex, "AI处理流程");
                return AIResponse.CreateFailure(requestId, wrappedException.Message);
            }
            finally
            {
                UnregisterCancellationToken(cancellationToken);
            }
        }

        /// <summary>
        /// 取消所有正在进行的请求
        /// </summary>
        public void CancelAllRequests()
        {
            try
            {
                _logger.LogInfo("开始取消所有正在进行的请求");
                
                lock (_activeCancellationTokens)
                {
                    foreach (var tokenSource in _activeCancellationTokens.ToList())
                    {
                        try
                        {
                            tokenSource?.Cancel();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"取消请求时发生错误: {ex.Message}");
                        }
                    }
                    
                    _activeCancellationTokens.Clear();
                }
                
                _logger.LogInfo("所有请求取消完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("取消所有请求时发生错误", ex);
            }
        }

        /// <summary>
        /// 提取数据组
        /// </summary>
        private async Task<List<AIDataGroup>> ExtractDataGroupsAsync(
            AIDataSourceConfig config, 
            CancellationToken cancellationToken)
        {
            return await _errorHandler.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 数据提取是同步操作
                return _dataExtractor.ExtractDataGroups(config);
            }, "数据提取");
        }

        /// <summary>
        /// 处理文件
        /// </summary>
        /// <param name="dataGroups">数据组列表</param>
        /// <param name="config">AI数据源配置，包含FileProcessingMode和ModelConfig</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ProcessFilesAsync(
            List<AIDataGroup> dataGroups,
            AIDataSourceConfig config,
            CancellationToken cancellationToken)
        {
            try
            {
                var allFiles = dataGroups.SelectMany(g => g.Files).ToList();
                if (!allFiles.Any())
                {
                    _logger.LogInfo("没有文件需要处理");
                    return;
                }

                _logger.LogInfo($"开始处理 {allFiles.Count} 个文件，模式: {config.FileProcessingMode}");

                var filePaths = allFiles.Select(f => f.FilePath).Distinct().ToList();
                var processedFiles = await _fileProcessor.ProcessFilesAsync(filePaths, config.FileProcessingMode, config.ModelConfig, cancellationToken);

                // 更新文件数据
                var fileDict = processedFiles.ToDictionary(f => f.FilePath, f => f);
                foreach (var group in dataGroups)
                {
                    for (int i = 0; i < group.Files.Count; i++)
                    {
                        if (fileDict.TryGetValue(group.Files[i].FilePath, out var processedFile))
                        {
                            group.Files[i] = processedFile;
                        }
                    }
                }

                _logger.LogInfo($"文件处理完成，成功处理 {processedFiles.Count}/{allFiles.Count} 个文件");
            }
            catch (Exception ex)
            {
                _logger.LogError("文件处理失败", ex);
                // 文件处理失败不中断整个流程，继续处理其他数据
            }
        }

        /// <summary>
        /// 构建AI请求
        /// </summary>
        private AIRequest BuildAIRequest(string requestId, List<AIDataGroup> dataGroups, AIDataSourceConfig config)
        {
            return new AIRequest
            {
                RequestId = requestId,
                GlobalPrompt = config.GlobalPrompt,
                DataGroups = dataGroups,
                ModelConfig = config.ModelConfig,
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 发送AI请求
        /// </summary>
        private async Task<AIResponse> SendAIRequestAsync(
            AIRequest request,
            IProgress<ProcessingProgress> progress,
            CancellationToken cancellationToken)
        {
            try
            {
                var groupCount = request.DataGroups.Count;

                // 如果数据组较少，直接发送单个请求
                if (groupCount <= 5)
                {
                    progress?.Report(new ProcessingProgress("正在AI分析...", 50));
                    return await _aiClient.SendRequestAsync(request, cancellationToken);
                }

                // 如果数据组较多，分批处理
                return await ProcessInBatchesAsync(request, progress, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"发送AI请求失败，请求ID: {request.RequestId}", ex);
                throw;
            }
        }

        /// <summary>
        /// 分批处理AI请求
        /// </summary>
        private async Task<AIResponse> ProcessInBatchesAsync(
            AIRequest request,
            IProgress<ProcessingProgress> progress,
            CancellationToken cancellationToken)
        {
            var batchSize = request.ModelConfig.BaseGroupSize;
            var allGroups = request.DataGroups;
            var allResults = new List<GroupResult>();
            var totalBatches = (int)Math.Ceiling((double)allGroups.Count / batchSize);

            _logger.LogInfo($"分批处理AI请求，总组数: {allGroups.Count}, 批大小: {batchSize}, 总批数: {totalBatches}");

            // 显示分批处理信息
            progress?.Report(new ProcessingProgress($"共分 {totalBatches} 个批次处理，开始AI分析...", 50));

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batchGroups = allGroups.Skip(batchIndex * batchSize).Take(batchSize).ToList();
                var batchRequest = new AIRequest
                {
                    RequestId = $"{request.RequestId}_batch_{batchIndex + 1}",
                    GlobalPrompt = request.GlobalPrompt,
                    DataGroups = batchGroups,
                    ModelConfig = request.ModelConfig,
                    ApiType = request.ApiType
                };

                var progressPercent = 50 + (int)(30.0 * (batchIndex + 1) / totalBatches);
                progress?.Report(new ProcessingProgress(
                    $"正在处理第 {batchIndex + 1}/{totalBatches} 批数据...",
                    progressPercent,
                    $"batch_{batchIndex + 1}",
                    batchIndex,
                    totalBatches));

                var batchResponse = await _aiClient.SendRequestAsync(batchRequest, cancellationToken);

                if (batchResponse.Success && batchResponse.Results?.Any() == true)
                {
                    allResults.AddRange(batchResponse.Results);
                }
                else
                {
                    _logger.LogWarning($"批次 {batchIndex + 1} 处理失败: {batchResponse.ErrorMessage}");
                }

                // 批次间添加延迟以避免API限制
                if (batchIndex < totalBatches - 1)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            var finalResponse = AIResponse.CreateSuccess(request.RequestId, allResults);
            finalResponse.Metadata["batchCount"] = totalBatches;
            finalResponse.Metadata["totalGroups"] = allGroups.Count;

            return finalResponse;
        }

        /// <summary>
        /// 回填结果
        /// </summary>
        private async Task FillResultsAsync(
            AIResponse response,
            AIDataSourceConfig config,
            CancellationToken cancellationToken)
        {
            try
            {
                await _resultFiller.FillResultsAsync(response, config);
            }
            catch (Exception ex)
            {
                var wrappedException = _errorHandler.WrapException(ex, "结果回填");
                _errorHandler.LogError(wrappedException, "结果回填");
                throw wrappedException;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        /// <param name="dataGroups">数据组列表</param>
        /// <param name="config">AI数据源配置，包含ModelConfig用于文件清理</param>
        private async Task CleanupResourcesAsync(List<AIDataGroup> dataGroups, AIDataSourceConfig config)
        {
            try
            {
                if (config.FileProcessingMode == FileProcessingMode.UploadToOpenAI)
                {
                    var uploadedFileIds = dataGroups
                        .SelectMany(g => g.Files)
                        .Where(f => f.IsUploaded && !string.IsNullOrEmpty(f.OpenAIFileId))
                        .Select(f => f.OpenAIFileId)
                        .Distinct()
                        .ToList();

                    if (uploadedFileIds.Any())
                    {
                        // 传递ModelConfig给文件清理方法
                        await _fileProcessor.CleanupUploadedFilesAsync(uploadedFileIds, config.ModelConfig);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"清理资源时发生错误: {ex.Message}");
                // 清理失败不影响主要流程
            }
        }

        /// <summary>
        /// 注册取消令牌
        /// </summary>
        private void RegisterCancellationToken(CancellationToken cancellationToken)
        {
            if (cancellationToken != CancellationToken.None)
            {
                lock (_activeCancellationTokens)
                {
                    var tokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    _activeCancellationTokens.Add(tokenSource);
                }
            }
        }

        /// <summary>
        /// 注销取消令牌
        /// </summary>
        private void UnregisterCancellationToken(CancellationToken cancellationToken)
        {
            if (cancellationToken != CancellationToken.None)
            {
                lock (_activeCancellationTokens)
                {
                    _activeCancellationTokens.RemoveAll(ts => ts.Token.IsCancellationRequested);
                }
            }
        }
    }
}
