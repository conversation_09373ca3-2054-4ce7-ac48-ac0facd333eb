﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Common.Utility
{
    public class AES
    {//加密函式
        public string encrypt(string string_secretContent, string string_pwd)
        {
            //密码转译一定都是用byte[] 所以把string都换成byte[]
            byte[] byte_secretContent = Encoding.UTF8.GetBytes(string_secretContent);
            byte[] byte_pwd = Encoding.UTF8.GetBytes(string_pwd);

            //加解密函数的key通常都会有固定的长度 而使用者输入的key长度不定 因此用hash过后的值当做key
            MD5CryptoServiceProvider provider_MD5 = new();
            //MD5CryptoServiceProvider 类的 ComputeHash 方法将哈希作为 16 字节的数组返回。
            byte[] byte_pwdMD5 = provider_MD5.ComputeHash(byte_pwd);

            StringBuilder sBuilder = new();
            for (int i = 0; i < byte_pwdMD5.Length; i++)
            {
                sBuilder.Append(byte_pwdMD5[i].ToString("x2"));        //32位字符串
            }
            byte_pwdMD5 = HexStringToByteArray(sBuilder.ToString());  //规格化成256位，即32个数的数组

            //产生加密实体 如果要用其他不同的加解密演算法就改这裡(ex:3DES)
            //.NET中默认实现了4种对称加密算法：DES、TripleDES、RC2、Rijndeal
            //Rijndael算是AES比较有弹性的版本 因此在.net中要使用AES加密就用RijndaelManaged类
            //此算法支持 128、192 或 256 位的密钥长度
            RijndaelManaged provider_AES = new()
            {
                BlockSize = 256                              //对应前面的密钥长度
            };
            ICryptoTransform encrypt_AES = provider_AES.CreateEncryptor(byte_pwdMD5, byte_pwdMD5);

            //output就是加密过后的结果
            byte[] output = encrypt_AES.TransformFinalBlock(byte_secretContent, 0, byte_secretContent.Length);
            return Convert.ToBase64String(output);
        }

        //解密函式
        public string decrypt(string byte_ciphertext, string string_pwd)
        {
            //密码转译一定都是用byte[] 所以把string都换成byte[]
            byte[] byte_pwd = Encoding.UTF8.GetBytes(string_pwd);

            //加解密函数的key通常都会有固定的长度 而使用者输入的key长度不定 因此用hash过后的值当做key
            MD5CryptoServiceProvider provider_MD5 = new();
            byte[] byte_pwdMD5 = provider_MD5.ComputeHash(byte_pwd);

            StringBuilder sBuilder = new();
            for (int i = 0; i < byte_pwdMD5.Length; i++)
            {
                sBuilder.Append(byte_pwdMD5[i].ToString("x2"));
            }
            byte_pwdMD5 = HexStringToByteArray(sBuilder.ToString());  //规格化成32大小数组

            //产生解密实体
            RijndaelManaged provider_AES = new()
            {
                BlockSize = 256
            };
            ICryptoTransform decrypt_AES = provider_AES.CreateDecryptor(byte_pwdMD5, byte_pwdMD5);

            //string_secretContent就是解密后的明文
            byte[] Buffer = Convert.FromBase64String(byte_ciphertext);
            byte[] byte_secretContent = decrypt_AES.TransformFinalBlock(Buffer, 0, Buffer.Length);
            string string_secretContent = Encoding.UTF8.GetString(byte_secretContent);
            return string_secretContent;
        }

        //生成16个大写字母+数字的哈希值
        public string RegCode16r(String str)
        {
            //return HashSHA512(str).Substring(4, 16);
            string strRegCode = HashSHA1(str);
            return $"{strRegCode.Substring(4, 4)}-{strRegCode.Substring(8, 4)}-{strRegCode.Substring(12, 4)}-{strRegCode.Substring(16, 4)}";
        }

        string HashSHA512(string strPlain)
        {
            //SHA1Managed(160位)、SHA256Managed、SHA384Managed、SHA512Managed
            SHA512Managed sha512 = new();
            string strHash = string.Empty;
            byte[] btHash = sha512.ComputeHash(Encoding.Unicode.GetBytes(strPlain));

            StringBuilder sBuilder = new();
            for (int i = 0; i < btHash.Length; i++)
            {
                sBuilder.Append(btHash[i].ToString("X2"));
            }
            return sBuilder.ToString();
        }

        string HashSHA1(string strPlain)
        {
            //SHA1Managed(160位)
            SHA1 sha1 = new SHA1Managed();
            string strHash = string.Empty;
            byte[] btHash = sha1.ComputeHash(Encoding.Unicode.GetBytes(strPlain));

            StringBuilder sBuilder = new();
            for (int i = 0; i < btHash.Length; i++)
            {
                sBuilder.Append(btHash[i].ToString("X2"));
            }
            return sBuilder.ToString();
        }

        static byte[] HexStringToByteArray(string strHexString)
        {
            int len = strHexString.Length;

            if (len == 0) throw new Exception("HexString 字符出错!!");
            //int byteLen = len ;
            byte[] bytes = new byte[len];
            for (int i = 0; i < len; i++)
            {
                bytes[i] = Convert.ToByte(strHexString.Substring(i, 1), 16);
            }
            return bytes;
        }

        public string MD5(String str)
        {
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] data = Encoding.UTF8.GetBytes(str);
            byte[] result = md5.ComputeHash(data); //MD5CryptoServiceProvider 类的 ComputeHash 方法将哈希作为 16 字节的数组返回。
            string s = BitConverter.ToString(result).Replace("-", string.Empty);  //规格化成32位
            return s;
        }
    }
}