# ExtensionsTools 功能模块索引生成 - AI提示词

## 🎯 生成目标

为 ExtensionsTools 目录创建简洁高效的功能模块函数索引，方便AI编程工具快速定位已有功能模块和public函数。

## 📋 生成原则

### 1. 简洁性原则
- **功能描述**: 每个方法描述控制在10字以内
- **路径标准化**: 使用相对于ExtensionsTools的路径
- **避免冗余**: 不重复显示相同信息

### 2. 实用性原则
- **只记录public方法**: 忽略private、internal和protected方法
- **突出核心功能**: 重点展示最常用的方法
- **分类清晰**: 按功能模块逻辑分组

### 3. 可维护性原则
- **格式统一**: 严格按照模板格式
- **易于更新**: 结构化设计便于后续修改
- **批次处理**: 分批次生成，避免一次性处理过多内容

## 🔧 生成流程

### 阶段1: 文件分析
1. **读取源文件**: 使用view工具读取.cs文件内容
2. **识别public方法**: 使用正则搜索 `public static.*\(` 或 `public.*\(`
3. **提取方法签名**: 获取方法名、参数类型、返回类型
4. **分析XML注释**: 提取方法功能描述

### 阶段2: 信息提炼
1. **方法名简化**: 保留完整方法名，去除复杂参数详情
2. **功能描述精炼**: 将XML注释summary压缩为10字以内描述
3. **分类归纳**: 按功能相似性对方法进行分组
4. **优先级排序**: 核心方法优先展示

### 阶段3: 格式化输出
```markdown
#### 模块名 - 功能简述
**路径**: ExtensionsTools/相对路径
**功能**: 模块核心功能简述(20字以内)
**主要类**: 核心类名
**核心方法**:
- 方法名(关键参数) - 功能描述(10字以内)
- 方法名(关键参数) - 功能描述(10字以内)
```

## 📝 具体操作指南

### 1. 方法提取规则
```regex
# 搜索模式
public static.*\(     # 静态公共方法
public.*\(           # 实例公共方法

# 排除模式
//.*public           # 注释掉的方法
private.*\(          # 私有方法
internal.*\(         # 内部方法
```

### 2. 描述简化规则
- **动词优先**: 用动词开头描述功能 (获取、设置、检查、转换等)
- **去除冗余**: 删除"该方法"、"此函数"等无意义词汇
- **保留关键词**: 保留核心业务术语和技术名词
- **统一格式**: 动词+对象+结果的格式

### 3. 参数简化规则
- **基础类型**: string, int, bool 直接显示
- **复杂类型**: 只显示类型名，不显示完整命名空间
- **泛型类型**: List<T>, Dictionary<K,V> 简化显示
- **可选参数**: 用[]标识可选参数

## 🎯 质量检查清单

### 内容质量
- [ ] 所有public方法都已记录
- [ ] 方法描述准确简洁
- [ ] 路径信息正确无误
- [ ] 分类逻辑清晰合理

### 格式质量
- [ ] 严格按照模板格式
- [ ] 缩进和标记符号正确
- [ ] 无拼写和语法错误
- [ ] 链接和引用有效

### 实用性质量
- [ ] AI能快速定位到具体方法
- [ ] 功能描述有助于理解用途
- [ ] 分类有助于按需查找
- [ ] 整体结构便于浏览

## 🔄 批次处理策略

### 批次划分原则
- **每批次5-8个文件**: 避免单次处理过多内容
- **按功能模块分组**: 相关功能放在同一批次
- **复杂模块单独处理**: ETExcel等大型模块独立成批

### 进度控制要求
1. **开始前**: 检查进度控制文件，了解当前状态
2. **处理中**: 实时更新处理进度
3. **完成后**: 更新进度文件，标记完成状态
4. **质量检查**: 每批次完成后进行质量验证

## 📚 参考示例

### 优秀示例
```markdown
#### ETString - 字符串处理扩展
**路径**: ExtensionsTools/ExtensionsTools/ETString.cs
**功能**: 提供丰富的字符串处理和操作功能
**主要类**: ETString (静态类)
**核心方法**:
- RemoveRepeatedChars(string, string) - 移除重复字符
- ContainsAny(string, IEnumerable<string>) - 检查包含任一项
- ToList(string, string) - 分割为列表
- GetMd5Str(string) - 获取MD5哈希
```

### 避免的错误
```markdown
# ❌ 错误示例
- RemoveRepeatedCharsFromInputStringWithSpecialCharacters() - 该方法用于从输入字符串中移除指定的重复字符

# ✅ 正确示例  
- RemoveRepeatedChars(string, string) - 移除重复字符
```

## 🚀 开始生成指令

当准备生成索引时，请按以下步骤执行：

1. **检查进度**: 读取 `功能模块索引生成进度.md` 了解当前状态
2. **确定批次**: 根据进度文件确定下一个要处理的批次
3. **分析文件**: 逐个分析批次内的.cs文件
4. **提取信息**: 按照上述规则提取方法信息
5. **格式化输出**: 按照模板格式生成索引内容
6. **更新进度**: 完成后更新进度控制文件
7. **质量检查**: 验证生成内容的准确性和完整性

---

**🎯 目标**: 创建最实用、最简洁的ExtensionsTools功能索引，让AI编程更高效！
