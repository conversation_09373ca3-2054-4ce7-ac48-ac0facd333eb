# TextBox右键菜单布局说明

## 菜单结构图

```
┌─────────────────────┐
│      复制           │  ← 1. 文本操作功能组（顶部）
├─────────────────────┤
│    复制所有         │
├─────────────────────┤
│      清空           │
├─────────────────────┤
│     ─────────       │  ← 2. 分隔线
├─────────────────────┤
│      保存           │  ← 3. 历史记录功能组（底部）
├─────────────────────┤
│      历史      ►    │  ← 4. 历史子菜单
│                     │
│  ┌─────────────────┐│
│  │ 历史记录项1     ││  ← 5. 动态生成的历史记录
│  │ 历史记录项2     ││
│  │ 历史记录项3     ││
│  │ ...             ││
│  ├─────────────────┤│
│  │ 清除历史        ││  ← 6. 清除历史功能（子菜单底部）
│  └─────────────────┘│
└─────────────────────┘
```

## 功能分组说明

### 🔝 顶部功能组：文本操作
- **复制**：复制选中文本
- **复制所有**：复制全部文本
- **清空**：清空文本框内容

### 🔽 底部功能组：历史记录管理
- **保存**：将当前文本保存到历史记录
- **历史**：展开历史记录子菜单
  - 历史记录项：点击填入对应内容
  - **清除历史**：删除所有历史记录文件

## 菜单行为特性

### 历史子菜单动态生成
- **有历史记录时**：
  ```
  ┌─────────────────┐
  │ 最新记录        │
  │ 第二新记录      │
  │ 更早记录        │
  ├─────────────────┤
  │ 清除历史        │
  └─────────────────┘
  ```

- **无历史记录时**：
  ```
  ┌─────────────────┐
  │ 无历史记录      │ (灰色不可点击)
  ├─────────────────┤
  │ 清除历史        │ (灰色不可点击)
  └─────────────────┘
  ```

### 确认对话框
- **清空文本**：确定要清空文本框内容吗？
- **清除历史**：确定要清除所有历史记录吗？

### 操作反馈
- **保存成功**：历史记录已保存
- **复制成功**：已复制所有内容到剪贴板
- **清除成功**：历史记录已清除
- **复制失败**：请先选择要复制的文本 / 文本框为空，无内容可复制

## 设计理念

### 1. 功能分组
- 将相关功能归类，提高用户操作效率
- 文本操作功能置顶，作为常用功能
- 历史管理功能置底，作为高级功能

### 2. 层次结构
- 主菜单简洁，避免过多选项
- 历史记录使用子菜单，节省空间
- 清除历史放在子菜单底部，避免误操作

### 3. 用户体验
- 分隔线明确区分功能组
- 动态菜单实时反映状态
- 确认对话框防止误操作
- 即时反馈提升操作体验

## 与原需求对比

### ✅ 完全实现的需求
1. **保存**：✅ 保存到JSON格式文件
2. **历史**：✅ 动态生成历史记录按键
3. **复制**：✅ 复制选定文本到剪贴板
4. **复制所有**：✅ 复制所有字符到剪贴板
5. **清空**：✅ 清空textbox内容

### 🚀 额外增强功能
1. **清除历史**：清除所有历史记录文件
2. **功能分组**：合理的菜单布局和分组
3. **确认机制**：防止误操作的确认对话框
4. **操作反馈**：用户友好的操作提示
5. **动态菜单**：实时反映历史记录状态

## 技术实现要点

### 菜单创建顺序
1. 复制菜单项
2. 复制所有菜单项
3. 清空菜单项
4. 分隔线
5. 保存菜单项
6. 历史菜单项（含子菜单动态生成逻辑）

### 子菜单动态生成
```csharp
historyMenuItem.DropDownOpening += (sender, e) =>
{
    // 清空现有子菜单
    historyMenuItem.DropDownItems.Clear();
    
    // 加载历史记录并生成菜单项
    // 添加分隔线
    // 添加清除历史菜单项
};
```

这样的设计既满足了原始需求，又提供了更好的用户体验和功能完整性。
