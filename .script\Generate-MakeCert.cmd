@echo off
echo === 使用makecert生成VSTO兼容证书 ===

REM 设置变量
set CERT_NAME=HyExcelVsto
set CERT_SUBJECT=CN=%CERT_NAME%
set CERT_FILE=%CERT_NAME%_MakeCert.cer
set PVK_FILE=%CERT_NAME%_MakeCert.pvk
set PFX_FILE=%CERT_NAME%_MakeCert.pfx
set PASSWORD=123456

REM 查找makecert路径
set MAKECERT_PATH=
for /f "delims=" %%i in ('dir /s /b "%ProgramFiles(x86)%\Windows Kits\*\makecert.exe" 2^>nul') do (
    set MAKECERT_PATH=%%i
    goto :found_makecert
)

:found_makecert
if "%MAKECERT_PATH%"=="" (
    echo 错误: 未找到makecert工具
    echo 请安装Windows SDK
    pause
    exit /b 1
)

echo 使用makecert: %MAKECERT_PATH%

REM 生成自签名证书
echo.
echo 步骤1: 生成自签名证书...
"%MAKECERT_PATH%" -r -pe -n "%CERT_SUBJECT%" -b 01/01/2024 -e 01/01/2030 -eku 1.3.6.1.5.5.7.3.3 -ss my -sr currentuser -sky signature -sp "Microsoft RSA SChannel Cryptographic Provider" -sy 12 -sv "%PVK_FILE%" "%CERT_FILE%"

if %ERRORLEVEL% neq 0 (
    echo 错误: 证书生成失败
    pause
    exit /b 1
)

REM 查找pvk2pfx工具
set PVK2PFX_PATH=
for /f "delims=" %%i in ('dir /s /b "%ProgramFiles(x86)%\Windows Kits\*\pvk2pfx.exe" 2^>nul') do (
    set PVK2PFX_PATH=%%i
    goto :found_pvk2pfx
)

:found_pvk2pfx
if "%PVK2PFX_PATH%"=="" (
    echo 警告: 未找到pvk2pfx工具，无法生成PFX文件
    goto :manual_pfx
)

REM 转换为PFX格式
echo.
echo 步骤2: 转换为PFX格式...
"%PVK2PFX_PATH%" -pvk "%PVK_FILE%" -spc "%CERT_FILE%" -pfx "%PFX_FILE%" -po %PASSWORD%

if %ERRORLEVEL% neq 0 (
    echo 错误: PFX转换失败
    goto :manual_pfx
)

echo.
echo === 证书生成成功 ===
echo 证书文件: %CERT_FILE%
echo 私钥文件: %PVK_FILE%
echo PFX文件: %PFX_FILE%
echo 密码: %PASSWORD%
goto :end

:manual_pfx
echo.
echo === 手动转换PFX ===
echo 请使用以下PowerShell命令手动转换:
echo $cert = Get-ChildItem -Path Cert:\CurrentUser\My | Where-Object {$_.Subject -eq "%CERT_SUBJECT%"}
echo $pwd = ConvertTo-SecureString -String "%PASSWORD%" -Force -AsPlainText
echo Export-PfxCertificate -Cert $cert -FilePath "%PFX_FILE%" -Password $pwd

:end
echo.
echo 下一步: 将PFX文件复制到项目目录并更新项目配置
pause
