﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.DiaSymReader.Native.1.7.0\build\Microsoft.DiaSymReader.Native.props" Condition="Exists('..\packages\Microsoft.DiaSymReader.Native.1.7.0\build\Microsoft.DiaSymReader.Native.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{462C8F82-97DD-44A5-8894-7958AA7C466A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>HyExcelVsto</RootNamespace>
    <AssemblyName>HyExcelVsto</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <DefineConstants>VSTO40</DefineConstants>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>C:\共享\Tmp\</PublishUrl>
    <InstallUrl />
    <TargetCulture>zh-chs</TargetCulture>
    <ApplicationVersion>********</ApplicationVersion>
    <AutoIncrementApplicationRevision>true</AutoIncrementApplicationRevision>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateInterval>0</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <ProductName>HyExcelVsto</ProductName>
    <PublisherName />
    <SupportUrl />
    <FriendlyName>HyExcelVsto</FriendlyName>
    <OfficeApplicationDescription />
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8.1 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>Excel</OfficeApplication>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>3</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>latest</LangVersion>
    <RegisterForComInterop>false</RegisterForComInterop>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <DocumentationFile>
    </DocumentationFile>
    <!-- 调试性能优化 -->
    <StartupObject />
    <NoWarn>1701;1702</NoWarn>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>latest</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <!-- <Reference Include="Accessibility" /> 注释掉以避免2.0版本冲突 -->
    <Reference Include="AngleSharp, Version=1.3.0.0, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.1.3.0\lib\net472\AngleSharp.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=*******, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.5.2.1\lib\net462\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.Core.1.7.5\lib\net45\GeoAPI.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI.CoordinateSystems, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.CoordinateSystems.1.7.5\lib\net45\GeoAPI.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.6\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.PowerPoint, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Visio, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Word, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.5.0.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NetTopologySuite, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.2.6.0\lib\netstandard2.0\NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Office, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="ServiceStack, Version=6.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.8.8.0\lib\net472\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=6.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Client.8.8.0\lib\net472\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Common.8.8.0\lib\net472\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Interfaces.8.8.0\lib\net472\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=6.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\packages\ServiceStack.Text.8.8.0\lib\net472\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.1\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.9.0.5\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.5\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Common, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.9.0.5\lib\net462\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.6\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.3\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.9.0.6\lib\net462\System.Memory.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.1\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.1\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.9.0.5\lib\net462\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.6\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.6\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.9.0.5\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Tools.Excel, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="Extensions\ZnWireless.cs" />
    <Compile Include="GlobalSettings.cs" />
    <Compile Include="Extensions\ucExcelCandidateList.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Extensions\ucExcelCandidateList.Designer.cs">
      <DependentUpon>ucExcelCandidateList.cs</DependentUpon>
    </Compile>
    <Compile Include="Extensions\Interfaces\IExcelEventReceiver.cs" />
    <Compile Include="Extensions\Interfaces\IExcelMessageReceiver.cs" />
    <Compile Include="HyLicenseManager.cs" />
    <Compile Include="HyRibbon.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HyRibbon.Designer.cs">
      <DependentUpon>HyRibbon.cs</DependentUpon>
    </Compile>
    <Compile Include="HyUIPermissionManager.cs" />
    <Compile Include="Module.AI\frmAIv2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.AI\frmAIv2.Designer.cs">
      <DependentUpon>frmAIv2.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\AssemblyLoadTracker.cs" />
    <Compile Include="Module.Common\ExcelFileManagerHelper.cs" />
    <Compile Include="Module.Common\ExcelFileRecord.cs" />
    <Compile Include="Module.Common\ExcelFileRecordManager.cs" />
    <Compile Include="Module.Common\FormManager.cs" />
    <Compile Include="Module.Common\frmAssemblyTracker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmCrosshairOverlayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmCrosshairOverlayForm.Designer.cs">
      <DependentUpon>frmCrosshairOverlayForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmDropdownInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmDropdownInputForm.Designer.cs">
      <DependentUpon>frmDropdownInputForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmExcelFileManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmExcelFileManager.Designer.cs">
      <DependentUpon>frmExcelFileManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmLongStringDisplayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmLongStringDisplayForm.Designer.cs">
      <DependentUpon>frmLongStringDisplayForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmTopForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmTopForm.Designer.cs">
      <DependentUpon>frmTopForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmVisioHelper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmVisioHelper.Designer.cs">
      <DependentUpon>frmVisioHelper.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmVisioHelperFunctions3.cs" />
    <Compile Include="Module.Common\frmVisioHelperFunctions2.cs" />
    <Compile Include="Module.Common\frmWordHelper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmWordHelper.Designer.cs">
      <DependentUpon>frmWordHelper.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmWPS打开.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmWPS打开.Designer.cs">
      <DependentUpon>frmWPS打开.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm向下填充.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm向下填充.Designer.cs">
      <DependentUpon>frm向下填充.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm批量查找.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm批量查找.Designer.cs">
      <DependentUpon>frm批量查找.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm文本复制粘贴辅助框.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm文本复制粘贴辅助框.Designer.cs">
      <DependentUpon>frm文本复制粘贴辅助框.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm自动脚本2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm设置标签及下拉候选项.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm设置标签及下拉候选项.Designer.cs">
      <DependentUpon>frm设置标签及下拉候选项.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmVisioHelperFunctions1.cs" />
    <Compile Include="Module.Common\HyFunctions2.cs" />
    <Compile Include="Extensions\MenuManager.cs" />
    <Compile Include="Extensions\TaskPaneManager.cs" />
    <Compile Include="Module.Common\OfficeImageIdExportHelper.cs" />
    <Compile Include="Module.Dialog\dlgBox模板.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Dialog\dlgBox模板.Designer.cs">
      <DependentUpon>dlgBox模板.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm复制及合并.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm复制及合并.Designer.cs">
      <DependentUpon>frm复制及合并.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm备份及发送.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm备份及发送.Designer.cs">
      <DependentUpon>frm备份及发送.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm字符处理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm字符处理.Designer.cs">
      <DependentUpon>frm字符处理.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm文件操作.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm文件操作.Designer.cs">
      <DependentUpon>frm文件操作.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm前后添加删除字符.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm前后添加删除字符.Designer.cs">
      <DependentUpon>frm前后添加删除字符.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frmPPTHelper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frmPPTHelper.Designer.cs">
      <DependentUpon>frmPPTHelper.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Wenzi\ScheduleConverter.cs" />
    <Compile Include="Module.Wenzi\Utils\Logging\ETLoggerAdapter.cs" />
    <Compile Include="Module.Wenzi\Utils\Logging\ILogger.cs" />
    <Compile Include="Module.Wenzi\Utils\Logging\TextBoxLogger.cs" />
    <Compile Include="Module.WX\51Helper\Dx51HelpAPI.cs" />
    <Compile Include="Module.WX\51Helper\Dx51HelpBase.cs" />
    <Compile Include="Module.WX\51Helper\Dx51HelpOther.cs" />
    <Compile Include="Module.WX\51Helper\Dx51HelpTask1.cs" />
    <Compile Include="Module.WX\51Helper\Dx51HelpTask2.cs" />
    <Compile Include="Module.WX\51Helper\Dx51TaskProcessor.cs" />
    <Compile Include="Module.WX\51Helper\frm51Helper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\51Helper\frm51Helper.Designer.cs">
      <DependentUpon>frm51Helper.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\51Helper\frm51Helper2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\AngleExtractor\AngleExtractorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\AngleExtractor\AngleExtractorForm.Designer.cs">
      <DependentUpon>AngleExtractorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\AngleExtractor\AngleExtractorHelper.cs" />
    <Compile Include="Module.WX\frmGPS生成图层.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\frmGPS生成图层.Designer.cs">
      <DependentUpon>frmGPS生成图层.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm填表同步数据.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm填表同步数据.Designer.cs">
      <DependentUpon>frm填表同步数据.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\frm查找站点.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\frm查找站点.Designer.cs">
      <DependentUpon>frm查找站点.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\KmlConverter\frmKmlConverter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\KmlConverter\frmKmlConverter.Designer.cs">
      <DependentUpon>frmKmlConverter.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\KmlConverter\KmlConverterHelper.cs" />
    <Compile Include="Module.WX\KmlConverter\KmlConverterIntegration.cs" />
    <Compile Include="Module.WX\KmlConverter\KmlDescriptionConverter.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Core\OrderDataProcessor.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Core\OrderKmlExceptionHandler.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Core\OrderKmlLogger.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Models\OrderKmlPoint.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Models\OrderKmlResult.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\Models\OrderStationData.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs" />
    <Compile Include="Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs">
      <DependentUpon>OrderKmlGeneratorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs">
      <DependentUpon>frmPolygonGpsConverter.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs" />
    <Compile Include="Module.WX\StationConverter\Core\StationDataConverter.cs" />
    <Compile Include="Module.WX\StationConverter\Core\StationGroupProcessor.cs" />
    <Compile Include="Module.WX\StationConverter\Models\FrequencyBandConfig.cs" />
    <Compile Include="Module.WX\StationConverter\Models\LogicalStation.cs" />
    <Compile Include="Module.WX\StationConverter\Models\PhysicalStation.cs" />
    <Compile Include="Module.WX\StationConverter\StationConverterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\StationConverter\StationConverterForm.Designer.cs">
      <DependentUpon>StationConverterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\StationConverter\StationConverterHelper.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\ErrorHandlingConfig.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\ExcelDataAccess.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\Interfaces\IDataAnalyzer.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\StationDataAnalyzer.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\StationDataProcessor.cs" />
    <Compile Include="Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs" />
    <Compile Include="Module.WX\StationDataProcessor\StationDataProcessorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\StationDataProcessor\StationDataProcessorForm.Designer.cs">
      <DependentUpon>StationDataProcessorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\StationDataProcessor\StationDataProcessorHelper.cs" />
    <Compile Include="Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.Designer.cs">
      <DependentUpon>TowerAccountProcessorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs" />
    <Compile Include="Module.WX\VstoCommon.cs" />
    <Compile Include="Module.Common\HyFunctions.cs" />
    <Compile Include="Module.Common\frm合规检查.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm合规检查.Designer.cs">
      <DependentUpon>frm合规检查.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm工作表管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm工作表管理.Designer.cs">
      <DependentUpon>frm工作表管理.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm自动脚本.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm自动脚本.Designer.cs">
      <DependentUpon>frm自动脚本.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Common\frm设置页眉页脚.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Common\frm设置页眉页脚.Designer.cs">
      <DependentUpon>frm设置页眉页脚.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\frm格式化经纬度.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.WX\frm格式化经纬度.Designer.cs">
      <DependentUpon>frm格式化经纬度.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.WX\HyVstoForm.cs" />
    <Compile Include="Module.Wenzi\Constants\AttendanceConstants.cs" />
    <Compile Include="Module.Wenzi\Controls\ProgressDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Wenzi\frm考勤.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module.Wenzi\frm考勤.Designer.cs">
      <DependentUpon>frm考勤.cs</DependentUpon>
    </Compile>
    <Compile Include="Module.Wenzi\Judges\BaseJudge.cs" />
    <Compile Include="Module.Wenzi\Judges\ExceptionRecordJudge.cs" />
    <Compile Include="Module.Wenzi\Judges\LeaveJudge.cs" />
    <Compile Include="Module.Wenzi\Judges\NightShiftJudge.cs" />
    <Compile Include="Module.Wenzi\Judges\NormalShiftJudge.cs" />
    <Compile Include="Module.Wenzi\Models\AttendanceModels.cs" />
    <Compile Include="Module.Wenzi\Models\AttendanceRuleConfig.cs" />
    <Compile Include="Module.Wenzi\Models\ConfigurationModels.cs" />
    <Compile Include="Module.Wenzi\Models\ExcelSheetConfig.cs" />
    <Compile Include="Module.Wenzi\Services\AttendanceConfigService.cs" />
    <Compile Include="Module.Wenzi\Services\AttendanceService.cs" />
    <Compile Include="Module.Wenzi\Services\Base\DataValidatorBase.cs" />
    <Compile Include="Module.Wenzi\Services\Base\ExcelServiceBase.cs" />
    <Compile Include="Module.Wenzi\Services\EmployeeValidationService.cs" />
    <Compile Include="Module.Wenzi\Services\ExcelService.cs" />
    <Compile Include="Module.Wenzi\Services\ExcelTemplateService.cs" />
    <Compile Include="Module.Wenzi\Services\IAttendanceConfigService.cs" />
    <Compile Include="Module.Wenzi\Utils\ConfigManager.cs" />
    <Compile Include="Module.Wenzi\Utils\Exceptions\AttendanceException.cs" />
    <Compile Include="Module.Wenzi\Utils\Exceptions\DataValidationException.cs" />
    <Compile Include="Module.Wenzi\Utils\Exceptions\ExcelOperationException.cs" />
    <Compile Include="Module.Wenzi\Utils\Extensions\StringExtensions.cs" />
    <Compile Include="Module.Wenzi\Utils\PerformanceMonitor.cs" />
    <Compile Include="Module.Wenzi\Utils\ProgressReporter.cs" />
    <Compile Include="Module.Wenzi\Utils\ShiftNameFormatter.cs" />
    <Compile Include="Module.Wenzi\Utils\TimeHelper.cs" />
    <Compile Include="Module.Wenzi\Utils\TimeSpanExtensions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <EmbeddedResource Include="Extensions\ucExcelCandidateList.resx">
      <DependentUpon>ucExcelCandidateList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HyRibbon.resx">
      <DependentUpon>HyRibbon.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.AI\frmAIv2.resx">
      <DependentUpon>frmAIv2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmExcelFileManager.resx">
      <DependentUpon>frmExcelFileManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmTopForm.resx">
      <DependentUpon>frmTopForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmVisioHelper.resx">
      <DependentUpon>frmVisioHelper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmWordHelper.resx">
      <DependentUpon>frmWordHelper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmWPS打开.resx">
      <DependentUpon>frmWPS打开.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm向下填充.resx">
      <DependentUpon>frm向下填充.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm批量查找.resx">
      <DependentUpon>frm批量查找.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm文本复制粘贴辅助框.resx">
      <DependentUpon>frm文本复制粘贴辅助框.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm设置标签及下拉候选项.resx">
      <DependentUpon>frm设置标签及下拉候选项.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Dialog\dlgBox模板.resx">
      <DependentUpon>dlgBox模板.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm复制及合并.resx">
      <DependentUpon>frm复制及合并.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm备份及发送.resx">
      <DependentUpon>frm备份及发送.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm合规检查.resx">
      <DependentUpon>frm合规检查.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm工作表管理.resx">
      <DependentUpon>frm工作表管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm字符处理.resx">
      <DependentUpon>frm字符处理.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm前后添加删除字符.resx">
      <DependentUpon>frm前后添加删除字符.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm自动脚本.resx">
      <DependentUpon>frm自动脚本.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm设置页眉页脚.resx">
      <DependentUpon>frm设置页眉页脚.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm文件操作.resx">
      <DependentUpon>frm文件操作.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frmPPTHelper.resx">
      <DependentUpon>frmPPTHelper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\51Helper\frm51Helper.resx">
      <DependentUpon>frm51Helper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\frmGPS生成图层.resx">
      <DependentUpon>frmGPS生成图层.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Common\frm填表同步数据.resx">
      <DependentUpon>frm填表同步数据.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\frm格式化经纬度.resx">
      <DependentUpon>frm格式化经纬度.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\frm查找站点.resx">
      <DependentUpon>frm查找站点.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.Wenzi\frm考勤.resx">
      <DependentUpon>frm考勤.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.resx">
      <DependentUpon>frmPolygonGpsConverter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\AngleExtractor\AngleExtractorForm.resx">
      <DependentUpon>AngleExtractorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\StationConverter\StationConverterForm.resx">
      <DependentUpon>StationConverterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\StationDataProcessor\StationDataProcessorForm.resx">
      <DependentUpon>StationDataProcessorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.resx">
      <DependentUpon>TowerAccountProcessorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="config\config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\.et\ETConfig.ini">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="config\.template\hyExceData.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="config\.stationdata\StationDataProcessor_4G.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="config\.stationdata\StationDataProcessor_5G.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="config\.et\StringPrefixSuffix.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\字符规整预置.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\批量查找后缀.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\收藏文件.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\查找文件预置目录.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\正则表达式预置.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="ExcelVsto_TemporaryKey.pfx" />
    <None Include="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Module.WX\51Helper\功能模块总结.md" />
    <None Include="Module.WX\KmlConverter\README.md" />
    <None Include="Module.WX\PolygonGpsConverter\README.md" />
    <None Include="Module.WX\PolygonGpsConverter\使用示例.md" />
    <None Include="Module.WX\PolygonGpsConverter\功能完成总结.md" />
    <None Include="Module.WX\StationConverter\README.md" />
    <None Include="Module.WX\StationDataProcessor\基站数据处理器使用说明.md" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Ribbon.cs">
      <XmlRibbon>true</XmlRibbon>
    </Compile>
    <Compile Include="HyExcelVstoSettings1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>HyExcelVstoSettings.settings</DependentUpon>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <None Include="HyExcelVstoSettings.settings">
      <Generator>PublicSettingsSingleFileGenerator</Generator>
      <LastGenOutput>HyExcelVstoSettings1.Designer.cs</LastGenOutput>
    </None>
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Images\api.png" />
    <Content Include="Images\office.png" />
    <Content Include="Images\panel.png" />
    <Content Include="Images\run.png" />
    <Content Include="Images\unlock.png" />
    <None Include="Resources\拨号键盘_1753836891.ico" />
    <None Include="Resources\拨号键盘_1753836891.png" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="VisioGraph">
      <Guid>{000D1140-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="config\.ai\" />
    <Folder Include="config\.data\" />
    <Folder Include="config\.xw\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ExtensionsTools\ExtensionsTools.csproj">
      <Project>{163811e3-bcfb-4e4d-9d21-59068851d958}</Project>
      <Name>ExtensionsTools</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <!--<PropertyGroup>
    <ManifestKeyFile>HyExcelVsto_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>50D17899991C65304AC2BB73529D6ED2E1F73456</ManifestCertificateThumbprint>
  </PropertyGroup>-->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>VSTO40;DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>false</RegisterForComInterop>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <GenerateSerializationAssemblies>On</GenerateSerializationAssemblies>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>VSTO40;TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>HyExcelVsto_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>65B7C1889E9F176886012DC0D2BE679DCF213CAD</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>ExcelVsto_TemporaryKey.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <!--<PropertyGroup>
    <AssemblyOriginatorKeyFile>HyExcelVsto.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>HyExcelVsto_TemporaryKey.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>-->
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="Excel" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="Excel" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\Excel\InstallRoot\Path#excel.exe" DebugInfoCommandLine="/x" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="Excel" GeneratedCodeNamespace="HyExcelVsto" PublishedHash="69C324AB27932AA2FBF2B7EA72250886FF164DE6" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" PublishedHash="138C78A00F92A73C2B4657B07C3189940CB70035" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets'))" />
  </Target>
  <Import Project="..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
</Project>