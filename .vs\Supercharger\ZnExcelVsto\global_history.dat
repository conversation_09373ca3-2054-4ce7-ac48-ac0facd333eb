<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnRibbon.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnRibbon.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-29T14:31:56.6663874+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.button正则表达式提取字符</ID><ImageSource>img\tvi\x_var-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>button正则表达式提取字符</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnRibbon.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-29T14:31:55.9242567+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.group字符格式</ID><ImageSource>img\tvi\x_var-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>group字符格式</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnRibbon.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-29T14:31:54.2456844+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper_bak\ZnExcelVSTO\ZnRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.InitializeConfigurationSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeConfigurationSettings</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:22:50.1797546+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.hyRibbon1_Load#void#object, RibbonUIEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>hyRibbon1_Load</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:22:48.7053027+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ZnRibbonClass</ItemName><ItemPath>ZnExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:21:12.6064521+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ZnExcelVsto.ZnRibbonClass.button51自动上传_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button51自动上传_Click</ItemName><ItemPath>ZnExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:21:10.1573809+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ZnExcelVSTO\ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>ZnExcelVsto</ProjectName></ProjectData>