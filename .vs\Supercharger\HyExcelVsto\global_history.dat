<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ITaskApplicationService.ExecuteBatchUploadAsync#Task&lt;BatchTaskExecutionResult&gt;#BatchUploadRequest, IProgress&lt;BatchProgress&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteBatchUploadAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ITaskApplicationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T10:36:11.5125222+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\ITaskApplicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IApiClient.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ApiResponse.Failurestatic##ApiResponse&lt;T&gt;#string, int</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Failure</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ApiResponse</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:21:07.4411043+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ApiResponse.Successstatic##ApiResponse&lt;T&gt;#T</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Success</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.ApiResponse</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:11:52.2077719+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.Core.Interfaces.IBatchOperationService</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IBatchOperationService</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.Core.Interfaces</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:32:05.5162222+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.获取登录信息#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>获取登录信息</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T10:25:39.6159293+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.button下载交付列表并更新_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button下载交付列表并更新_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:59:58.312474+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm51Helper</ItemName><ItemPath>HyExcelVsto.Module.WX</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:59:56.5753484+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.frm51Helper_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm51Helper_Load</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:58:11.1167215+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.cookiesFile</ID><ImageSource>img\tvi\x_var-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>cookiesFile</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:55:31.8420393+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.frm51Helper##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>frm51Helper</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:55:27.2653156+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.button登录_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button登录_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:32:09.78103+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.Cookies</ID><ImageSource>img\tvi\x_var-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Cookies</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:01:17.7839999+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ProcessingOptions</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:05:08.7799226+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions.DataRange</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DataRange</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:05:07.7590274+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorMainForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorMainForm.BtnTowerAccountProcessor_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnTowerAccountProcessor_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorMainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorMainForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:34:02.8337447+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorMainForm.BtnStationDataProcessor_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnStationDataProcessor_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorMainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorMainForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:05:32.7597+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorMainForm.BtnStationConverter_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnStationConverter_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorMainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorMainForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:58:08.2120235+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorMainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.BatchOperationForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.BatchOperationForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-12T21:45:27.8639974+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm备份及发送</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:58.683148+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.toolStripMenuItem打开配置文件_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>toolStripMenuItem打开配置文件_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T15:00:40.5443144+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.toolStripMenuItem刷新配置_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>toolStripMenuItem刷新配置_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:57:47.9674485+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.frm提取字符_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm提取字符_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:43:17.7997583+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm字符处理</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:35:33.2560718+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.frm字符处理##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>frm字符处理</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:35:31.1763857+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.LoadRegexRulesFromConfig#List&lt;RegexRule&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadRegexRulesFromConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:32:51.875135+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.ListViewRegex_SelectedIndexChanged#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ListViewRegex_SelectedIndexChanged</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:31:07.5237895+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.listView内置正则表达式_SelectedIndexChanged#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>listView内置正则表达式_SelectedIndexChanged</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:17:13.7446883+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.TabControl_SelectedIndexChanged#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TabControl_SelectedIndexChanged</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:17:10.1446447+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.tabControl1_SelectedIndexChanged#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>tabControl1_SelectedIndexChanged</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:17:05.2382955+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.InitializeCharacterProcessingForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeCharacterProcessingForm</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:16:49.9714526+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.ClearCustomRules_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ClearCustomRules_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:16:42.5331327+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.清空ToolStripMenuItem_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>清空ToolStripMenuItem_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:16:39.6374393+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmPPTHelper</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmPPTHelper</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:56:00.5149975+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmPPTHelper.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.Common.frmPPTHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:51:26.8413871+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.InitializeServices#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeServices</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:11:54.1660376+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.frm51HelperV2_Load#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm51HelperV2_Load</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:08:30.2353004+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.OnOperationCompleted#void#object, OperationCompletedEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>OnOperationCompleted</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:05:16.3258329+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm51HelperV2</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T21:52:55.1853096+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.frm51HelperV2_FormClosing#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm51HelperV2_FormClosing</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T21:52:33.5172117+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.CreateStatusGroup#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateStatusGroup</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T21:46:57.7334819+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.SetupEventHandlers#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetupEventHandlers</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T21:46:48.6857553+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX._51HelperV2.UI.frm51HelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-11T21:46:43.813233+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\frm51HelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorForm.BtnExtractTelecomPart_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnExtractTelecomPart_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T09:09:43.0769933+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorForm.BtnExtractAngles_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnExtractAngles_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T09:08:53.5659621+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorEntry.CheckExtensionsToolsstatic##bool#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CheckExtensionsTools</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName><TimeStamp>2025-07-17T15:27:08.4708455+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorEntry.ShowStationDataProcessorstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowStationDataProcessor</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName><TimeStamp>2025-07-17T15:26:52.7225493+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\StationDataProcessorEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETLogDisplayControl.Dispose#void#bool</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETLogDisplayControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-17T00:11:51.9495984+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文本复制粘贴辅助框.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm文本复制粘贴辅助框.ListView1_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ListView1_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm文本复制粘贴辅助框</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName><TimeStamp>2025-07-26T15:42:50.4892552+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文本复制粘贴辅助框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置页眉页脚.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm设置页眉页脚</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm设置页眉页脚</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置页眉页脚.cs</ProjectItemFileName><TimeStamp>2025-07-30T11:46:34.436712+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm设置页眉页脚.frm设置页眉页脚_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm设置页眉页脚_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frm设置页眉页脚</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置页眉页脚.cs</ProjectItemFileName><TimeStamp>2025-07-30T11:44:08.0607896+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm设置页眉页脚.ApplyHeaderFooterToWorksheet#void#Worksheet</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ApplyHeaderFooterToWorksheet</ItemName><ItemPath>HyExcelVsto.Module.Common.frm设置页眉页脚</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置页眉页脚.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:06:13.0499386+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置页眉页脚.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:56:21.4688912+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter.TxtRangeSelect_Enter#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TxtRangeSelect_Enter</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:44:52.9302414+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmPolygonGpsConverter</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:44:37.2448568+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter.SetPlaceholderText#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetPlaceholderText</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:44:20.0276286+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter.btnSelectRange_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnSelectRange_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:43:56.1644844+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter.btnConvert_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnConvert_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:41:35.7810019+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.AI.frmAIv2.ButtonExecute_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ButtonExecute_Click</ItemName><ItemPath>HyExcelVsto.Module.AI.frmAIv2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:56:40.1327574+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager.IsFirstOccurrence#bool#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsFirstOccurrence</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:13:16.6951026+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DataCacheManager</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:05:31.9021848+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager.Initialize#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Initialize</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:05:31.1088214+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager.RebuildRowMappingOnly#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RebuildRowMappingOnly</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.DataCacheManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:01:04.2824981+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache.SectorName</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SectorName</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:00:50.8785114+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache.StationName</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>StationName</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:00:50.4187702+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache.FrequencyBand</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FrequencyBand</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:00:49.8148436+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache.RowNumber</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RowNumber</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.RowDataCache</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T19:00:36.6005858+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\DataCacheManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.ReloadConfig#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ReloadConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:53.6817765+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessorConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:53.6817765+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.LogLevel</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogLevel</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:44.0869911+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.SaveConfig#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SaveConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:33.7706328+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.SaveConfiguration#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SaveConfiguration</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:18.3328482+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.LoadConfiguration#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadConfiguration</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:14.7689015+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.GetConfigFilePath#string#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetConfigFilePath</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:24:05.5522019+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.ResetToDefault#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ResetToDefault</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:23:55.6334489+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.HideColumns</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HideColumns</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:23:02.3193726+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.FrequencyMapping</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FrequencyMapping</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:22:38.4778621+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.GetHiddenColumnsArray#string[]#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetHiddenColumnsArray</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:22:15.3134025+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.HiddenColumns</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HiddenColumns</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:22:02.5042735+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.InitializeConfigFile#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeConfigFile</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:21:30.2909551+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.CreateDefaultConfigFile#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateDefaultConfigFile</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:21:24.720338+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.GetDefaultConfigContent#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetDefaultConfigContent</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:21:19.4868337+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig.GetDefaultHiddenColumns#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetDefaultHiddenColumns</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Configuration.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:21:11.4571001+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Configuration\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm字符处理.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.Common.frm字符处理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:21:37.7849525+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm字符处理.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TowerAccountProcessorForm</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:33:38.9061878+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm.BtnExtractTelecomPart_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnExtractTelecomPart_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:05:10.191265+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.BatchOperationForm.BatchOperationForm_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BatchOperationForm_Load</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.BatchOperationForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName><TimeStamp>2025-07-12T14:11:39.7699234+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\BatchOperationForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.ThisAddIn.ConfigurationSettings</ID><ImageSource>img\tvi\x_var-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConfigurationSettings</ItemName><ItemPath>HyExcelVsto.ThisAddIn</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-07-29T09:48:50.696384+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ThisAddIn.InitializeSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeSettings</ItemName><ItemPath>HyExcelVsto.ThisAddIn</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-07-28T21:25:10.2574507+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ThisAddIn.ThisAddIn_Startup#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThisAddIn_Startup</ItemName><ItemPath>HyExcelVsto.ThisAddIn</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-07-28T21:24:58.2462415+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ThisAddIn.InitializeSecondaryComponentsAsync#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeSecondaryComponentsAsync</ItemName><ItemPath>HyExcelVsto.ThisAddIn</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-07-28T21:24:52.6230977+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlConverterHelper.ConvertKmlstatic##bool#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertKml</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlConverterHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:18:52.1230833+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.LoadHeaderConfiguration#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadHeaderConfiguration</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:11:17.4915962+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.InitializeTargetHeaders#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeTargetHeaders</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:09:55.6826251+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.LoadDefaultHeaderConfiguration#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadDefaultHeaderConfiguration</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:09:45.4135865+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessor</ItemName><ItemPath>HyExcelVsto.Module.WX</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:08:56.9749595+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessor##Application, Worksheet</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>StationDataProcessor</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:04:46.7783477+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.FormatTargetWorksheet#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatTargetWorksheet</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T15:57:45.3802391+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessor.HEADER_开通时间</ID><ImageSource>img\tvi\x_property-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HEADER_开通时间</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-19T12:58:40.2717633+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.UI.OrderKmlGeneratorForm.Dispose#void#bool</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.UI.OrderKmlGeneratorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-23T17:19:51.2547052+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\GlobalSettings.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.GlobalSettings.EnableHelpForm</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>EnableHelpForm</ItemName><ItemPath>HyExcelVsto.GlobalSettings</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\GlobalSettings.cs</ProjectItemFileName><TimeStamp>2025-06-21T17:31:34.5618314+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.GlobalSettings.EnableHelpForm.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.GlobalSettings.EnableHelpForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\GlobalSettings.cs</ProjectItemFileName><TimeStamp>2025-06-21T17:31:34.5607625+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\GlobalSettings.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessorConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:17:52.6086154+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.Initializestatic##void#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Initialize</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:12:19.0950768+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskInfo.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo.CreateTime.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo.CreateTime</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:33:31.8062+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo.CreateTime</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateTime</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:33:28.1235062+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo.TaskInfo##JsonObject</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TaskInfo</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskInfo</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:33:17.2017494+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.button清除标色_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button清除标色_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:58:03.011974+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.button按上一行的值填充_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button按上一行的值填充_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:58:02.2176965+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.GetTargetRange#Range#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetTargetRange</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:57:59.5237275+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.GetSelectedFillMode#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetSelectedFillMode</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:57:53.83146+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.UpdateOptionDescriptions#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateOptionDescriptions</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:57:50.9357555+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.InitializeDialog#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeDialog</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:57:50.2535183+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.frm向下填充_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm向下填充_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:57:49.7659945+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.button候选项_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button候选项_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T16:37:13.1695412+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充.button填充_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button填充_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm向下填充</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T16:30:47.2316058+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm向下填充</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm向下填充</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName><TimeStamp>2025-07-27T16:30:46.3891755+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm向下填充.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmCrosshairOverlayForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmCrosshairOverlayForm.WndProc#void#Message</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WndProc</ItemName><ItemPath>HyExcelVsto.Module.Common.frmCrosshairOverlayForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:34:46.1497457+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmCrosshairOverlayForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper.ReportProgress#void#int, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ReportProgress</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-24T09:06:57.0827433+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>OrderKmlGeneratorHelper</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-24T09:06:52.7681282+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper.Dispose#void#bool</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-23T22:30:19.4507475+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper.GetWorksheetByName#Worksheet#Workbook, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetWorksheetByName</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-23T22:22:04.9476442+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper.GenerateKmlFromExcel#OrderKmlResult#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateKmlFromExcel</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.OrderKmlGeneratorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-23T17:32:48.3189808+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\OrderKmlGeneratorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-12T17:48:44.560462+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess.IsColumnExists#bool#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsColumnExists</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:02.954435+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess.GetDataRowCount#int#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetDataRowCount</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:02.2625067+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess.SetAutoFilterAsync#Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SetAutoFilterAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:22:20.1217197+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.IExcelDataAccess</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IExcelDataAccess</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:17:52.1190415+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterHelper.ParsePolygonCoordinatesstatic##List&lt;double[]&gt;#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ParsePolygonCoordinates</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:45:53.0570455+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置单元格.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm设置单元格.frm设置单元格##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>frm设置单元格</ItemName><ItemPath>HyExcelVsto.Module.Common.frm设置单元格</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置单元格.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:06:00.7509488+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm设置单元格</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm设置单元格</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置单元格.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:05:47.2543091+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm设置单元格.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.ClsAuthorization.ForceRefreshPermissionsAndUIstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ForceRefreshPermissionsAndUI</ItemName><ItemPath>HyExcelVsto.ClsAuthorization</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName><TimeStamp>2025-06-28T10:02:11.3002615+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ClsAuthorization</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ClsAuthorization</ItemName><ItemPath>HyExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName><TimeStamp>2025-06-28T09:51:59.3005344+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ClsAuthorization.InitializePermissionManagersstatic##void#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializePermissionManagers</ItemName><ItemPath>HyExcelVsto.ClsAuthorization</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName><TimeStamp>2025-06-28T09:51:17.7668144+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyPermissionKeys</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HyPermissionKeys</ItemName><ItemPath>HyExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName><TimeStamp>2025-06-28T09:51:11.3249308+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ClsAuthorization.HasPermissionstatic##bool#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HasPermission</ItemName><ItemPath>HyExcelVsto.ClsAuthorization</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName><TimeStamp>2025-06-28T09:35:31.5843337+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ClsAuthorization.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmPPTHelper.BindConfigurationSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BindConfigurationSettings</ItemName><ItemPath>HyExcelVsto.Module.Common.frmPPTHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:31:22.5293903+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmPPTHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frmGPS生成图层.GeneratePointKml#void#Range, Range, Range, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GeneratePointKml</ItemName><ItemPath>HyExcelVsto.Module.WX.frmGPS生成图层</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:01:25.4489604+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frmGPS生成图层.GenerateKmlFile#void#Range, Range, Range, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GenerateKmlFile</ItemName><ItemPath>HyExcelVsto.Module.WX.frmGPS生成图层</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:01:21.0705635+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frmGPS生成图层.button生成KML_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button生成KML_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frmGPS生成图层</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:01:15.4277774+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:57:52.0898745+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces.ITaskManagementService</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ITaskManagementService</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:03:29.2507168+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces.ITaskManagementService.GenerateDefaultConnectionTableAsync#Task&lt;OperationResult&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateDefaultConnectionTableAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces.ITaskManagementService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:02:31.5140415+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\ITaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.CookieManagerAdapter.RefreshLoginInfoAsyncstatic##Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RefreshLoginInfoAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.CookieManagerAdapter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName><TimeStamp>2025-07-11T23:27:19.001979+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.CookieManagerAdapter.ValidateLoginStatusAsyncstatic##Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ValidateLoginStatusAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.CookieManagerAdapter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName><TimeStamp>2025-07-11T23:05:28.5171948+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.ApiClientAdapter.UploadFileJsonObjectAsync#Task&lt;JsonObject&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileJsonObjectAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.ApiClientAdapter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:52:38.5629261+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.ApiClientAdapter.PostHtmlAsync#Task&lt;string&gt;#string, string, string, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>PostHtmlAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.ApiClientAdapter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:52:11.4132821+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClientAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpAPI.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.UploadFilestatic##Vsto51FileInfo#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFile</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:00:56.4583342+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpAPI.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessor</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:34:05.2808885+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.ProcessStationDataAsync#Task&lt;ProcessResult&gt;#Worksheet</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessStationDataAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:29:50.7262206+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.ValidateDataAsync#Task&lt;bool&gt;#Worksheet</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidateDataAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:25:30.8231412+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm台账扇区汇总.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm台账扇区汇总.btnProcess_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnProcess_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm台账扇区汇总</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm台账扇区汇总.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:11:58.9420059+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm台账扇区汇总.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterExample.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterExample.ExecuteStationConversionstatic##void#Workbook</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteStationConversion</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterExample</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName><TimeStamp>2025-07-21T15:28:33.9367479+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterExample.GetSourceDataRangestatic##Range#Worksheet</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetSourceDataRange</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterExample</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName><TimeStamp>2025-07-21T15:28:20.7104884+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmExcelFileManager.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.Common.frmExcelFileManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:04:00.7024326+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETLogDisplayControl</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLogDisplayControl</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName><TimeStamp>2025-07-17T09:40:17.3698739+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETLogDisplayControl.ETLogDisplayControl##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETLogDisplayControl</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETLogDisplayControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName><TimeStamp>2025-07-17T09:38:40.4854471+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorIntegrationTest.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorIntegrationTest.CreateTestDatastatic##void#Worksheet</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateTestData</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorIntegrationTest</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorIntegrationTest.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:17:59.6923179+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorIntegrationTest.TestTowerAccountProcessorIntegrationstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TestTowerAccountProcessorIntegration</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorIntegrationTest</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorIntegrationTest.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:17:36.4659661+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorIntegrationTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填充选择对话框.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm填充选择对话框</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm填充选择对话框</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填充选择对话框.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:45:24.6233819+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm填充选择对话框.GetTargetRange#Range#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetTargetRange</ItemName><ItemPath>HyExcelVsto.Module.Common.frm填充选择对话框</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填充选择对话框.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:04:08.4501813+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填充选择对话框.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper.ExtractTelecomPartstatic##ProcessingResult#Worksheet</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExtractTelecomPart</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:15:38.164171+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmTopMostForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmTopMostForm</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:33:19.8863179+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmTopMostForm.InitializeExcelHwndMonitor#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeExcelHwndMonitor</ItemName><ItemPath>HyExcelVsto.Module.Common.frmTopMostForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:31:34.8175228+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces.IBatchOperationService</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IBatchOperationService</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Interfaces</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T10:36:04.9568477+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Interfaces\IBatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.WorksPointInfo.WorksPointInfo##JsonObject</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WorksPointInfo</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.WorksPointInfo</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:36:14.6817612+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\WorksPointInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationService.ApproveAllAsync#Task&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ApproveAllAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T17:45:43.9558421+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.WorksPointUpdateTask</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WorksPointUpdateTask</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T17:43:37.294662+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationService.GetApprovalListAsync#Task&lt;System.Text.Json.Nodes.JsonObject&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetApprovalListAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T14:11:42.7766226+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\ConfigManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Wenzi.Utils.ConfigManager.ConfigManager##string, Logging.ILogger, ExcelService</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConfigManager</ItemName><ItemPath>HyExcelVsto.Module.Wenzi.Utils.ConfigManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:01:38.8595145+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\ConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.Controls.LogDisplayControl.Dispose#void#bool</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.Controls.LogDisplayControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName><TimeStamp>2025-07-12T21:40:11.0538468+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\Controls\LogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送.frmBackAndSend_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmBackAndSend_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frm备份及发送</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName><TimeStamp>2025-07-30T08:44:01.1645729+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送.button保存到临时文件夹_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button保存到临时文件夹_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm备份及发送</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName><TimeStamp>2025-07-29T10:12:38.0953636+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送.button另存到临时文件夹_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button另存到临时文件夹_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm备份及发送</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName><TimeStamp>2025-07-29T10:12:34.5615563+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送.生成临时文件存档#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>生成临时文件存档</ItemName><ItemPath>HyExcelVsto.Module.Common.frm备份及发送</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName><TimeStamp>2025-07-29T10:00:59.9662355+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm备份及发送.SetupContextMenuForPathTextBoxes#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetupContextMenuForPathTextBoxes</ItemName><ItemPath>HyExcelVsto.Module.Common.frm备份及发送</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName><TimeStamp>2025-07-29T09:43:25.9615697+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm备份及发送.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.AdditionalData.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.AdditionalData</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:50.1298182+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.AdditionalData</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AdditionalData</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:44.9216831+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.OperationTime.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.OperationTime</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:31.3721844+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult.OperationTime</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>OperationTime</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.UploadResult</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:31.3082126+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\UploadResult.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm复制及合并.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm复制及合并.dlgBox复制_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>dlgBox复制_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frm复制及合并</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm复制及合并.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:31:38.8332897+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm复制及合并.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractionParameters</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExtractionParameters</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:59:53.3029038+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AngleExtractorHelper</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:58:14.0492515+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractionParameters.MaxRowsLimit</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>MaxRowsLimit</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractionParameters</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:58:11.4925022+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractAnglesstatic##ExtractionResult#ExtractionParameters</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExtractAngles</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:57:35.7986737+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ValidateParametersstatic##ExtractionResult#ExtractionParameters</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ValidateParameters</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:57:31.4186337+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractionParameters.MaxRowsLimit.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorHelper.ExtractionParameters.MaxRowsLimit</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T21:57:20.7161959+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填表同步数据.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm填表同步数据</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm填表同步数据</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:34.5391601+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm填表同步数据.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmDropdownInputForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmDropdownInputForm.WndProc#void#Message</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WndProc</ItemName><ItemPath>HyExcelVsto.Module.Common.frmDropdownInputForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmDropdownInputForm.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:49:31.1826114+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmDropdownInputForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterForm.BtnCreateWorkbook_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnCreateWorkbook_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:05:14.230972+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationConverterForm</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:57:44.0769785+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterForm.StationConverterForm##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>StationConverterForm</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:57:01.862415+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterForm.ReleaseCustomResources#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ReleaseCustomResources</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:21:48.5805833+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.HyRibbonClass.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.HyRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:50:56.8614848+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.TryLoadNewFormatCookiesstatic##bool#string, Dictionary&lt;string, string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TryLoadNewFormatCookies</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:34:08.654131+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm.BtnExtractTelecomPart_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnExtractTelecomPart_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:15:50.0019374+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm.InitializeTowerAccountProcessor#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeTowerAccountProcessor</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:14:39.336586+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor\TowerAccountProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationGroupProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor.GroupLogicalStations#List&lt;PhysicalStation&gt;#List&lt;LogicalStation&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GroupLogicalStations</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:46:29.9688234+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor.GroupByGpsProximity#List&lt;PhysicalStation&gt;#List&lt;LogicalStation&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GroupByGpsProximity</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:45:08.9717212+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor.GroupByStationName#Dictionary&lt;string, List&lt;LogicalStation&gt;&gt;#List&lt;LogicalStation&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GroupByStationName</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationGroupProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:43:33.3123168+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationGroupProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm.ButtonLogin_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ButtonLogin_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:11:53.9571135+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm.ButtonDefaultConnectionTable_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ButtonDefaultConnectionTable_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:00:43.2856452+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>MainForm</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName><TimeStamp>2025-07-12T21:46:33.0662829+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm.ButtonDownloadTaskManagement_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ButtonDownloadTaskManagement_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.UI.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName><TimeStamp>2025-07-12T21:38:03.4522809+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\MainForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-21T22:51:46.8323548+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Utils\ExcelHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Utils.ExcelHelper.ValidateRangeSelectionstatic##ValidationResult#Range, int, string, ILogService</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ValidateRangeSelection</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Utils.ExcelHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:43:03.8790443+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Utils.ExcelHelper.BatchUpdateMultiColumnResultsstatic##void#Range, object[,], ILogService</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>BatchUpdateMultiColumnResults</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Utils.ExcelHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:43:02.3044673+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Utils\ExcelHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LogService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.LogService.Debug#void#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Debug</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.LogService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:39:12.1803612+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.LogService.Info#void#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Info</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.LogService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:14:51.2944143+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LogService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\PhysicalStation.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Models.PhysicalStation.GetHashCode#int#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetHashCode</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Models.PhysicalStation</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName><TimeStamp>2025-07-21T22:59:46.7006964+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\PhysicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm工作表管理.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm工作表管理.SetSheetVisible#bool#ListViewItem, XlSheetVisibility</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetSheetVisible</ItemName><ItemPath>HyExcelVsto.Module.Common.frm工作表管理</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm工作表管理.cs</ProjectItemFileName><TimeStamp>2025-07-02T12:24:39.3666042+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm工作表管理.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\WorksPointService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Services.WorksPointService.GetWorksPointBaseInfoAsync#Task&lt;object&gt;#string, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetWorksPointBaseInfoAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Services.WorksPointService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName><TimeStamp>2025-07-11T23:32:57.4076518+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\WorksPointService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.FormManager.ShowForm#void#Form, XlFormPosition</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowForm</ItemName><ItemPath>HyExcelVsto.Module.Common.FormManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs</ProjectItemFileName><TimeStamp>2025-06-21T17:30:57.1417221+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.UI.UIIntegrationAdapter.ParseChangePointItemsFromRange#List&lt;BatchUploadItem&gt;#Range</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ParseChangePointItemsFromRange</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.UI.UIIntegrationAdapter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName><TimeStamp>2025-07-12T10:36:11.1929317+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\UI\UIIntegrationAdapter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\LogicalStation.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Models.LogicalStation.GetHashCode#int#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetHashCode</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Models.LogicalStation</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName><TimeStamp>2025-07-21T22:59:46.2198103+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Models\LogicalStation.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm查找站点.UpdatePolygonSearchModeVisibility#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdatePolygonSearchModeVisibility</ItemName><ItemPath>HyExcelVsto.Module.WX.frm查找站点</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:20:41.2014805+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm查找站点.ucERS查找多边形_执行_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ucERS查找多边形_执行_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm查找站点</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.cs</ProjectItemFileName><TimeStamp>2025-07-23T00:01:33.6964947+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter.ExtractPrefixBeforeFirstDashstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExtractPrefixBeforeFirstDash</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:19:27.3470111+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter.GenerateDescriptionstatic##string#string, string, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GenerateDescription</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:19:21.9128068+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter.ProcessPlacemarksstatic##int#XDocument</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ProcessPlacemarks</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:19:05.0377432+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter.ConvertKmlWithDescriptionstatic##bool#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertKmlWithDescription</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlDescriptionConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:18:57.5795069+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlDescriptionConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationTemplate.GetCellDoubleValue#double#Range</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetCellDoubleValue</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.BatchOperationTemplate</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName><TimeStamp>2025-07-12T17:46:51.3483884+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\BatchOperationTemplate.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm批量查找.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm批量查找.button取消标色后关闭_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button取消标色后关闭_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.frm批量查找</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm批量查找.cs</ProjectItemFileName><TimeStamp>2025-07-27T20:43:49.7364039+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm批量查找.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\Exceptions\DataValidationException.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Wenzi.Utils.Exceptions.DataValidationException.DataValidationException##string, string, string, string, string, string, DateTime?, Exception</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DataValidationException</ItemName><ItemPath>HyExcelVsto.Module.Wenzi.Utils.Exceptions.DataValidationException</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName><TimeStamp>2025-07-20T09:28:58.0566673+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Wenzi\Utils\Exceptions\DataValidationException.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmWordHelper</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmWordHelper</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:55:55.2880451+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataAnalyzer.ParseStationName#string#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ParseStationName</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataAnalyzer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName><TimeStamp>2025-07-20T11:44:01.381716+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.HyRibbonClass.buttonHy专用工具_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>buttonHy专用工具_Click</ItemName><ItemPath>HyExcelVsto.HyRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:02:54.5804357+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyRibbonClass.btnStationConverter_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnStationConverter_Click</ItemName><ItemPath>HyExcelVsto.HyRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:56:15.975234+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyRibbonClass.btnTowerAccountProcessor_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnTowerAccountProcessor_Click</ItemName><ItemPath>HyExcelVsto.HyRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:50:24.9455895+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.ShowProcessingOptionsSummary#void#Core.Interfaces.ProcessingOptions</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowProcessingOptionsSummary</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:09.5025394+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.ConfirmProcessingOperation#bool#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ConfirmProcessingOperation</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:08.7763348+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.StartProcessingAsync#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StartProcessingAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:51:49.9209772+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.BtnProcess_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnProcess_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:51:37.7402088+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FileUploadService</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName><TimeStamp>2025-07-12T13:55:05.7430103+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService.UpdateBudgetServerStatusAsync#Task&lt;bool&gt;#TaskContext, FileInfo, double, double, double</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateBudgetServerStatusAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:37:23.1068817+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService.UploadFileOnlyAsync#Task&lt;FileInfo&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileOnlyAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:37:13.0562849+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService.GetWorksPointInfoAsync#Task&lt;WorksPointInfo&gt;#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetWorksPointInfoAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.FileUploadService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:37:02.0488187+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.button批量查找文件_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button批量查找文件_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:14:01.7231402+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ZnRibbonClass</ItemName><ItemPath>HyExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:14:00.1443471+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.button批量复制文件_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button批量复制文件_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:09:52.204626+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.button批量找文件_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button批量找文件_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:09:45.9203399+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.button文件操作_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button文件操作_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:09:15.7612977+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.buttonPPT生成修改_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>buttonPPT生成修改_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:08:59.9514689+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.buttonPPT转PDF_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>buttonPPT转PDF_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:08:32.4095149+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.buttonWord转PDF_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>buttonWord转PDF_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:07:02.012536+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.buttonWord生成修改_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>buttonWord生成修改_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:06:48.8284778+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.button正则表达式提取字符_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button正则表达式提取字符_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:03:22.7276167+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.ZnRibbonClass.btn标记重复值_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btn标记重复值_Click</ItemName><ItemPath>HyExcelVsto.ZnRibbonClass</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName><TimeStamp>2025-06-29T17:01:03.1397758+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ZnRibbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry.ProcessCurrentSelectionstatic##PolygonConversionResult#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessCurrentSelection</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:47:03.3146527+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PolygonGpsConverterEntry</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:47:02.2380071+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry.ShowConverterstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowConverter</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:46:53.9666526+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry.RunTeststatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RunTest</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:46:33.8087026+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry.GetFeatureInfostatic##string#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetFeatureInfo</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:46:25.8346624+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry.RunFormTeststatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RunFormTest</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.PolygonGpsConverterEntry</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:46:17.9552706+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\PolygonGpsConverterEntry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpBase.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.LoadFromFilestatic##Dictionary&lt;string, string&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadFromFile</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:25:05.3126483+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.TryLoadNewFormatCookiesstatic##bool#string, Dictionary&lt;string, string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TryLoadNewFormatCookies</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:24:59.854214+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Zn51Helper</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:00:24.3992234+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\51Helper\Dx51HelpBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm无线小工具.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm无线小工具.btn台账扇区汇总_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btn台账扇区汇总_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm无线小工具</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm无线小工具.cs</ProjectItemFileName><TimeStamp>2025-07-22T20:13:19.457668+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm无线小工具.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LoggingService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.LoggingService</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoggingService</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName><TimeStamp>2025-07-12T09:02:57.1209551+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.LoggingService.LogFatal#void#string, Exception</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogFatal</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Infrastructure.LoggingService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName><TimeStamp>2025-07-11T23:37:32.6762161+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\LoggingService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.StationConverterHelper.ExecuteAutoConversionstatic##bool#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteAutoConversion</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.StationConverterHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:03:59.4935181+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\StationConverterHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper.GetConfigDirectorystatic##string#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetConfigDirectory</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:03:45.0639584+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper.GetAvailableConfigFilesstatic##List&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetAvailableConfigFiles</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:03:38.2867214+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper.InitializeConfigurationAsyncstatic##Task&lt;bool&gt;#string, System.Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>InitializeConfigurationAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.StationDataProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:03:25.1551214+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\StationDataProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmWordHelper</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmWordHelper</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.cs</ProjectItemFileName><TimeStamp>2025-06-29T16:01:13.797789+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmWordHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frmGPS生成图层.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.frmGPS生成图层</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-25T23:25:15.210295+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frmGPS生成图层.Dispose#void#bool</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyExcelVsto.Module.WX.frmGPS生成图层</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-25T23:24:59.6387896+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frmGPS生成图层.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\FileUploadService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Services.FileUploadService.UploadFileAsync#Task&lt;FileUploadResult&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Services.FileUploadService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName><TimeStamp>2025-07-11T23:33:18.379385+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\FileUploadService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETRangeSelectControl.ActiveExcelApp#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ActiveExcelApp</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-16T23:42:21.6181889+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\HyFunctions.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.HyFunctions.Btn金额转大写static##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Btn金额转大写</ItemName><ItemPath>HyExcelVsto.Module.Common.HyFunctions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\HyFunctions.cs</ProjectItemFileName><TimeStamp>2025-07-30T11:52:07.5315941+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\HyFunctions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.AI.frmAIv2</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmAIv2</ItemName><ItemPath>HyExcelVsto.Module.AI</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:56:50.0524269+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.AI\frmAIv2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm合规检查.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm合规检查</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm合规检查</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm合规检查.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:45:40.8972995+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm合规检查.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Config.OrderKmlConfig.GetTowerSheetColumnTitlesstatic##string#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetTowerSheetColumnTitles</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Config.OrderKmlConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:38:45.9770184+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Config\OrderKmlConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces.IAuthenticationService.LoginAsync#Task&lt;AuthenticationResult&gt;#System.Windows.Forms.Form</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoginAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Interfaces.IAuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:13:40.7765233+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Interfaces\IAuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessorForm</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-19T19:36:37.0691203+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\UI\StationDataProcessorForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmVisioHelper</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmVisioHelper</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:34.8584869+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmVisioHelper.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.Common.frmVisioHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:03.3595394+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\BatchOperationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Services.BatchOperationService.ExecuteBatchAsync#Task&lt;BatchOperationResult&lt;TOutput&gt;&gt;#IEnumerable&lt;TInput&gt;, Func&lt;TInput, Task&lt;TOutput&gt;&gt;, BatchExecutionOptions, IProgress&lt;BatchProgress&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteBatchAsync</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Services.BatchOperationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T10:36:00.6041747+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Services\BatchOperationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions.SetAutoFilter</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SetAutoFilter</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:16:36.8627481+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions.SetAutoFilter.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProcessingOptions.SetAutoFilter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:16:36.83599+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\Interfaces\IDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor.ExtractFromTowerSheet#List&lt;OrderStationData&gt;#Worksheet, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExtractFromTowerSheet</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName><TimeStamp>2025-07-23T20:57:44.5819393+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor.ExtractDataFromWorksheet#List&lt;OrderStationData&gt;#Worksheet, Dictionary&lt;string, int&gt;, string, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExtractDataFromWorksheet</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName><TimeStamp>2025-07-23T15:24:26.7377934+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor.FindColumnPositions#Dictionary&lt;string, int&gt;#Worksheet, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FindColumnPositions</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:37:43.9382854+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor.ExtractFromMicroSheet#List&lt;OrderStationData&gt;#Worksheet, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExtractFromMicroSheet</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:36:48.0630271+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor.ExtractSingleRowData#OrderStationData#Worksheet, Dictionary&lt;string, int&gt;, string, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExtractSingleRowData</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderDataExtractor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:53:52.4769227+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Example\KmlConverterExample.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.Example.KmlConverterExample.ShowConversionEffectstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowConversionEffect</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.Example.KmlConverterExample</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName><TimeStamp>2025-07-22T16:33:14.8580661+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Example\KmlConverterExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\frmKmlConverter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.frmKmlConverter.btnBatchConvert_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnBatchConvert_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.frmKmlConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:23:54.6537942+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.frmKmlConverter.btnConvert_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnConvert_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.frmKmlConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:18:36.6766016+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\frmKmlConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm文件操作</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm文件操作</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:15.4726769+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.MenuManager.RebuildAllMenus#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RebuildAllMenus</ItemName><ItemPath>HyExcelVsto.Extensions.MenuManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T11:54:25.5991201+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm_Entry.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorForm.ReleaseCustomResources#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ReleaseCustomResources</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm_Entry.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:34:32.2882927+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationDataProcessorForm.StationDataProcessorForm##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>StationDataProcessorForm</ItemName><ItemPath>HyExcelVsto.Module.WX.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm_Entry.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:05:49.7640741+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessorForm_Entry.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.BtnExecuteConversion_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnExecuteConversion_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:03:44.272718+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm.BtnCreateWorkbook_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnCreateWorkbook_Click</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.UI.StationDataProcessorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName><TimeStamp>2025-07-21T22:55:04.1301944+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationDataProcessor\UI\StationDataProcessorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmVisioHelper.frmHelper_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmHelper_Load</ItemName><ItemPath>HyExcelVsto.Module.Common.frmVisioHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:28:35.7562259+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmVisioHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer.DisableOptimization#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DisableOptimization</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:27:29.7557786+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer.GetMemoryUsage#string#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetMemoryUsage</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:27:21.4841176+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer.ExecuteWithOptimization#T#Func&lt;T&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteWithOptimization</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.PerformanceOptimizer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName><TimeStamp>2025-07-19T20:27:17.2392071+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\PerformanceOptimizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.下载交付列表并更新#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>下载交付列表并更新</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName><TimeStamp>2025-07-11T08:59:58.7407919+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyLicenseManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.HyLicenseManager.RefreshAuthorizationAsyncInternalstatic##Task#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RefreshAuthorizationAsyncInternal</ItemName><ItemPath>HyExcelVsto.HyLicenseManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyLicenseManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T11:53:44.8166204+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyLicenseManager</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HyLicenseManager</ItemName><ItemPath>HyExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyLicenseManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T11:53:41.0184253+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Models\ApiResponse.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51HelperV2.Models.ApiResponse.IsSuccess</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsSuccess</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51HelperV2.Models.ApiResponse</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName><TimeStamp>2025-07-11T22:11:49.5996306+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Models\ApiResponse.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderKmlGenerator.GenerateKmlFile#bool#List&lt;OrderKmlPoint&gt;, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateKmlFile</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.Core.OrderKmlGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:30:40.4391189+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\Core\OrderKmlGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService.GetUserInfoAsync#Task&lt;JsonObject&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetUserInfoAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:39:18.2953721+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService.ValidateLoginAsync#Task&lt;AuthenticationResult&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ValidateLoginAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:39:15.1060518+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService.LoginAsync#Task&lt;AuthenticationResult&gt;#Form</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoginAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:38:54.2788039+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService.GetLoginHeadersAsync#Task&lt;string&gt;#Form</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetLoginHeadersAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:38:53.3433924+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService.CheckWebView2Runtime#bool#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CheckWebView2Runtime</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.AuthenticationService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:14:29.2077137+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\AuthenticationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter.ProcessLogicalStations#List&lt;PhysicalStation&gt;#List&lt;LogicalStation&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessLogicalStations</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:23:46.430292+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter.ConvertStationData#void#Worksheet, Worksheet, Range, Range</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertStationData</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:11:11.5660768+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataConverter</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:08:40.7664585+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter.GenerateOutputData#object[,]#List&lt;PhysicalStation&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateOutputData</ItemName><ItemPath>HyExcelVsto.Module.WX.StationConverter.Core.StationDataConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName><TimeStamp>2025-07-21T23:08:03.2896807+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\StationConverter\Core\StationDataConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.DownloadTaskManagementFilestatic##Task&lt;(bool Success, string Result)&gt;#string, DateTime?, DateTime?</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DownloadTaskManagementFile</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName><TimeStamp>2025-07-10T23:25:56.5915698+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm51Helper\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\FileInfo.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo.FileInfo##JsonObject</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FileInfo</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:36:28.6263933+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo.UploadTime.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo.UploadTime</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:58.3754105+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo.UploadTime</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadTime</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.FileInfo</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:54.6374506+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\FileInfo.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess.AddCalculatedColumnsAsync#Task&lt;int&gt;#Dictionary&lt;string, Func&lt;object[], object&gt;&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AddCalculatedColumnsAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:42:23.0003512+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess.SetAutoFilterAsync#Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SetAutoFilterAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:21:44.0730335+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess.GetFilterRange#Range#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetFilterRange</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ExcelDataAccess</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:17:19.6476723+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ExcelDataAccess.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext.FindDynamicField#JsonObject#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FindDynamicField</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:36:22.6062915+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext.TaskContext##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TaskContext</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:38.0958527+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext.CreateTime.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext.CreateTime</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:24.0598066+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext.CreateTime</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateTime</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Models.TaskContext</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName><TimeStamp>2025-07-12T12:35:24.0598066+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Models\TaskContext.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.设置存储目录#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>设置存储目录</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:10:50.7728008+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.下载交付列表并更新#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>下载交付列表并更新</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName><TimeStamp>2025-07-30T15:49:42.102146+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.上传设计交底#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>上传设计交底</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:12:34.0948788+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.AddStatisticsColumnsWithRowContext#Task&lt;int&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AddStatisticsColumnsWithRowContext</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:38:21.5058416+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.SetColumnFormatsToText#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetColumnFormatsToText</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:38:08.8414483+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.CalculateLogicalStationFromCache#object#object[]</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CalculateLogicalStationFromCache</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:12.1091955+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.CalculateStationFrequencyIndex#object#object[]</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CalculateStationFrequencyIndex</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T15:34:11.487869+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.ProcessStationDataAsync#Task&lt;ProcessResult&gt;#Worksheet, ProcessingOptions</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessStationDataAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:52:38.8160481+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor.ValidateDataAsync#Task&lt;bool&gt;#Worksheet, ProcessingOptions</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidateDataAsync</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:52:33.6198305+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmExcelFileManager.RecordCurrentFiles#int#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RecordCurrentFiles</ItemName><ItemPath>HyExcelVsto.Module.Common.frmExcelFileManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:14:46.3399868+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frmExcelFileManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmExcelFileManager</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.cs</ProjectItemFileName><TimeStamp>2025-07-29T14:29:39.6912817+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmExcelFileManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient.DownloadFileAsync#Task&lt;bool&gt;#string, string, string, string, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DownloadFileAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:41:04.2958081+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient.UploadFileJsonObjectAsync#Task&lt;JsonObject&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileJsonObjectAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:41:03.59711+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient.UploadFileAsync#Task&lt;string&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:41:02.9411072+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient.PostJsonObjectAsync#Task&lt;JsonObject&gt;#string, string, string, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>PostJsonObjectAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:41:02.04042+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient.PostAsync#Task&lt;string&gt;#string, string, string, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>PostAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.ApiClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:39:34.2167377+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\ApiClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\Constants.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Infrastructure.Constants</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Constants</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Infrastructure</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName><TimeStamp>2025-07-12T23:41:11.0687116+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Infrastructure\Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Ribbon.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Ribbon.GetCustomUI#string#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetCustomUI</ItemName><ItemPath>HyExcelVsto.Ribbon</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Ribbon.cs</ProjectItemFileName><TimeStamp>2025-07-07T09:55:15.7072757+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Ribbon.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.LoadAllConfigurationsstatic##void#ETSectionConfigReader</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadAllConfigurations</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:36:23.6044835+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.COLUMNS_TO_SHOW</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>COLUMNS_TO_SHOW</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:34:17.4726988+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.SOURCE_COLUMNS</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SOURCE_COLUMNS</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:34:06.3553394+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.HEADER_COLUMNS</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HEADER_COLUMNS</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:33:53.8801288+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.SUFFIX_PATTERNS</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SUFFIX_PATTERNS</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T13:33:32.0046137+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.SUFFIX_PATTERNS.get</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>get</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.SUFFIX_PATTERNS</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T12:00:19.3555474+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.IsInitialized</ID><ImageSource>img\tvi\x_property-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsInitialized</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T11:43:45.1276971+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.LoadFrequencyMapstatic##void#ETSectionConfigReader</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadFrequencyMap</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T09:55:38.5254971+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationDataProcessorConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T09:47:47.8811397+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig.LoadSourceColumnsstatic##void#ETSectionConfigReader</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadSourceColumns</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.StationDataProcessorConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName><TimeStamp>2025-07-20T09:42:11.3667855+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\StationDataProcessorConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterIntegration.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.KmlConverterIntegration.ConvertSpecificKmlstatic##bool#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertSpecificKml</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.KmlConverterIntegration</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName><TimeStamp>2025-07-22T16:32:37.8988918+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\KmlConverterIntegration.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper.SummarizeDatastatic##SummaryResult#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SummarizeData</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T22:21:09.0950051+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper.SummarizeToAuditAccountstatic##SummaryResult#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SummarizeToAuditAccount</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T22:20:56.7653157+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper.FindColumnByNamestatic##int#Worksheet, string, int</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FindColumnByName</ItemName><ItemPath>HyExcelVsto.Module.WX.TowerAccountProcessor.TowerAccountProcessorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-22T22:05:44.4235838+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\TowerAccountProcessor_JY\TowerAccountProcessorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm.InitializeAngleExtractor#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeAngleExtractor</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T12:05:27.7598984+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm.AngleExtractorForm##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AngleExtractorForm</ItemName><ItemPath>HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorForm.cs</ProjectItemFileName><TimeStamp>2025-07-31T11:56:47.6229595+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\AngleExtractor\AngleExtractorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.button设置存储目录_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button设置存储目录_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-30T17:40:40.4631724+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.button下载交付列表并更新_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button下载交付列表并更新_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-30T14:43:17.287639+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm51Helper.button上传设计交底_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button上传设计交底_Click</ItemName><ItemPath>HyExcelVsto.Module.WX.frm51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:12:30.7491271+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\frm51Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm查找站点.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.frm查找站点</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-23T00:08:52.6616622+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.frm查找站点</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frm查找站点</ItemName><ItemPath>HyExcelVsto.Module.WX</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:57:34.8806+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\frm查找站点.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:39:43.5685888+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmPolygonGpsConverter</ItemName><ItemPath>HyExcelVsto.Module.WX.PolygonGpsConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-26T00:02:33.1062381+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\PolygonGpsConverter\frmPolygonGpsConverter.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidationRulesConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName><TimeStamp>2025-07-19T21:11:14.36587+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig.FlashDelay</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FlashDelay</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName><TimeStamp>2025-07-19T21:11:12.8508761+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig.RequiredColumn2</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RequiredColumn2</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName><TimeStamp>2025-07-19T21:11:03.8209597+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig.RequiredColumn1</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RequiredColumn1</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName><TimeStamp>2025-07-19T21:11:02.7194668+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig.MinDataRows</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>MinDataRows</ItemName><ItemPath>HyExcelVsto.Module.Common.StationDataProcessor.Core.ValidationRulesConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName><TimeStamp>2025-07-19T21:10:47.6309311+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\StationDataProcessor\Core\ConfigStructures.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.frm文件操作.LoadPresetDirectories#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>LoadPresetDirectories</ItemName><ItemPath>HyExcelVsto.Module.Common.frm文件操作</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:01:35.3589272+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frm文件操作.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageIdExportHelper.GetOfficeImageIdsFromConfigstatic##List&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetOfficeImageIdsFromConfig</ItemName><ItemPath>HyExcelVsto.Module.Common.OfficeImageIdExportHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-30T07:52:20.1491548+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageIdExportHelper.TryExtractIconWithGetImageMsostatic##bool#Worksheet, int, int, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TryExtractIconWithGetImageMso</ItemName><ItemPath>HyExcelVsto.Module.Common.OfficeImageIdExportHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-30T00:09:35.8202842+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageHelper.ConvertPixelByPixelstatic##Bitmap#stdole.IPictureDisp</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertPixelByPixel</ItemName><ItemPath>HyExcelVsto.Module.Common.OfficeImageHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-29T21:11:43.0753321+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageIdExportHelper</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>OfficeImageIdExportHelper</ItemName><ItemPath>HyExcelVsto.Module.Common</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-29T20:29:47.792548+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageIdExportHelper.GetIconSymbolstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetIconSymbol</ItemName><ItemPath>HyExcelVsto.Module.Common.OfficeImageIdExportHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-29T16:31:00.8514067+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.Common.OfficeImageIdExportHelper.TryAddIconFromCommandBarstatic##bool#Worksheet, int, int, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TryAddIconFromCommandBar</ItemName><ItemPath>HyExcelVsto.Module.Common.OfficeImageIdExportHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName><TimeStamp>2025-07-29T16:04:56.288054+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\OfficeImageIdExportHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.Helper51V2.Core.Services.TaskManagementService.GenerateDefaultConnectionTableAsync#Task&lt;OperationResult&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateDefaultConnectionTableAsync</ItemName><ItemPath>HyExcelVsto.Module.WX.Helper51V2.Core.Services.TaskManagementService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:03:10.3588573+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51HelperV2\Core\Services\TaskManagementService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpTask2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper.DownloadDeliveryListFilestatic##Task&lt;(bool success, string message)&gt;#DateTime?, DateTime?</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DownloadDeliveryListFile</ItemName><ItemPath>HyExcelVsto.Extensions.Dx51Helper.Zn51Helper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpTask2.cs</ProjectItemFileName><TimeStamp>2025-07-30T14:43:56.147238+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\51Helper\Dx51HelpTask2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Program.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.Program.ShowHelpstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowHelp</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.Program</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Program.cs</ProjectItemFileName><TimeStamp>2025-07-22T15:36:21.219686+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetPermissionUIActions#Dictionary&lt;string, List&lt;Action&lt;bool&gt;&gt;&gt;#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetPermissionUIActions</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:01:04.3660606+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.SetDefaultUIVisibility#void#bool</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SetDefaultUIVisibility</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:51:56.1832769+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.RegisterPermissionUIMappings#void#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RegisterPermissionUIMappings</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:51:52.6714783+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyControlMappingManager.GenerateControlTitleMappingDynamically#Dictionary&lt;string, string&gt;#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateControlTitleMappingDynamically</ItemName><ItemPath>HyExcelVsto.HyControlMappingManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:32:38.1083343+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyControlMappingManager.GenerateControlPermissionMappingDynamically#Dictionary&lt;string, string&gt;#Dictionary&lt;string, List&lt;string&gt;&gt;</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateControlPermissionMappingDynamically</ItemName><ItemPath>HyExcelVsto.HyControlMappingManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:32:11.0727673+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.RegisterControlPermissionMappings#void#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RegisterControlPermissionMappings</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:26:48.2518877+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyControlMappingManager</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HyControlMappingManager</ItemName><ItemPath>HyExcelVsto</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:59.7478517+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetControlPermissionKey#string#string</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetControlPermissionKey</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:54.9637952+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetControlNormalTitleFallback#string#string</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetControlNormalTitleFallback</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:53.2074415+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.OnRefreshRibbonControlTitles#void#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>OnRefreshRibbonControlTitles</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:48.3697939+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetGlobalControlPermissionMapping#Dictionary&lt;string, string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetGlobalControlPermissionMapping</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:32.6334904+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.RegisterSpecialPermissionControls#void#Dictionary&lt;string, (string PermissionKey, string NormalTitle)&gt;, Dictionary&lt;string, string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>RegisterSpecialPermissionControls</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:26.845472+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetGlobalControlTitleMapping#Dictionary&lt;string, string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetGlobalControlTitleMapping</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:25.3042078+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.ForceInitializeGlobalMappings#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ForceInitializeGlobalMappings</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:24.6377667+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.ForceRefreshPermissionsAndUI#void#</ID><ImageSource>img\tvi\x_method_override-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ForceRefreshPermissionsAndUI</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:20.5524432+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.Initialize#void#</ID><ImageSource>img\tvi\x_method_override-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Initialize</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:19.590551+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.LogPermissionStatus#void#string, bool, DateTime?</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogPermissionStatus</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:18.4665598+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.CreateControlPermissionManager#ETControlPermissionManager#</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateControlPermissionManager</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:14.1727915+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.HyUIPermissionManager.GetPermissionDisplayName#string#string</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetPermissionDisplayName</ItemName><ItemPath>HyExcelVsto.HyUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T21:08:08.840491+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.OrderKmlGenerator.UI.OrderKmlGeneratorForm.BindConfigurationSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BindConfigurationSettings</ItemName><ItemPath>HyExcelVsto.Module.WX.OrderKmlGenerator.UI.OrderKmlGeneratorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:31:58.4447065+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\OrderKmlGenerator\UI\OrderKmlGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\TestConverter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.TestConverter.TestConversionstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TestConversion</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.TestConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T17:13:17.2816751+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyExcelVsto.Module.WX.KmlConverter.TestConverter.ShowExamplestatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowExample</ItemName><ItemPath>HyExcelVsto.Module.WX.KmlConverter.TestConverter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyExcelVsto.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName><TimeStamp>2025-07-22T16:33:06.8438553+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.WX\KmlConverter\TestConverter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>HyExcelVsto</ProjectName></ProjectData>