# VSTO签名问题一键修复脚本
# 自动检测并修复"清单 XML 签名无效"问题

param(
    [ValidateSet("DisableSign", "GenerateCert", "ConfigureTrust", "Auto")]
    [string]$Solution = "Auto",
    [string]$ProjectPath = "HyExcelVsto\HyExcelVsto.csproj",
    [switch]$Rebuild = $true,
    [switch]$Publish = $false
)

Write-Host "=== VSTO签名问题一键修复脚本 ===" -ForegroundColor Cyan
Write-Host "解决方案: $Solution" -ForegroundColor Yellow
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

# 检查项目文件是否存在
if (-not (Test-Path $ProjectPath)) {
    Write-Error "项目文件不存在: $ProjectPath"
    exit 1
}

# 备份原始项目文件
$backupPath = "$ProjectPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Copy-Item $ProjectPath $backupPath
Write-Host "已备份项目文件: $backupPath" -ForegroundColor Green

try {
    switch ($Solution) {
        "DisableSign" {
            Write-Host "`n=== 方案1：禁用签名 ===" -ForegroundColor Green
            
            # 读取项目文件
            $content = Get-Content $ProjectPath -Raw
            
            # 禁用签名
            $content = $content -replace '<SignManifests>true</SignManifests>', '<SignManifests>false</SignManifests>'
            
            # 注释掉证书配置
            $content = $content -replace '(<PropertyGroup>\s*<ManifestKeyFile>.*?</ManifestKeyFile>\s*</PropertyGroup>)', '<!--$1-->'
            $content = $content -replace '(<PropertyGroup>\s*<ManifestCertificateThumbprint>.*?</ManifestCertificateThumbprint>\s*</PropertyGroup>)', '<!--$1-->'
            
            # 保存修改
            $content | Set-Content $ProjectPath -Encoding UTF8
            Write-Host "已禁用项目签名" -ForegroundColor Green
        }
        
        "GenerateCert" {
            Write-Host "`n=== 方案2：生成兼容证书 ===" -ForegroundColor Green
            
            # 调用证书生成脚本
            & "$PSScriptRoot\Generate-CompatibleCertificate.ps1" -CertificateName "HyExcelVsto" -OutputPath "HyExcelVsto"
            
            Write-Host "证书生成完成，请手动更新项目配置" -ForegroundColor Yellow
        }
        
        "ConfigureTrust" {
            Write-Host "`n=== 方案3：配置信任设置 ===" -ForegroundColor Green
            
            # 调用信任配置脚本
            & "$PSScriptRoot\Configure-VSTOTrust.ps1" -EnableFullTrust -DisableSignatureCheck
            
            Write-Host "信任设置配置完成" -ForegroundColor Green
        }
        
        "Auto" {
            Write-Host "`n=== 自动检测最佳方案 ===" -ForegroundColor Green
            
            # 检查当前签名状态
            $content = Get-Content $ProjectPath -Raw
            $isSigningEnabled = $content -match '<SignManifests>true</SignManifests>'
            
            if ($isSigningEnabled) {
                Write-Host "检测到启用了签名，采用禁用签名方案" -ForegroundColor Yellow
                
                # 禁用签名
                $content = $content -replace '<SignManifests>true</SignManifests>', '<SignManifests>false</SignManifests>'
                $content | Set-Content $ProjectPath -Encoding UTF8
                Write-Host "已自动禁用签名" -ForegroundColor Green
            }
            else {
                Write-Host "签名已禁用，检查其他配置..." -ForegroundColor Yellow
            }
        }
    }
    
    # 重新编译项目
    if ($Rebuild) {
        Write-Host "`n=== 重新编译项目 ===" -ForegroundColor Green
        
        # 查找MSBuild
        $msbuildPaths = @(
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
        )
        
        $msbuild = $msbuildPaths | Where-Object { Test-Path $_ } | Select-Object -First 1
        
        if ($msbuild) {
            Write-Host "使用MSBuild: $msbuild" -ForegroundColor Yellow
            
            # 清理项目
            & $msbuild $ProjectPath /t:Clean /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
            
            # 重新编译
            & $msbuild $ProjectPath /t:Build /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "项目编译成功" -ForegroundColor Green
            }
            else {
                Write-Warning "项目编译失败，请检查错误信息"
            }
        }
        else {
            Write-Warning "未找到MSBuild，请手动编译项目"
        }
    }
    
    # 发布项目
    if ($Publish) {
        Write-Host "`n=== 发布项目 ===" -ForegroundColor Green
        
        if ($msbuild) {
            & $msbuild $ProjectPath /t:Publish /p:Configuration=Release /p:Platform="Any CPU" /v:minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "项目发布成功" -ForegroundColor Green
            }
            else {
                Write-Warning "项目发布失败，请检查错误信息"
            }
        }
    }
    
    Write-Host "`n=== 修复完成 ===" -ForegroundColor Cyan
    Write-Host "1. 项目配置已更新" -ForegroundColor White
    Write-Host "2. 建议测试VSTO安装程序" -ForegroundColor White
    Write-Host "3. 如有问题，可恢复备份文件: $backupPath" -ForegroundColor White
    
}
catch {
    Write-Error "修复过程中发生错误: $($_.Exception.Message)"
    
    # 恢复备份
    Write-Host "正在恢复备份文件..." -ForegroundColor Yellow
    Copy-Item $backupPath $ProjectPath -Force
    Write-Host "已恢复原始项目文件" -ForegroundColor Green
}

Write-Host "`n=== 使用建议 ===" -ForegroundColor Cyan
Write-Host "快速修复: .\Fix-VSTOSignature.ps1 -Solution DisableSign -Rebuild" -ForegroundColor Gray
Write-Host "完整发布: .\Fix-VSTOSignature.ps1 -Solution Auto -Rebuild -Publish" -ForegroundColor Gray
