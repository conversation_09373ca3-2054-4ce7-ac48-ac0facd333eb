# 正则表达式预置配置文件
# 配置格式说明：
# [配置节名称] - 每个配置节代表一个正则表达式规则
# name=规则显示名称
# pattern=正则表达式模式
# group=返回第几组（默认为0，表示整个匹配）
# description=规则描述（可选）

[GetStationName]
name=获取基站名
pattern=[\\u4E00-\\u9FFF]+
group=0
description=提取中文字符，用于获取基站名称

[ExtractBeforeDashUnderscorePipe]
name=提取 - | _ 前内容
pattern=[^-_\\|]*
group=0
description=提取减号、下划线、竖线前的内容

[ExtractBeforeDot]
name=提取 . 前内容
pattern=^(.*?)(?=\\.|$)
group=1
description=提取点号前的内容，使用捕获组

[ExtractBefore800]
name=提取 800 前内容
pattern=^(.+?)-800.*$
group=1
description=提取800前的内容，使用捕获组

[ExtractNumbers]
name=提取数字
pattern=\\d+
group=0
description=提取所有数字

[ExtractEmail]
name=提取邮箱地址
pattern=[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}
group=0
description=提取邮箱地址

[ExtractPhoneNumber]
name=提取手机号码
pattern=1[3-9]\\d{9}
group=0
description=提取中国大陆手机号码

[ExtractIPAddress]
name=提取IP地址
pattern=\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b
group=0
description=提取IPv4地址

[ExtractDate]
name=提取日期(YYYY-MM-DD)
pattern=\\d{4}-\\d{2}-\\d{2}
group=0
description=提取YYYY-MM-DD格式的日期

[ExtractTime]
name=提取时间(HH:MM:SS)
pattern=\\d{2}:\\d{2}:\\d{2}
group=0
description=提取HH:MM:SS格式的时间

[ExtractBracketContent]
name=提取括号内容
pattern=\\(([^)]+)\\)
group=1
description=提取圆括号内的内容

[ExtractSquareBracketContent]
name=提取方括号内容
pattern=\\[([^\\]]+)\\]
group=1
description=提取方括号内的内容

