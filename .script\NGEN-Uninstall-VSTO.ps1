# NGEN Uninstall Script for VSTO Plugin and Dependencies
# Must run as Administrator

param(
    [string]$BuildConfiguration = "Debug",
    [string]$Platform = "AnyCPU"
)

Write-Host "=== NGEN Uninstall Script for HyExcelVsto ===" -ForegroundColor Cyan
Write-Host "Configuration: $BuildConfiguration" -ForegroundColor Yellow
Write-Host "Platform: $Platform" -ForegroundColor Yellow

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Define paths
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$binPath = Join-Path $projectRoot "HyExcelVsto\bin\$BuildConfiguration"
$ngenPath = "${env:WINDIR}\Microsoft.NET\Framework64\v4.0.30319\ngen.exe"
$ngen32Path = "${env:WINDIR}\Microsoft.NET\Framework\v4.0.30319\ngen.exe"

Write-Host "`nBin Path: $binPath" -ForegroundColor Green

# Function to uninstall NGEN for an assembly
function Uninstall-NgenAssembly {
    param(
        [string]$AssemblyPath,
        [string]$AssemblyName
    )
    
    Write-Host "`nUninstalling: $AssemblyName" -ForegroundColor Yellow
    
    try {
        # Uninstall 64-bit
        Write-Host "  Uninstalling 64-bit native image..." -ForegroundColor Gray
        & $ngenPath uninstall "$AssemblyPath" /verbose
        
        # Uninstall 32-bit
        Write-Host "  Uninstalling 32-bit native image..." -ForegroundColor Gray
        & $ngen32Path uninstall "$AssemblyPath" /verbose
        
        Write-Host "  SUCCESS: $AssemblyName uninstalled" -ForegroundColor Green
    }
    catch {
        Write-Host "  WARNING: Failed to uninstall $AssemblyName - $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# All assemblies to uninstall (in reverse order of installation)
$allAssemblies = @(
    # Other dependencies
    "System.Security.Principal.Windows.dll",
    "System.Security.AccessControl.dll",
    "Microsoft.Win32.Registry.dll",
    "System.Numerics.Vectors.dll",
    "System.IO.Pipelines.dll",
    "System.Threading.Channels.dll",
    "System.Text.Encodings.Web.dll",
    "System.Text.Encoding.CodePages.dll",
    "System.Drawing.Common.dll",
    "System.Diagnostics.DiagnosticSource.dll",
    "System.Collections.Immutable.dll",
    "SharpCompress.dll",
    "Castle.Core.dll",
    "AngleSharp.dll",
    
    # Microsoft Extensions
    "Microsoft.Bcl.AsyncInterfaces.dll",
    "Microsoft.Extensions.Configuration.Abstractions.dll",
    "Microsoft.Extensions.Configuration.dll",
    "Microsoft.Extensions.Logging.Abstractions.dll",
    "Microsoft.Extensions.Logging.dll",
    "Microsoft.Extensions.DependencyInjection.Abstractions.dll",
    "Microsoft.Extensions.DependencyInjection.dll",
    
    # ServiceStack
    "ServiceStack.Text.dll",
    "ServiceStack.Interfaces.dll",
    "ServiceStack.Common.dll",
    "ServiceStack.Client.dll",
    "ServiceStack.dll",
    
    # High-priority dependencies
    "System.Threading.Tasks.Extensions.dll",
    "System.Runtime.CompilerServices.Unsafe.dll",
    "System.Buffers.dll",
    "System.Memory.dll",
    "System.Text.Json.dll",
    "Newtonsoft.Json.dll",
    
    # Microsoft Office Tools
    "Microsoft.VisualStudio.Tools.Applications.Runtime.dll",
    "Microsoft.Office.Tools.Common.v4.0.Utilities.dll",
    "Microsoft.Office.Tools.v4.0.Framework.dll",
    "Microsoft.Office.Tools.Excel.dll",
    "Microsoft.Office.Tools.Common.dll",
    "Microsoft.Office.Tools.dll",
    
    # Custom libraries
    "ExtensionsTools.dll",
    
    # Main VSTO Assembly (last)
    "HyExcelVsto.dll"
)

Write-Host "`n=== Starting NGEN Uninstallation ===" -ForegroundColor Cyan

foreach ($assembly in $allAssemblies) {
    $assemblyPath = Join-Path $binPath $assembly
    if (Test-Path $assemblyPath) {
        Uninstall-NgenAssembly -AssemblyPath $assemblyPath -AssemblyName $assembly
    } else {
        Write-Host "`nSKIP: $assembly (file not found)" -ForegroundColor Gray
    }
}

# Additional cleanup - remove any orphaned native images
Write-Host "`n=== Additional Cleanup ===" -ForegroundColor Cyan
Write-Host "Performing NGEN update to clean up orphaned images..." -ForegroundColor Yellow

try {
    & $ngenPath update /verbose
    & $ngen32Path update /verbose
    Write-Host "NGEN update completed successfully" -ForegroundColor Green
}
catch {
    Write-Host "WARNING: NGEN update failed - $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n=== NGEN Uninstallation Completed ===" -ForegroundColor Green
Write-Host "All native images have been removed." -ForegroundColor White
Write-Host "`nRecommendations:" -ForegroundColor Cyan
Write-Host "1. Restart Excel to ensure changes take effect" -ForegroundColor White
Write-Host "2. You can reinstall native images anytime using 'NGEN-Install-VSTO.ps1'" -ForegroundColor White

Read-Host "`nPress any key to exit"
