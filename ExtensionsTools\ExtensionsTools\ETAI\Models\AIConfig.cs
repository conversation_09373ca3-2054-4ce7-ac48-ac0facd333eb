using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using OpenAI.Chat;
using Newtonsoft.Json;

namespace ET.AI.Models
{
    /// <summary>
    /// AI配置信息类，用于存储和管理AI模型的配置参数
    /// </summary>
    /// <remarks>
    /// 该类包含所有AI服务所需的配置信息，包括：
    /// <list type="bullet">
    /// <item><description>模型参数（模型名称、温度等）</description></item>
    /// <item><description>API设置（密钥、基础URL等）</description></item>
    /// <item><description>请求限制（并发数、频率等）</description></item>
    /// <item><description>网络设置（代理、超时等）</description></item>
    /// </list>
    /// </remarks>
    public class AIConfig
    {
        /// <summary>
        /// 获取或设置模型名称
        /// </summary>
        /// <remarks>
        /// 指定要使用的AI模型的标识符，例如：
        /// <list type="bullet">
        /// <item><description>"gpt-4" - GPT-4模型</description></item>
        /// <item><description>"gpt-3.5-turbo" - GPT-3.5 Turbo模型</description></item>
        /// <item><description>"glm-4" - 智谱GLM-4模型</description></item>
        /// </list>
        /// </remarks>
        public string Model { get; set; }

        /// <summary>
        /// 获取或设置温度参数
        /// </summary>
        /// <remarks>
        /// 控制输出的随机性，取值范围0-2：
        /// <list type="bullet">
        /// <item><description>0.0：输出最确定性的答案</description></item>
        /// <item><description>0.2-0.5：保持相对确定性的同时允许一些变化</description></item>
        /// <item><description>0.8-1.0：产生更多样化和创造性的输出</description></item>
        /// <item><description>1.0以上：产生更随机的输出</description></item>
        /// </list>
        /// </remarks>
        public float? Temperature { get; set; }

        /// <summary>
        /// 获取或设置Top P参数
        /// </summary>
        /// <remarks>
        /// 控制输出的多样性，取值范围0-1：
        /// <list type="bullet">
        /// <item><description>0.1：输出非常保守，只考虑最可能的选项</description></item>
        /// <item><description>0.5：平衡保守性和多样性</description></item>
        /// <item><description>0.9：允许更多样化的输出</description></item>
        /// </list>
        /// 通常与Temperature参数配合使用，建议不要同时设置高值。
        /// </remarks>
        public float? TopP { get; set; }

        /// <summary>
        /// 获取或设置响应格式
        /// </summary>
        /// <remarks>
        /// 指定AI模型响应的格式：
        /// <list type="bullet">
        /// <item><description>使用 ChatResponseFormat.CreateJsonObjectFormat() 获取JSON格式响应</description></item>
        /// <item><description>使用 ChatResponseFormat.CreateTextFormat() 获取文本格式响应</description></item>
        /// </list>
        /// </remarks>
        public ChatResponseFormat ResponseFormat { get; set; }



        /// <summary>
        /// 获取或设置系统角色内容
        /// </summary>
        /// <remarks>
        /// 定义AI助手的系统级指令或角色设定，用于：
        /// <list type="bullet">
        /// <item><description>设置AI的行为规范</description></item>
        /// <item><description>定义回答的格式和风格</description></item>
        /// <item><description>提供领域特定的知识或限制</description></item>
        /// </list>
        /// </remarks>
        public string SystemContent { get; set; }

        /// <summary>
        /// 获取或设置最大并发请求数
        /// </summary>
        /// <remarks>
        /// 限制同时进行的API请求数量：
        /// <list type="bullet">
        /// <item><description>null：使用默认值（10）</description></item>
        /// <item><description>建议值：5-20，具体取决于API限制</description></item>
        /// </list>
        /// </remarks>
        public int? MaxConcurrentRequests { get; set; }

        /// <summary>
        /// 获取或设置每分钟最大请求数
        /// </summary>
        /// <remarks>
        /// 限制每分钟可以发送的API请求数量：
        /// <list type="bullet">
        /// <item><description>null：使用默认值（15）</description></item>
        /// <item><description>建议根据API提供商的限制设置</description></item>
        /// </list>
        /// </remarks>
        public int? MaxRequestsPerMinute { get; set; }

        /// <summary>
        /// 获取或设置总最大请求数
        /// </summary>
        /// <remarks>
        /// 限制整个会话的最大请求总数：
        /// <list type="bullet">
        /// <item><description>null：不限制总请求数</description></item>
        /// <item><description>用于控制API使用成本</description></item>
        /// </list>
        /// </remarks>
        public int? TotalMaxRequests { get; set; }

        /// <summary>
        /// 获取或设置基础URL
        /// </summary>
        /// <remarks>
        /// AI服务的基础URL地址：
        /// <list type="bullet">
        /// <item><description>可以是官方API端点</description></item>
        /// <item><description>也可以是自定义的代理服务器</description></item>
        /// </list>
        /// </remarks>
        public string BaseURL { get; set; }

        /// <summary>
        /// 获取或设置API密钥列表
        /// </summary>
        /// <remarks>
        /// 支持多个API密钥，系统会自动：
        /// <list type="bullet">
        /// <item><description>在多个密钥间轮换</description></item>
        /// <item><description>处理速率限制</description></item>
        /// <item><description>平衡负载</description></item>
        /// </list>
        /// </remarks>
        public List<string> APIKeys { get; set; } = new List<string>();

        /// <summary>
        /// 获取或设置代理服务器地址
        /// </summary>
        /// <remarks>
        /// 代理服务器的主机名或IP地址，例如：
        /// <list type="bullet">
        /// <item><description>"127.0.0.1" - 本地代理</description></item>
        /// <item><description>"proxy.example.com" - 远程代理</description></item>
        /// </list>
        /// </remarks>
        public string ProxyHost { get; set; }

        /// <summary>
        /// 获取或设置代理服务器端口
        /// </summary>
        /// <remarks>
        /// 代理服务器的端口号，常见值：
        /// <list type="bullet">
        /// <item><description>7890 - 常用代理端口</description></item>
        /// <item><description>1080 - SOCKS代理默认端口</description></item>
        /// </list>
        /// </remarks>
        public int? ProxyPort { get; set; }

        /// <summary>
        /// 获取或设置JSON解析失败时的最大重试次数
        /// </summary>
        /// <remarks>
        /// 当AI返回的JSON无效时的重试机制：
        /// <list type="bullet">
        /// <item><description>null：使用默认重试次数</description></item>
        /// <item><description>建议值：1-3次</description></item>
        /// </list>
        /// </remarks>
        public int? MaxJsonRetryCount { get; set; }

        /// <summary>
        /// 获取或设置请求超时时间（秒）
        /// </summary>
        /// <remarks>
        /// 控制单个AI请求的最大等待时间：
        /// <list type="bullet">
        /// <item><description>null：使用默认值（120秒）</description></item>
        /// <item><description>建议值：60-300秒，取决于模型和任务复杂度</description></item>
        /// </list>
        /// </remarks>
        public int? RequestTimeout { get; set; }

        /// <summary>
        /// 获取或设置基础分组大小
        /// </summary>
        /// <remarks>
        /// 控制查询分组时的基础组大小：
        /// <list type="bullet">
        /// <item><description>默认值：10</description></item>
        /// <item><description>系统会自动将最小组大小阈值设置为基础组大小的一半</description></item>
        /// </list>
        /// </remarks>
        public int BaseGroupSize { get; set; } = 10;

        /// <summary>
        /// 读取system_content.ai文件内容并追加到SystemContent
        /// </summary>
        /// <param name="modelFilePath">模型配置文件路径</param>
        /// <remarks>
        /// 该方法会：
        /// <list type="bullet">
        /// <item><description>检查并读取system_content.ai文件</description></item>
        /// <item><description>将文件内容追加到现有的SystemContent</description></item>
        /// <item><description>保持原有格式并添加适当的分隔</description></item>
        /// </list>
        /// 如果文件不存在或读取失败，将保持原有SystemContent不变。
        /// </remarks>
        public void AppendSystemContent(string modelFilePath)
        {
            if (string.IsNullOrEmpty(modelFilePath)) return;
            try
            {
                string systemContentPath = ETConfig.GetConfigPath("system_content.ai", "AiModel");
                if (File.Exists(systemContentPath))
                {
                    string additionalContent = File.ReadAllText(systemContentPath, Encoding.UTF8);
                    if (!string.IsNullOrEmpty(additionalContent))
                    {
                        if (!string.IsNullOrEmpty(SystemContent))
                        {
                            SystemContent = $"{SystemContent}{Environment.NewLine}{Environment.NewLine}{additionalContent}";
                        }
                        else
                        {
                            SystemContent = additionalContent;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"读取system_content.ai文件失败：{ex.Message}");
            }
        }


    }
}