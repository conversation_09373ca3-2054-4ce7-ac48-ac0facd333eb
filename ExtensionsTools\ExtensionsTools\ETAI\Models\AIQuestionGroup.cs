using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace ET.AI.Models
{
    /// <summary>
    /// AI问题组类，表示一组相关的问题及其上下文
    /// </summary>
    /// <remarks>
    /// 该类用于组织和管理一组相关的问题，包括：
    /// <list type="bullet">
    /// <item><description>问题组的索引位置</description></item>
    /// <item><description>问题的上下文信息</description></item>
    /// <item><description>问题列表</description></item>
    /// </list>
    /// 通常用于批量处理Excel中的问题。
    /// </remarks>
    public class AIQuestionGroup
    {
        /// <summary>
        /// 获取或设置列表索引
        /// </summary>
        /// <remarks>
        /// 表示问题组在Excel中的位置：
        /// <list type="bullet">
        /// <item><description>在问题行模式下，表示行号</description></item>
        /// <item><description>在问题列模式下，表示列号</description></item>
        /// </list>
        /// </remarks>
        public int ListIndex { get; set; }

        /// <summary>
        /// 获取或设置已知信息1
        /// </summary>
        /// <remarks>
        /// 存储问题的主要上下文信息：
        /// <list type="bullet">
        /// <item><description>通常来自Excel中的相关单元格</description></item>
        /// <item><description>用于为AI提供问题的背景信息</description></item>
        /// </list>
        /// </remarks>
        public string KnownInfo1 { get; set; }

        /// <summary>
        /// 获取或设置已知信息2
        /// </summary>
        /// <remarks>
        /// 存储问题的补充上下文信息：
        /// <list type="bullet">
        /// <item><description>可选的额外背景信息</description></item>
        /// <item><description>用于提供更详细的问题上下文</description></item>
        /// </list>
        /// </remarks>
        public string KnownInfo2 { get; set; }

        /// <summary>
        /// 获取或设置问题列表
        /// </summary>
        /// <remarks>
        /// 存储该组中的所有问题项：
        /// <list type="bullet">
        /// <item><description>每个问题项都是<see cref="AIQuestionItem"/>类型</description></item>
        /// <item><description>问题按照Excel中的顺序排列</description></item>
        /// </list>
        /// </remarks>
        public List<AIQuestionItem> Questions { get; set; } = new List<AIQuestionItem>();

        /// <summary>
        /// 将问题组转换为JObject格式
        /// </summary>
        /// <returns>包含问题组所有信息的JSON对象</returns>
        /// <remarks>
        /// 转换过程包括：
        /// <list type="bullet">
        /// <item><description>添加列表索引</description></item>
        /// <item><description>添加已知信息（单个或多个）</description></item>
        /// <item><description>添加问题列表</description></item>
        /// </list>
        /// 生成的JSON结构适用于AI服务的输入格式。
        /// </remarks>
        public JObject ToJObject()
        {
            JObject questionGroup = new JObject();
            questionGroup["listIndex"] = ListIndex;

            if (string.IsNullOrEmpty(KnownInfo2))
            {
                questionGroup["已知信息"] = KnownInfo1;
            }
            else
            {
                questionGroup["已知信息1"] = KnownInfo1;
                questionGroup["已知信息2"] = KnownInfo2;
            }

            JArray questions = new JArray();
            foreach (AIQuestionItem item in Questions)
            {
                JObject question = new JObject();
                question["questionIndex"] = item.QuestionIndex;
                question["question"] = item.Question;
                questions.Add(question);
            }
            questionGroup["问题"] = questions;

            return questionGroup;
        }
    }
}