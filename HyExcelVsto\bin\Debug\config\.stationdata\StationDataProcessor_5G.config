
# ========================================
# 5G基站数据处理器配置文件
# 用于配置5G基站数据的识别、处理和显示规则
# 版本: 2.0
# 最后更新: 2025-01-20
# ========================================

# 基站名称前缀匹配模式配置
# 用于识别符合规范的5G基站名称前缀
# 支持正则表达式格式
[PREFIX_PATTERNS]
^GO_JY[A-Z]{2}_                          # 标准5G基站前缀格式：GO_JY + 两个大写字母 + 下划线

# 基站名称后缀匹配模式配置
# 用于识别5G基站设备类型和编号的后缀模式
# 支持各种RRU、AAU设备类型的识别
[SUFFIX_PATTERNS]
_800RRU([1-9]|[1-4][0-9]|50)$           # 800MHz RRU设备，编号1-50
800RRU([1-9]|[1-4][0-9]|50)$            # 800MHz RRU设备（无前缀下划线）
_800FRRU([1-9]|[1-4][0-9]|50)$          # 800MHz FRRU设备，编号1-50
800FRRU([1-9]|[1-4][0-9]|50)$           # 800MHz FRRU设备（无前缀下划线）
_CAFRRU([1-9]|[1-4][0-9]|50)$           # CA FRRU设备，编号1-50
CAFRRU([1-9]|[1-4][0-9]|50)$			# CA FRRU设备，编号1-50
_LFRRU([1-9]|[1-4][0-9]|50)$            # L频段FRRU设备，编号1-50
LFRRU([1-9]|[1-4][0-9]|50)$             # L频段FRRU设备（无前缀下划线）
_FRRU([1-9]|[1-4][0-9]|50)$             # 通用FRRU设备，编号1-50
FRRU([1-9]|[1-4][0-9]|50)$              # 通用FRRU设备（无前缀下划线）
_LRRU([1-9]|[1-4][0-9]|50)$             # L频段RRU设备，编号1-50
LRRU([1-9]|[1-4][0-9]|50)$              # L频段RRU设备（无前缀下划线）
_RRU([1-9]|[1-4][0-9]|50)$              # 通用RRU设备，编号1-50
RRU([1-9]|[1-4][0-9]|50)$               # 通用RRU设备（无前缀下划线）
AAU([1-9]|[1-4][0-9]|50)$               # AAU有源天线单元，编号1-50
_N21_\d+_\d+$                           # N21频段设备，支持多级编号
_N800_\d+$                              # N800频段设备，支持数字编号
_\d+$                                   # 通用数字后缀，匹配纯数字结尾

# 需要显示的数据列配置
# 定义在处理结果中需要显示的原始数据列
# 每行一个列名，按显示顺序排列
[COLUMNS_TO_SHOW]
区/市/县/旗                             # 行政区域信息
所属营业部名称                           # 营业部归属信息
扇区中文名                              # 扇区的中文名称
基站中文名                              # 基站的中文名称
设备厂家                               # 设备制造商信息
扇区经度                               # 扇区地理位置经度
扇区纬度                               # 扇区地理位置纬度
扇区地址                               # 扇区详细地址
开通日期                               # 扇区开通投入使用时间
天线方位角                              # 天线指向方位角度
总下倾角                               # 天线总下倾角度
天线挂高                               # 天线安装高度



# 频率映射配置
# 将原始频率值映射为标准化的频段标识
# 格式：原始频率=标准频段标识
[FREQUENCY_MAP]
1755=1.8GL                              # 1755MHz映射为1.8GL频段
1775=1.8G                               # 1775MHz映射为1.8G频段
1930=2.1G                               # 1930MHz映射为2.1G频段
1750=1.8GL                              # 1750MHz映射为1.8GL频段
829.2=800M                              # 829.2MHz映射为800M频段
3.5GHz=3.5G                             # 3.5GHz映射为3.5G频段
800MHz=800NR                            # 800MHz映射为800NR频段
2.1GHz=2.1G                             # 2.1GHz映射为2.1G频段




# 输出表头列名映射配置
# 定义处理结果表格的表头列名
# 格式：内部标识=显示名称
[HEADER_COLUMNS]
HEADER_站名=站名                         # 基站名称列标题
HEADER_频段=频段                         # 频段信息列标题
HEADER_站名频段索引=站名频段索引           # 站名频段组合索引列标题
HEADER_逻辑站=逻辑站                     # 逻辑站标识列标题
HEADER_物理站=物理站                     # 物理站标识列标题
HEADER_经度=经度                         # 扇区经度列标题
HEADER_纬度=纬度                         # 扇区纬度列标题
HEADER_小区数=小区数                     # 小区数量统计列标题
HEADER_设备数=设备数                     # 设备数量统计列标题
HEADER_是否有功分=是否有功分              # 功分器状态列标题
HEADER_异常提醒=异常提醒                 # 异常情况提醒列标题
HEADER_下倾角=下倾角                     # 天线下倾角列标题
HEADER_方向角=方向角                     # 天线方向角列标题
HEADER_挂高=挂高                        # 天线挂高列标题
HEADER_经纬度索引=经纬度索引              # 经纬度组合索引列标题

# 源数据列名映射配置
# 定义从源数据中读取的列名与内部字段的映射关系
# 格式：内部字段标识=源数据列名
[SOURCE_COLUMNS]
SOURCE_扇区中文名=扇区中文名              # 扇区中文名称字段映射
SOURCE_频段频点=频段                     # 频段频点信息字段映射
SOURCE_总下倾角=总下倾角                 # 总下倾角数据字段映射
SOURCE_天线方位角=天线方位角              # 天线方位角数据字段映射
SOURCE_天线挂高=天线挂高                 # 天线挂高数据字段映射
SOURCE_扇区经度=扇区经度                 # 扇区经度坐标字段映射
SOURCE_扇区纬度=扇区纬度                 # 扇区纬度坐标字段映射

# ========================================
# 配置文件结束
# 5G基站数据处理器配置完成
# ========================================


