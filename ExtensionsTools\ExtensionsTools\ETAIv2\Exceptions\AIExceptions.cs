using System;

namespace ExtensionsTools.ETAIv2.Exceptions
{
    /// <summary>
    /// AI处理基础异常
    /// </summary>
    public class AIProcessingException : Exception
    {
        public string ErrorCode { get; set; }
        public string Context { get; set; }

        public AIProcessingException(string message) : base(message)
        {
        }

        public AIProcessingException(string message, Exception innerException) : base(message, innerException)
        {
        }

        public AIProcessingException(string message, string errorCode, string context = null) : base(message)
        {
            ErrorCode = errorCode;
            Context = context;
        }

        public AIProcessingException(string message, string errorCode, Exception innerException, string context = null) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
            Context = context;
        }
    }

    /// <summary>
    /// 文件处理异常
    /// </summary>
    public class FileProcessingException : AIProcessingException
    {
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string FileType { get; set; }

        public FileProcessingException(string message, string filePath = null) : base(message)
        {
            FilePath = filePath;
        }

        public FileProcessingException(string message, Exception innerException, string filePath = null) 
            : base(message, innerException)
        {
            FilePath = filePath;
        }

        public FileProcessingException(string message, string filePath, long fileSize, string fileType) : base(message)
        {
            FilePath = filePath;
            FileSize = fileSize;
            FileType = fileType;
        }
    }

    /// <summary>
    /// Excel集成异常
    /// </summary>
    public class ExcelIntegrationException : AIProcessingException
    {
        public string RangeAddress { get; set; }
        public string WorksheetName { get; set; }
        public string WorkbookName { get; set; }

        public ExcelIntegrationException(string message) : base(message)
        {
        }

        public ExcelIntegrationException(string message, Exception innerException) : base(message, innerException)
        {
        }

        public ExcelIntegrationException(string message, string rangeAddress, string worksheetName = null, string workbookName = null) 
            : base(message)
        {
            RangeAddress = rangeAddress;
            WorksheetName = worksheetName;
            WorkbookName = workbookName;
        }
    }

    /// <summary>
    /// AI API异常
    /// </summary>
    public class AIAPIException : AIProcessingException
    {
        public string APIType { get; set; }
        public int? StatusCode { get; set; }
        public string RequestId { get; set; }

        public AIAPIException(string message, string apiType = null) : base(message)
        {
            APIType = apiType;
        }

        public AIAPIException(string message, Exception innerException, string apiType = null) 
            : base(message, innerException)
        {
            APIType = apiType;
        }

        public AIAPIException(string message, string apiType, int statusCode, string requestId = null) : base(message)
        {
            APIType = apiType;
            StatusCode = statusCode;
            RequestId = requestId;
        }
    }

    /// <summary>
    /// 配置异常
    /// </summary>
    public class ConfigurationException : AIProcessingException
    {
        public string ConfigFile { get; set; }
        public string ConfigSection { get; set; }

        public ConfigurationException(string message, string configFile = null) : base(message)
        {
            ConfigFile = configFile;
        }

        public ConfigurationException(string message, Exception innerException, string configFile = null) 
            : base(message, innerException)
        {
            ConfigFile = configFile;
        }

        public ConfigurationException(string message, string configFile, string configSection) : base(message)
        {
            ConfigFile = configFile;
            ConfigSection = configSection;
        }
    }

    /// <summary>
    /// 数据验证异常
    /// </summary>
    public class DataValidationException : AIProcessingException
    {
        public string DataType { get; set; }
        public string ValidationRule { get; set; }

        public DataValidationException(string message, string dataType = null) : base(message)
        {
            DataType = dataType;
        }

        public DataValidationException(string message, Exception innerException, string dataType = null) 
            : base(message, innerException)
        {
            DataType = dataType;
        }

        public DataValidationException(string message, string dataType, string validationRule) : base(message)
        {
            DataType = dataType;
            ValidationRule = validationRule;
        }
    }

    /// <summary>
    /// 网络异常
    /// </summary>
    public class NetworkException : AIProcessingException
    {
        public string Endpoint { get; set; }
        public TimeSpan? Timeout { get; set; }

        public NetworkException(string message, string endpoint = null) : base(message)
        {
            Endpoint = endpoint;
        }

        public NetworkException(string message, Exception innerException, string endpoint = null) 
            : base(message, innerException)
        {
            Endpoint = endpoint;
        }

        public NetworkException(string message, string endpoint, TimeSpan timeout) : base(message)
        {
            Endpoint = endpoint;
            Timeout = timeout;
        }
    }
}
