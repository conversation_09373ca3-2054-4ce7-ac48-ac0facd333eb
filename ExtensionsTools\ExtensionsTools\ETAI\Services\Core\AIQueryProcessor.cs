using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenAI;
using OpenAI.Chat;
using System.Linq;
using ET.AI.Services.Core.Models;
using ET.AI.Models;
using System.ClientModel;

namespace ET.AI.Services.Core
{
    /// <summary>
    /// AI查询处理器，负责处理AI查询请求
    /// </summary>
    /// <remarks>
    /// 该类主要负责：
    /// 1. 处理AI查询组的请求
    /// 2. 管理API请求的重试机制
    /// 3. 处理API响应和错误
    /// 4. 管理请求限制和计数
    /// </remarks>
    class AIQueryProcessor
    {
        readonly AIService _service;
        readonly object _requestCounterLock = new object();
        readonly Queue<DateTime> _requestTimestamps = new Queue<DateTime>();
        int _totalRequestCount = 0;

        /// <summary>
        /// 初始化AI查询处理器
        /// </summary>
        /// <param name="service">AI服务实例</param>
        public AIQueryProcessor(AIService service)
        {
            _service = service;
        }

        /// <summary>
        /// 处理查询组
        /// </summary>
        /// <param name="group">要处理的查询组</param>
        /// <param name="questionGroups">问题组列表</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="onResponseReceived">响应接收回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>包含请求和响应的元组(请求JSON, 响应JSON)</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取可用的API密钥
        /// 2. 执行查询并处理响应
        /// 3. 在失败时进行重试
        /// 4. 触发进度事件通知
        /// </remarks>
        /// <exception cref="Exception">当超过最大重试次数时抛出</exception>
        [System.Runtime.InteropServices.ComVisible(false)]
        internal async Task<(JObject RequestJson, JObject ResponseJson)> ProcessQueryGroup(
            QueryGroup group,
            List<AIQuestionGroup> questionGroups,
            string rulesFileName,
            Action<JObject, bool> onResponseReceived = null,
            CancellationToken cancellationToken = default)
        {
            int currentRetry = 0;
            const int maxRetryCount = 3;
            const int retryDelaySeconds = 2;

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var (apiKey, releaseKey) = await _service.KeyManager.GetNextAvailableKey();
                    try
                    {
                        var (requestJson, result) = await ExecuteQuery(group, questionGroups, rulesFileName, apiKey, currentRetry, cancellationToken);

                        if (onResponseReceived != null)
                        {
                            JObject groupResult = new JObject
                            {
                                ["list"] = new JArray { result }
                            };
                            onResponseReceived(groupResult, false);
                        }

                        _service.RaiseProgressEvent($"完成第{group.GroupIndex}组查询");
                        return (requestJson, result);
                    }
                    finally
                    {
                        releaseKey();
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (currentRetry < maxRetryCount)
                    {
                        currentRetry++;
                        _service.RaiseProgressEvent($"第{group.GroupIndex}组查询失败：{ex.Message}");
                        _service.RaiseProgressEvent($"第{group.GroupIndex}组查询失败，等待{retryDelaySeconds}秒后进行第{currentRetry}次重试...");
                        try
                        {
                            await Task.Delay(retryDelaySeconds * 1000, cancellationToken);
                        }
                        catch (OperationCanceledException)
                        {
                            throw;
                        }
                        continue;
                    }
                    _service.RaiseProgressEvent($"第{group.GroupIndex}组查询失败：{ex.Message}，已达到最大重试次数");
                    throw;
                }
            }
            throw new OperationCanceledException();
        }

        /// <summary>
        /// 执行单个查询
        /// </summary>
        /// <param name="group">查询组</param>
        /// <param name="questionGroups">问题组列表</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="apiKey">API密钥</param>
        /// <param name="currentRetry">当前重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>包含请求JSON和响应JSON的元组</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 构建问题列表
        /// 2. 创建OpenAI客户端
        /// 3. 发送请求并获取响应
        /// 4. 解析响应结果
        /// </remarks>
        [System.Runtime.InteropServices.ComVisible(false)]
        async Task<(JObject RequestJson, JObject ResponseJson)> ExecuteQuery(
            QueryGroup group,
            List<AIQuestionGroup> questionGroups,
            string rulesFileName,
            string apiKey,
            int currentRetry,
            CancellationToken cancellationToken)
        {
            // 构建问题列表
            JArray questionList = new JArray();
            foreach (var item in group.Items)
            {
                // 查找对应的问题组
                AIQuestionGroup questionGroup = questionGroups.FirstOrDefault(g => g.ListIndex == item.ListIndex);
                if (questionGroup == null)
                {
                    throw new ArgumentException($"找不到索引为 {item.ListIndex} 的问题组");
                }
                questionList.Add(questionGroup.ToJObject());
            }

            // 构造请求
            JObject requestJson = AIJsonHelper.ConstructRequestJson(questionList, rulesFileName);
            OpenAIClient client = CreateOpenAIClient(apiKey);
            ChatClient chatClient = client.GetChatClient(_service.CurrentConfig.Model);

            int keyIndex = _service.CurrentConfig.APIKeys.IndexOf(apiKey) + 1;
            string retryMessage = currentRetry > 0 ? $"正在进行第{currentRetry}次重试 " : string.Empty;
            _service.RaiseProgressEvent($"开始处理第{group.GroupIndex}组查询 {retryMessage}[使用Key{keyIndex}]");

            // 发送请求并获取响应
            string response = await GetChatResponse(chatClient, requestJson, cancellationToken);

            // 先保存原始响应，避免JSON解析失败导致无法保存
            _service.SaveRawResponse(group.GroupIndex, requestJson.ToString(Formatting.Indented), response);

            // 解析响应
            return (requestJson, await AIJsonHelper.TryParseJsonResponse(response,
                async () => await GetChatResponse(chatClient, requestJson, cancellationToken)));
        }

        /// <summary>
        /// 创建OpenAI客户端
        /// </summary>
        /// <param name="apiKey">API密钥</param>
        /// <returns>配置好的OpenAI客户端实例</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 配置基础URL
        /// 2. 设置请求超时
        /// 3. 配置代理设置
        /// 4. 创建并返回客户端实例
        /// </remarks>
        OpenAIClient CreateOpenAIClient(string apiKey)
        {
            OpenAIClientOptions options = new OpenAIClientOptions();
            if (!string.IsNullOrEmpty(_service.CurrentConfig.BaseURL))
            {
                options.Endpoint = new Uri(_service.CurrentConfig.BaseURL);
            }

            options.NetworkTimeout = TimeSpan.FromSeconds(_service.CurrentConfig.RequestTimeout ?? AIService.DEFAULT_REQUEST_TIMEOUT);

            // 设置代理
            if (!string.IsNullOrWhiteSpace(_service.CurrentConfig.ProxyHost) && _service.CurrentConfig.ProxyPort.HasValue)
            {
                System.Net.WebRequest.DefaultWebProxy = new System.Net.WebProxy(_service.CurrentConfig.ProxyHost, _service.CurrentConfig.ProxyPort.Value);
            }
            else
            {
                System.Net.WebRequest.DefaultWebProxy = null;
            }

            return new OpenAIClient(new ApiKeyCredential(apiKey), options);
        }

        /// <summary>
        /// 获取聊天响应
        /// </summary>
        /// <param name="chatClient">聊天客户端</param>
        /// <param name="requestJson">请求JSON</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>聊天响应文本</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 构建消息列表
        /// 2. 配置聊天选项
        /// 3. 检查请求限制
        /// 4. 发送请求并获取响应
        /// </remarks>
        /// <exception cref="Exception">当达到总请求数限制时抛出</exception>
        [System.Runtime.InteropServices.ComVisible(false)]
        async Task<string> GetChatResponse(ChatClient chatClient, JObject requestJson, CancellationToken cancellationToken)
        {
            List<ChatMessage> messages = new List<ChatMessage>();
            if (!string.IsNullOrEmpty(_service.CurrentConfig.SystemContent))
            {
                messages.Add(new SystemChatMessage(_service.CurrentConfig.SystemContent));
            }
            messages.Add(new UserChatMessage(requestJson.ToString(Formatting.None)));

            ChatCompletionOptions chatOptions = new ChatCompletionOptions
            {
                Temperature = _service.CurrentConfig.Temperature ?? 0.2f,
                TopP = _service.CurrentConfig.TopP ?? 0.2f,
                ResponseFormat = _service.CurrentConfig.ResponseFormat
            };

            // 检查并增加总请求计数
            lock (_requestCounterLock)
            {
                if (_service.CurrentConfig.TotalMaxRequests.HasValue &&
                    _totalRequestCount >= _service.CurrentConfig.TotalMaxRequests.Value)
                {
                    throw new Exception($"已达到总请求数限制（{_service.CurrentConfig.TotalMaxRequests.Value}次），请稍后再试！");
                }
                _totalRequestCount++;
            }

            ClientResult<ChatCompletion> completion = await chatClient.CompleteChatAsync(messages, chatOptions, cancellationToken);
            return completion.Value.Content[0].Text;
        }
    }
}