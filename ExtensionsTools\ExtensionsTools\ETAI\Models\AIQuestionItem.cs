using System;

namespace ET.AI.Models
{
    /// <summary>
    /// AI问题项类，表示单个问题的信息
    /// </summary>
    /// <remarks>
    /// 该类用于封装单个问题的相关信息，包括：
    /// <list type="bullet">
    /// <item><description>问题的索引位置</description></item>
    /// <item><description>问题的具体内容</description></item>
    /// </list>
    /// 通常作为<see cref="AIQuestionGroup"/>的成员使用。
    /// </remarks>
    public class AIQuestionItem
    {
        /// <summary>
        /// 获取或设置问题索引
        /// </summary>
        /// <remarks>
        /// 表示问题在Excel中的位置：
        /// <list type="bullet">
        /// <item><description>在问题行模式下，表示列索引</description></item>
        /// <item><description>在问题列模式下，表示行索引</description></item>
        /// </list>
        /// </remarks>
        public int QuestionIndex { get; set; }

        /// <summary>
        /// 获取或设置问题内容
        /// </summary>
        /// <remarks>
        /// 存储从Excel单元格中读取的问题文本。
        /// 如果单元格为空或格式不正确，可能为null或空字符串。
        /// </remarks>
        public string Question { get; set; }
    }
}