# HHUcDirectorySelect 目录选择控件功能说明

## 概述

`HHUcDirectorySelect` 是一个增强的目录选择用户控件，在原有功能基础上新增了智能历史记录管理功能。该控件参照 `ETUcFileSelect` 的实现方式，为用户提供更便捷的目录选择体验。

## 主要改进

### 1. 控件结构优化
- **原来**: 使用 `RichTextBox` 显示路径
- **现在**: 使用 `ComboBox` 显示路径并支持历史记录下拉选择

### 2. 新增历史记录功能
- 自动保存最近30个使用的目录路径
- 按使用时间排序，最新的在最前面
- 支持下拉列表快速选择历史路径
- 重复路径自动去重并移至最前
- 历史记录文件按窗体和控件名称独立存储

## 核心功能特性

### 1. 基础功能
- **目录选择**: 点击选择按钮打开文件夹浏览对话框
- **路径显示**: ComboBox显示当前选择的目录路径
- **事件通知**: 路径选择和文本变更事件
- **自适应布局**: 界面自动调整适应不同尺寸

### 2. 历史记录管理
- **自动保存**: 通过对话框选择目录时自动保存到历史记录
- **智能加载**: 控件加载时自动读取历史记录
- **去重处理**: 相同路径自动去重，最新的移至顶部
- **数量限制**: 最多保存30条历史记录，超出自动删除最旧的
- **独立存储**: 每个窗体的每个控件都有独立的历史记录文件

### 3. 配置选项
- **AutoFillLatestValue**: 是否自动填充最近使用的路径（默认: true）
- **Name**: 控件名称，用于生成独立的历史记录文件

## 使用方法

### 1. 基本使用
```csharp
// 创建控件实例
HHUcDirectorySelect directorySelect = new HHUcDirectorySelect();
directorySelect.Name = "myDirectorySelect"; // 设置控件名称
directorySelect.AutoFillLatestValue = true; // 启用自动填充

// 添加事件处理
directorySelect.OnPathSelected += (path) => {
    Console.WriteLine($"选择的路径: {path}");
};
```

### 2. 获取和设置路径
```csharp
// 获取当前路径
string currentPath = directorySelect.Text;

// 设置路径
directorySelect.Text = @"C:\MyFolder";
```

### 3. 手动保存历史记录
```csharp
// 保存当前路径到历史记录文件
directorySelect.SavePathHistoryToFile();
```

## 历史记录存储机制

### 1. 存储位置
```
应用程序目录/Data/ET/Cache/HHUcDirectorySelect/[窗体类名]_[控件名].data
```

### 2. 文件格式
- 纯文本文件，每行一个路径
- UTF-8编码
- 按使用时间倒序排列（最新的在最前面）

### 3. 自动管理
- **加载时机**: 控件Load事件触发时
- **保存时机**: 通过对话框选择目录时
- **清理机制**: 超过30条记录时自动删除最旧的

## 事件说明

### 1. OnPathSelected
- **触发时机**: 通过文件夹浏览对话框选择目录后
- **参数**: string filePath - 选择的目录路径
- **用途**: 处理目录选择完成后的业务逻辑

### 2. TextChanged
- **触发时机**: ComboBox文本内容发生变化时
- **参数**: object sender, EventArgs e
- **用途**: 实时响应路径文本的变化

## 属性说明

### 1. Text (override)
- **类型**: string
- **说明**: 获取或设置当前显示的目录路径
- **特点**: 直接操作ComboBox的Text属性

### 2. AutoFillLatestValue
- **类型**: bool
- **默认值**: true
- **说明**: 是否在控件加载时自动填充最近使用的路径

### 3. Name
- **类型**: string
- **说明**: 控件名称，用于生成独立的历史记录文件
- **重要性**: 必须设置，否则历史记录可能冲突

## 最佳实践

### 1. 控件命名
```csharp
// 为每个控件设置有意义的名称
directorySelectInput.Name = "inputDirectory";
directorySelectOutput.Name = "outputDirectory";
```

### 2. 事件处理
```csharp
// 推荐在路径选择事件中处理业务逻辑
directorySelect.OnPathSelected += (path) => {
    // 验证路径有效性
    if (Directory.Exists(path)) {
        // 处理有效路径
        ProcessDirectory(path);
    }
};
```

### 3. 历史记录管理
```csharp
// 在窗体关闭时可选择性保存当前路径
private void Form_FormClosing(object sender, FormClosingEventArgs e) {
    if (!string.IsNullOrWhiteSpace(directorySelect.Text)) {
        directorySelect.SavePathHistoryToFile();
    }
}
```

## 注意事项

1. **控件名称**: 必须为每个控件设置唯一的Name属性，避免历史记录冲突
2. **路径验证**: 建议在使用路径前验证其有效性
3. **异常处理**: 历史记录读写失败时会静默处理，不影响控件正常使用
4. **性能考虑**: 历史记录文件较小，对性能影响微乎其微
5. **兼容性**: 完全向后兼容，现有代码无需修改即可使用新功能

## 技术实现细节

### 1. 控件结构变更
- 将 `RichTextBox textBox路径` 替换为 `ComboBox comboBox路径`
- 更新相关的事件处理方法名称
- 保持原有的界面布局和样式

### 2. 历史记录实现
- 参照 `ETUcFileSelect` 的历史记录管理机制
- 使用相同的存储路径结构和文件格式
- 实现相同的加载、保存、去重逻辑

### 3. 兼容性保证
- 保持原有的公共接口不变
- 事件签名和属性访问方式保持一致
- 新增功能通过可选属性控制，默认启用
