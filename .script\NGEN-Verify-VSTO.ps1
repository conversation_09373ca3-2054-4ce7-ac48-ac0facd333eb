# NGEN Verification Script for VSTO Plugin
# Can run without Administrator privileges

param(
    [string]$BuildConfiguration = "Debug"
)

Write-Host "=== NGEN Verification Script for HyExcelVsto ===" -ForegroundColor Cyan
Write-Host "Configuration: $BuildConfiguration" -ForegroundColor Yellow

# Define paths
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$binPath = Join-Path $projectRoot "HyExcelVsto\bin\$BuildConfiguration"
$ngenPath = "${env:WINDIR}\Microsoft.NET\Framework64\v4.0.30319\ngen.exe"
$ngen32Path = "${env:WINDIR}\Microsoft.NET\Framework\v4.0.30319\ngen.exe"

Write-Host "`nBin Path: $binPath" -ForegroundColor Green

# Function to check NGEN status for an assembly
function Check-NgenStatus {
    param(
        [string]$AssemblyPath,
        [string]$AssemblyName
    )
    
    if (-not (Test-Path $AssemblyPath)) {
        Write-Host "$AssemblyName : FILE NOT FOUND" -ForegroundColor Red
        return
    }
    
    $status64 = "Not Installed"
    $status32 = "Not Installed"
    
    try {
        # Check 64-bit
        $result64 = & $ngenPath display "$AssemblyPath" 2>&1
        if ($LASTEXITCODE -eq 0 -and $result64 -notlike "*not found*") {
            $status64 = "Installed"
        }
    }
    catch {
        $status64 = "Error"
    }
    
    try {
        # Check 32-bit
        $result32 = & $ngen32Path display "$AssemblyPath" 2>&1
        if ($LASTEXITCODE -eq 0 -and $result32 -notlike "*not found*") {
            $status32 = "Installed"
        }
    }
    catch {
        $status32 = "Error"
    }
    
    $color = "Red"
    if ($status64 -eq "Installed" -and $status32 -eq "Installed") {
        $color = "Green"
    } elseif ($status64 -eq "Installed" -or $status32 -eq "Installed") {
        $color = "Yellow"
    }
    
    Write-Host "$AssemblyName : x64=$status64, x86=$status32" -ForegroundColor $color
}

# Check all assemblies
$assembliesToCheck = @(
    # Main assemblies
    "HyExcelVsto.dll",
    "ExtensionsTools.dll",
    
    # Microsoft Office Tools
    "Microsoft.Office.Tools.dll",
    "Microsoft.Office.Tools.Common.dll",
    "Microsoft.Office.Tools.Excel.dll",
    "Microsoft.Office.Tools.v4.0.Framework.dll",
    "Microsoft.Office.Tools.Common.v4.0.Utilities.dll",
    "Microsoft.VisualStudio.Tools.Applications.Runtime.dll",
    
    # High-priority dependencies
    "Newtonsoft.Json.dll",
    "System.Text.Json.dll",
    "System.Memory.dll",
    "System.Buffers.dll",
    "System.Runtime.CompilerServices.Unsafe.dll",
    "System.Threading.Tasks.Extensions.dll",
    
    # ServiceStack
    "ServiceStack.dll",
    "ServiceStack.Client.dll",
    "ServiceStack.Common.dll",
    "ServiceStack.Interfaces.dll",
    "ServiceStack.Text.dll",
    
    # Microsoft Extensions
    "Microsoft.Extensions.DependencyInjection.dll",
    "Microsoft.Extensions.DependencyInjection.Abstractions.dll",
    "Microsoft.Extensions.Logging.dll",
    "Microsoft.Extensions.Logging.Abstractions.dll",
    "Microsoft.Bcl.AsyncInterfaces.dll",
    
    # Other important dependencies
    "AngleSharp.dll",
    "Castle.Core.dll",
    "SharpCompress.dll",
    "System.Collections.Immutable.dll",
    "System.Diagnostics.DiagnosticSource.dll",
    "System.Drawing.Common.dll"
)

Write-Host "`n=== NGEN Status Check ===" -ForegroundColor Cyan
Write-Host "Legend: Green=Both installed, Yellow=Partial, Red=Not installed" -ForegroundColor Gray

$installedCount = 0
$totalCount = 0

foreach ($assembly in $assembliesToCheck) {
    $assemblyPath = Join-Path $binPath $assembly
    Check-NgenStatus -AssemblyPath $assemblyPath -AssemblyName $assembly
    
    if (Test-Path $assemblyPath) {
        $totalCount++
        
        # Quick check if at least one architecture is installed
        try {
            $result = & $ngenPath display "$assemblyPath" 2>&1
            if ($LASTEXITCODE -eq 0 -and $result -notlike "*not found*") {
                $installedCount++
            } else {
                $result = & $ngen32Path display "$assemblyPath" 2>&1
                if ($LASTEXITCODE -eq 0 -and $result -notlike "*not found*") {
                    $installedCount++
                }
            }
        }
        catch {
            # Ignore errors for counting
        }
    }
}

# Summary
Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "Total assemblies checked: $totalCount" -ForegroundColor White
Write-Host "Assemblies with native images: $installedCount" -ForegroundColor White

$percentage = if ($totalCount -gt 0) { [math]::Round(($installedCount / $totalCount) * 100, 1) } else { 0 }
Write-Host "Coverage: $percentage%" -ForegroundColor $(if ($percentage -gt 80) { "Green" } elseif ($percentage -gt 50) { "Yellow" } else { "Red" })

# Performance recommendations
Write-Host "`n=== Performance Recommendations ===" -ForegroundColor Cyan
if ($percentage -lt 50) {
    Write-Host "LOW COVERAGE: Run 'NGEN-Install-VSTO.ps1' as Administrator to improve performance" -ForegroundColor Red
} elseif ($percentage -lt 80) {
    Write-Host "PARTIAL COVERAGE: Some assemblies may benefit from native image generation" -ForegroundColor Yellow
} else {
    Write-Host "GOOD COVERAGE: Most assemblies have native images for faster loading" -ForegroundColor Green
}

# Additional system info
Write-Host "`n=== System Information ===" -ForegroundColor Cyan
Write-Host ".NET Framework Version: $([System.Runtime.InteropServices.RuntimeInformation]::FrameworkDescription)" -ForegroundColor Gray
Write-Host "OS Architecture: $([System.Runtime.InteropServices.RuntimeInformation]::OSArchitecture)" -ForegroundColor Gray
Write-Host "Process Architecture: $([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)" -ForegroundColor Gray

Read-Host "`nPress any key to exit"
