using ET;
using System;
using System.Windows.Forms;

namespace ExtensionsTools.Examples
{
    /// <summary>
    /// TextBox历史记录功能使用示例
    /// </summary>
    /// <remarks>
    /// 演示如何使用ETForm.BindTextBox方法为TextBox添加历史记录功能
    /// </remarks>
    public partial class TextBoxHistoryExample : Form
    {
        private TextBox textBox1;
        private TextBox textBox2;
        private Button btnTest;
        private Label lblInstructions;

        public TextBoxHistoryExample()
        {
            InitializeComponent();
            InitializeTextBoxHistory();
        }

        /// <summary>
        /// 初始化窗体控件
        /// </summary>
        private void InitializeComponent()
        {
            this.textBox1 = new TextBox();
            this.textBox2 = new TextBox();
            this.btnTest = new Button();
            this.lblInstructions = new Label();
            this.SuspendLayout();

            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(30, 50);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(300, 23);
            this.textBox1.TabIndex = 0;

            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(30, 100);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(300, 23);
            this.textBox2.TabIndex = 1;

            // 
            // btnTest
            // 
            this.btnTest.Location = new System.Drawing.Point(30, 150);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new System.Drawing.Size(100, 30);
            this.btnTest.TabIndex = 2;
            this.btnTest.Text = "测试按钮";
            this.btnTest.UseVisualStyleBackColor = true;

            // 
            // lblInstructions
            // 
            this.lblInstructions.AutoSize = true;
            this.lblInstructions.Location = new System.Drawing.Point(30, 20);
            this.lblInstructions.Name = "lblInstructions";
            this.lblInstructions.Size = new System.Drawing.Size(400, 15);
            this.lblInstructions.TabIndex = 3;
            this.lblInstructions.Text = "在文本框中输入内容，然后右键查看历史记录功能菜单";

            // 
            // TextBoxHistoryExample
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(450, 220);
            this.Controls.Add(this.lblInstructions);
            this.Controls.Add(this.btnTest);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.textBox1);
            this.Name = "TextBoxHistoryExample";
            this.Text = "TextBox历史记录功能示例";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        /// <summary>
        /// 初始化TextBox历史记录功能
        /// </summary>
        private void InitializeTextBoxHistory()
        {
            try
            {
                // 为textBox1绑定历史记录功能，使用默认设置（最多10条记录，自动填充最新值）
                ETForm.BindTextBox(textBox1);

                // 为textBox2绑定历史记录功能，自定义设置（最多5条记录，不自动填充）
                ETForm.BindTextBox(textBox2, maxHistoryCount: 5, autoFillLatestValue: false);

                // 也可以使用自定义historyKey的方式
                // ETForm.BindTextBox(textBox1, "CustomKey_TextBox1", 15, true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化TextBox历史记录功能时发生错误：{ex.Message}", 
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// 程序入口点示例
    /// </summary>
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TextBoxHistoryExample());
        }
    }
}
