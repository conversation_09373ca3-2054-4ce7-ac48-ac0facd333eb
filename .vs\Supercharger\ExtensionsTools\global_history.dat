<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETRangeSelectControl.GetExcelApplication#Microsoft.Office.Interop.Excel.Application#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetExcelApplication</ItemName><ItemPath>ET.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:28:52.8016157+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETRangeSelectControl.button选择_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button选择_Click</ItemName><ItemPath>ET.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:21:26.74097+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETRangeSelectControl.ShowSelectionBox#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowSelectionBox</ItemName><ItemPath>ET.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-27T21:17:46.6753454+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETRangeSelectControl.textBox地址_Leave#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>textBox地址_Leave</ItemName><ItemPath>ET.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-27T21:15:49.0627279+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETUcFileSelect.GetHistoryFilePath#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetHistoryFilePath</ItemName><ItemPath>ET.Controls.ETUcFileSelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:27:30.993937+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETUcFileSelect</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETUcFileSelect</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:27:19.8932298+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETUcFileSelect.PathSelectedHandler</ID><ImageSource>img\tvi\y_delegate-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PathSelectedHandler</ItemName><ItemPath>ET.Controls.ETUcFileSelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:27:18.4789278+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETUcFileSelect.AutoFillLatestValue</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AutoFillLatestValue</ItemName><ItemPath>ET.Controls.ETUcFileSelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName><TimeStamp>2025-07-28T14:23:35.3846497+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETRemoteLicenseProvider.GetDataWithRetryAsync#Task&lt;byte[]&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetDataWithRetryAsync</ItemName><ItemPath>ET.ETLicense.ETRemoteLicenseProvider</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName><TimeStamp>2025-06-29T13:00:14.9712212+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETRemoteLicenseProvider.IsRetriableExceptionstatic##bool#Exception</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IsRetriableException</ItemName><ItemPath>ET.ETLicense.ETRemoteLicenseProvider</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName><TimeStamp>2025-06-29T13:00:13.1536257+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETRemoteLicenseProvider.GetLicenseInfoAsync#Task&lt;ETLicenseInfo&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetLicenseInfoAsync</ItemName><ItemPath>ET.ETLicense.ETRemoteLicenseProvider</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName><TimeStamp>2025-06-29T12:56:29.0778517+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.HHUcDirectorySelect.Text.set</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>set</ItemName><ItemPath>ET.Controls.HHUcDirectorySelect.Text</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:54:24.0777882+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.HHUcDirectorySelect.Text</ID><ImageSource>img\tvi\x_property_override-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Text</ItemName><ItemPath>ET.Controls.HHUcDirectorySelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:54:11.8431246+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETAutoCollapseWindowBehavior.ShowFullWindow#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowFullWindow</ItemName><ItemPath>ET.ETAutoCollapseWindowBehavior</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName><TimeStamp>2025-06-20T21:51:05.6781281+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowser.WebView_NavigationCompleted#void#object, CoreWebView2NavigationCompletedEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebView_NavigationCompleted</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:11:45.6016353+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowser.ETLoginWebBrowser_Load#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLoginWebBrowser_Load</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:01:55.6804387+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Utils.AIConfigManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AIConfigManager</ItemName><ItemPath>ExtensionsTools.ETAIv2.Utils</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:02:38.2641468+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Utils.AIConfigManager.AIConfigManager##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AIConfigManager</ItemName><ItemPath>ExtensionsTools.ETAIv2.Utils.AIConfigManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:02:32.34417+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.AIExcelAssistant.ProcessExcelDataAsync#Task&lt;AIResponse&gt;#Range, Range, Range, Range, string, string, DataSourceMode, FileProcessingMode, bool, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessExcelDataAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.AIExcelAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:56:53.4721736+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.AIExcelAssistant.AIExcelAssistant##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AIExcelAssistant</ItemName><ItemPath>ExtensionsTools.ETAIv2.AIExcelAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:45:54.1898225+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETLicenseController.ForceRefreshAllWithCallbackAsync#Task#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ForceRefreshAllWithCallbackAsync</ItemName><ItemPath>ET.ETLicense.ETLicenseController</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName><TimeStamp>2025-06-29T20:43:39.3402247+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.HHExcelExtensions</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HHExcelExtensions</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:35:11.2437537+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETConfig.GetConfigDirectorystatic##string#string, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetConfigDirectory</ItemName><ItemPath>ET.ETConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETConfig.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:24:11.8640413+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETConfig.GetETConfigIniFilePathstatic##string#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetETConfigIniFilePath</ItemName><ItemPath>ET.ETConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETConfig.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:21:04.6463124+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETConfig</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETConfig</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETConfig.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:20:12.7061674+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETExcel.Controls.ETLogDisplayControl.InitializeLogDisplay#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeLogDisplay</ItemName><ItemPath>ExtensionsTools.ETExcel.Controls.ETLogDisplayControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName><TimeStamp>2025-07-22T22:02:38.1242303+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETExcel.Controls.ETLogDisplayControl.GetDefaultInitialMessage#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetDefaultInitialMessage</ItemName><ItemPath>ExtensionsTools.ETExcel.Controls.ETLogDisplayControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName><TimeStamp>2025-07-22T22:02:27.1354631+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETForm.CreateDefaultConfigFilestatic##void#string, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateDefaultConfigFile</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:31:52.9299408+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.LoadRibbonGalleryFromConfigstatic##void#object, string, object, System.Action</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadRibbonGalleryFromConfig</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:24:54.5694982+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.LoadContextMenuStripstatic##void#ContextMenuStrip, Dictionary&lt;string, string[]&gt;, TextBox</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadContextMenuStrip</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:53.1563891+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.LoadContextMenuStripFromConfigstatic##void#ContextMenuStrip, string, TextBox</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadContextMenuStripFromConfig</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:52.3212146+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.HandleRibbonGalleryFileClickstatic##bool#string, object</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HandleRibbonGalleryFileClick</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:49.178121+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.HandleRibbonGalleryClickCompletestatic##bool#object, string, object</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HandleRibbonGalleryClickComplete</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:48.1258805+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.HandleRibbonGalleryItemClickstatic##bool#string, string, System.Action, System.Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HandleRibbonGalleryItemClick</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:46.9976133+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.CheckedListBoxItemInvalidstatic##void#CheckedListBox, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CheckedListBoxItemInvalid</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:42.9597836+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.AddMessageToListviewstatic##void#ListView, string, bool</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AddMessageToListview</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:33.8872148+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.AddConfigManagementButtonsstatic##void#object, string, object, System.Action</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AddConfigManagementButtons</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:23:22.9188314+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.HandleConfigManagementOperationstatic##bool#string, string, System.Action, bool</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HandleConfigManagementOperation</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:21:18.4840317+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm.RibbonGalleryConfigInfo</ID><ImageSource>img\tvi\y_class-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>RibbonGalleryConfigInfo</ItemName><ItemPath>ET.ETForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T23:21:00.9510651+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETForm</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETForm</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:58:07.394013+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.FindMatchingBracket#int#string, int, char, char</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FindMatchingBracket</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-24T14:47:08.6643127+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.BuildRequestContent#string#AIRequest</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BuildRequestContent</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T22:08:17.4762112+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.SendChatRequestAsync#Task&lt;AIResponse&gt;#AIRequest, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SendChatRequestAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T21:11:00.5835298+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.DetermineOptimalAPI#APIType#AIRequest</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DetermineOptimalAPI</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T21:02:04.9848343+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.IsAssistantAPISupported#bool#AIModelConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IsAssistantAPISupported</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:57:55.1438824+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient.ValidateModelConfig#void#AIModelConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidateModelConfig</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIClient</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:57:49.1042493+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIClient</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AIClient</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:55:35.3351337+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETAboutLicenseForm.btnUpdateLicense_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnUpdateLicense_Click</ItemName><ItemPath>ET.ETLicense.ETAboutLicenseForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName><TimeStamp>2025-06-29T20:43:27.799424+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETLicenseManager.WriteFileSecurelystatic##void#string, byte[]</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WriteFileSecurely</ItemName><ItemPath>ET.ETLicense.ETLicenseManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName><TimeStamp>2025-06-29T13:00:12.0679288+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETNotificationHelper.ShowNotificationstatic##void#string, bool, bool, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowNotification</ItemName><ItemPath>ET.ETNotificationHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName><TimeStamp>2025-07-30T09:12:42.4784699+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlPermissionManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETControlPermissionManager</ItemName><ItemPath>ET.ETLicense</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T15:05:44.3507819+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlPermissionManager.RegisterControlPermissionMapping#void#string, string, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RegisterControlPermissionMapping</ItemName><ItemPath>ET.ETLicense.ETControlPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T15:05:43.3320819+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlPermissionManager._controlTitleMapping</ID><ImageSource>img\tvi\x_var-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>_controlTitleMapping</ItemName><ItemPath>ET.ETLicense.ETControlPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T15:04:03.224218+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETExcel.Controls.ETRangeSelectControl.InitializeControlSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeControlSettings</ItemName><ItemPath>ExtensionsTools.ETExcel.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName><TimeStamp>2025-07-18T10:55:33.5825058+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETUcFileSelect.ETUcFileSelect##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETUcFileSelect</ItemName><ItemPath>ET.ETUcFileSelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:47:58.9844304+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETString</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETString</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETString.cs</ProjectItemFileName><TimeStamp>2025-07-03T22:37:02.6800834+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserTest.GetHeaderSummarystatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetHeaderSummary</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowserTest</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName><TimeStamp>2025-07-04T23:14:22.3366395+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserTest.ShowHeaderDetailsstatic##void#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowHeaderDetails</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowserTest</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName><TimeStamp>2025-07-04T23:14:13.13298+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserTest</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLoginWebBrowserTest</ItemName><ItemPath>ET.ETLoginWebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName><TimeStamp>2025-07-04T23:03:21.2472324+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserTest.TestRefreshCookiesstatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TestRefreshCookies</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowserTest</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName><TimeStamp>2025-07-04T23:01:29.878743+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETExcel.Controls.ETRangeSelectControl.Dispose#void#bool</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Dispose</ItemName><ItemPath>ExtensionsTools.ETExcel.Controls.ETRangeSelectControl</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:53:19.6256965+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelDataSummarizer.SummarizeDatastatic##Range#Range, Range, List&lt;string&gt;, SummarizeOptions</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SummarizeData</ItemName><ItemPath>ET.ETExcelDataSummarizer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:59:48.9480805+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.CopyWorksheetFromTemplateToWorkbookstatic##Worksheet#string, Workbook, bool, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CopyWorksheetFromTemplateToWorkbook</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:10:32.1787193+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.SaveCopyAsstatic##FileInfo#Workbook, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SaveCopyAs</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName><TimeStamp>2025-07-28T18:36:55.7337894+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelConfig</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETExcelConfig</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:10:28.3638384+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETExcelConfig.TemplateExcelPath</ID><ImageSource>img\tvi\x_var-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TemplateExcelPath</ItemName><ItemPath>ET.ETExcelConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:10:18.8673031+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.HHExcelExtensions.Formate设置背景色static##void#Range, EnumColorNum</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Formate设置背景色</ItemName><ItemPath>ET.HHExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName><TimeStamp>2025-07-17T21:46:21.3650795+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETStringPrefixSuffixProcessor.Initializestatic##void#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Initialize</ItemName><ItemPath>ET.ETStringPrefixSuffixProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:07:10.567606+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETStringPrefixSuffixProcessor</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETStringPrefixSuffixProcessor</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:05:32.2646065+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.CreateLoginInfoJsonFromWebViewAsyncstatic##Task&lt;string&gt;#WebView2, Dictionary&lt;string, string&gt;, HeadersOptions</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateLoginInfoJsonFromWebViewAsync</ItemName><ItemPath>ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:17:13.2240981+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.FindHeadersInRangestatic##Dictionary&lt;string, Range&gt;#Range, string[], bool, Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FindHeadersInRange</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:40:15.7934553+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.FindHeadersInRangeCorestatic##Dictionary&lt;string, Range&gt;#object, string[], bool, Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FindHeadersInRangeCore</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:40:02.8316679+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.FindColumnsByHeadersInternalstatic##List&lt;Range&gt;#string, object, bool, Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FindColumnsByHeadersInternal</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:39:43.5782456+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.FindColumnByHeaderTitlestatic##Range#string, Range, Action&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FindColumnByHeaderTitle</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName><TimeStamp>2025-07-23T14:39:32.6859354+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.IETOssService</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IETOssService</ItemName><ItemPath>ET.ETLicense</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName><TimeStamp>2025-06-20T22:53:29.1865237+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.IETOssService.GetLicenseFromOssAsync#Task&lt;ETLicenseInfo&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetLicenseFromOssAsync</ItemName><ItemPath>ET.ETLicense.IETOssService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName><TimeStamp>2025-06-20T22:52:39.322422+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETOssService.ETOssService##ETOssConfig, IETLicenseCryptoService</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETOssService</ItemName><ItemPath>ET.ETLicense.ETOssService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName><TimeStamp>2025-06-20T22:42:46.881145+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.HHUcDirectorySelect</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HHUcDirectorySelect</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:54:06.5404829+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETSectionConfigReader</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETSectionConfigReader</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:17:17.5152966+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETSectionConfigReader.TestConfigReaderstatic##void#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TestConfigReader</ItemName><ItemPath>ET.ETSectionConfigReader</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-19T16:16:26.4079612+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowser</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLoginWebBrowser</ItemName><ItemPath>ET.ETLoginWebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-12T22:15:16.2460272+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETGPS.FindClosestPointsstatic##List&lt;XlGpsPointAndRange&gt;#List&lt;XlGpsPointAndRange&gt;, XlGpsPoint, int</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FindClosestPoints</ItemName><ItemPath>ET.ETGPS</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName><TimeStamp>2025-07-20T20:29:34.6098741+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETGPS</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETGPS</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName><TimeStamp>2025-07-20T20:20:36.2514294+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETGPS.ExtractGpsListstatic##List&lt;XlGpsPointAndRange&gt;#Range</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExtractGpsList</ItemName><ItemPath>ET.ETGPS</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName><TimeStamp>2025-07-20T20:06:36.5381899+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.IsCellEmptystatic##bool#Range, bool</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsCellEmpty</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName><TimeStamp>2025-07-27T13:49:57.5100896+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETExcelExtensions.IsCellNonTextstatic##bool#Range</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsCellNonText</ItemName><ItemPath>ET.ETExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName><TimeStamp>2025-07-27T13:49:52.9542609+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.GetControlStructure#Dictionary&lt;string, ETRibbonControlInfo&gt;#Func&lt;Type, bool&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetControlStructure</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:53:25.3753215+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.GetControlParent#string#object</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetControlParent</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:53:24.529913+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.GetControlLabel#string#object</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetControlLabel</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:53:22.7528245+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.CreateControlInfo#ETRibbonControlInfo#string, object, Type</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateControlInfo</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:53:20.5182126+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.CreateForRibbonstatic##ETControlMappingGenerator#object, Type</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateForRibbon</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:52:51.9451057+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETControlMappingGenerator.ETControlMappingGenerator##object, Type, BindingFlags</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETControlMappingGenerator</ItemName><ItemPath>ET.ETLicense.ETControlMappingGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-01T09:52:22.6904363+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETInputDialog.ETInputDialog##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETInputDialog</ItemName><ItemPath>ET.Controls.ETInputDialog</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:14:43.8801044+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETInputDialog.MaxPromptWidth</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>MaxPromptWidth</ItemName><ItemPath>ET.Controls.ETInputDialog</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:12:17.4607908+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETInputDialog.MaxPromptWidth.set</ID><ImageSource/><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>set</ItemName><ItemPath>ET.Controls.ETInputDialog.MaxPromptWidth</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName><TimeStamp>2025-07-30T22:12:17.4456059+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETIniFile</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETIniFile</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETIniFile.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:57:24.3361453+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETIniFile.GetSection#Dictionary&lt;string, string&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetSection</ItemName><ItemPath>ET.ETIniFile</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETIniFile.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:56:58.4555764+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETIniFile.ETIniFile##string</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ETIniFile</ItemName><ItemPath>ET.ETIniFile</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETIniFile.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:56:54.3728156+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.HHExcelExtensions</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HHExcelExtensions</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:35:29.2526988+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserFactory</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLoginWebBrowserFactory</ItemName><ItemPath>ET.ETLoginWebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:57:10.1016835+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLoginWebBrowser.ETLoginWebBrowserFactory.GetHeadersJsonstatic##string#string, Form</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetHeadersJson</ItemName><ItemPath>ET.ETLoginWebBrowser.ETLoginWebBrowserFactory</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:40:23.981776+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETOssBrowserForm.ListObjects#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ListObjects</ItemName><ItemPath>ET.ETLicense.ETOssBrowserForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName><TimeStamp>2025-06-28T16:07:12.8675769+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETPermissionManager.HasPermission#bool#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HasPermission</ItemName><ItemPath>ET.ETLicense.ETPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-03T13:46:04.8509784+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETPermissionManager.CheckPermissionAsync#Task&lt;bool&gt;#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CheckPermissionAsync</ItemName><ItemPath>ET.ETLicense.ETPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-03T13:45:59.9806407+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETUIPermissionManager.CreateUIActionsstatic##List&lt;Action&lt;bool&gt;&gt;#Action&lt;bool&gt;[]</ID><ImageSource>img\tvi\x_method-s_protected-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateUIActions</ItemName><ItemPath>ET.ETLicense.ETUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T10:39:16.7796498+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.DefaultExcelApplicationProvider</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DefaultExcelApplicationProvider</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:29:43.3466319+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.DefaultExcelApplicationProvider.GetExcelApplication#Application#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetExcelApplication</ItemName><ItemPath>ET.Controls.DefaultExcelApplicationProvider</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:29:03.7834553+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.IExcelApplicationProvider</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IExcelApplicationProvider</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:28:57.6291343+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.IExcelApplicationProvider.GetExcelApplication#Application#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetExcelApplication</ItemName><ItemPath>ET.Controls.IExcelApplicationProvider</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName><TimeStamp>2025-07-27T22:20:46.5939047+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETUcFileSelect</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETUcFileSelect</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:51:47.4975792+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETExcel.Controls.ETUcFileSelect.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>ExtensionsTools.ETExcel.Controls.ETUcFileSelect</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:49:40.2991094+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.HHExcelExtensions.OptimizeRangeSizestatic##Range#Range</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>OptimizeRangeSize</ItemName><ItemPath>ET.HHExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName><TimeStamp>2025-07-17T14:25:56.2751906+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.HHExcelExtensions.OptimizeSingleAreaRangestatic##Range#Range</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>OptimizeSingleAreaRange</ItemName><ItemPath>ET.HHExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName><TimeStamp>2025-07-17T14:22:21.13458+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.HHExcelExtensions.Resizestatic##Range#Range</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Resize</ItemName><ItemPath>ET.HHExcelExtensions</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName><TimeStamp>2025-07-17T14:21:08.7704988+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Interfaces.IAIProcessingManager.ProcessAsync#Task&lt;AIResponse&gt;#AIDataSourceConfig, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Interfaces.IAIProcessingManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName><TimeStamp>2025-06-22T21:02:04.7901994+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.UploadFileAsync#Task&lt;FileData&gt;#string, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFileAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:55:50.2873394+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.AIFileProcessor##IAILogger, IAIConfigManager</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>AIFileProcessor</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:55:47.5038264+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.CleanupUploadedFilesAsync#Task#List&lt;string&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CleanupUploadedFilesAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:54:40.8918511+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.ProcessFilesAsync#Task&lt;List&lt;FileData&gt;&gt;#List&lt;string&gt;, FileProcessingMode, AIModelConfig, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessFilesAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:52:23.791964+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.CreateOpenAIFileClient#OpenAIFileClient#AIModelConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateOpenAIFileClient</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:52:06.559153+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.ValidateFileForUpload#void#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidateFileForUpload</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:50:08.6327611+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.ReadFileLocallyAsync#Task&lt;FileData&gt;#string, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ReadFileLocallyAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:44:47.7870408+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AIFileProcessor</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:42:28.0785402+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor.AnalyzeUploadError#string#Exception, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AnalyzeUploadError</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIFileProcessor</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:35:06.0121392+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Utils.AILogger.LogInfo#void#string, object[]</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogInfo</ItemName><ItemPath>ExtensionsTools.ETAIv2.Utils.AILogger</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:49:56.6937688+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Utils.AILogger.FormatMessage#string#string, string, object[]</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatMessage</ItemName><ItemPath>ExtensionsTools.ETAIv2.Utils.AILogger</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:49:53.0595993+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Utils.AILogger.LogError#void#string, Exception, object[]</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogError</ItemName><ItemPath>ExtensionsTools.ETAIv2.Utils.AILogger</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:37:37.6849941+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETLicenseManagerBase.InitializeLicenseController#void#int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>InitializeLicenseController</ItemName><ItemPath>ET.ETLicense.ETLicenseManagerBase</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName><TimeStamp>2025-07-01T23:17:02.6662865+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLogManager.Errorstatic##void#object, string, Exception</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Error</ItemName><ItemPath>ET.ETLogManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:25:38.1897795+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLogManager.Warningstatic##void#string, Exception</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Warning</ItemName><ItemPath>ET.ETLogManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:25:35.0089455+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLogManager.Debugstatic##void#object, string, Exception</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Debug</ItemName><ItemPath>ET.ETLogManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:22:19.3602737+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLogManager.Infostatic##void#object, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Info</ItemName><ItemPath>ET.ETLogManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:22:14.8532448+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLogManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLogManager</ItemName><ItemPath>ET</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName><TimeStamp>2025-07-24T22:22:01.7978056+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETLogDisplayControl</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETLogDisplayControl</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:52:03.7029453+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETLicenseGenerator.LoadFromOssAsync#Task&lt;ETLicenseInfo&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadFromOssAsync</ItemName><ItemPath>ET.ETLicense.ETLicenseGenerator</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName><TimeStamp>2025-06-20T22:52:36.0511099+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIResultFiller.ConvertCellValue#object#object</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ConvertCellValue</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIResultFiller</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName><TimeStamp>2025-06-22T15:37:39.1087788+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIProcessingManager.ProcessFilesAsync#Task#List&lt;AIDataGroup&gt;, AIDataSourceConfig, CancellationToken</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ProcessFilesAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIProcessingManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:46:30.5779555+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Services.Core.AIProcessingManager.ProcessAsync#Task&lt;AIResponse&gt;#AIDataSourceConfig, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ProcessAsync</ItemName><ItemPath>ExtensionsTools.ETAIv2.Services.Core.AIProcessingManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName><TimeStamp>2025-06-22T16:12:22.2437582+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETLicenseGeneratorForm.WriteFileSecurelystatic##void#string, byte[]</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WriteFileSecurely</ItemName><ItemPath>ET.ETLicense.ETLicenseGeneratorForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName><TimeStamp>2025-06-29T12:56:37.2072202+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Models.AIModelConfig.GetEffectiveBaseUrl#string#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetEffectiveBaseUrl</ItemName><ItemPath>ExtensionsTools.ETAIv2.Models.AIModelConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName><TimeStamp>2025-06-22T21:02:04.8789717+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ExtensionsTools.ETAIv2.Models.AIModelConfig.GetEffectiveApiKey#string#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetEffectiveApiKey</ItemName><ItemPath>ExtensionsTools.ETAIv2.Models.AIModelConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName><TimeStamp>2025-06-22T20:50:28.2290097+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.ETLicense.ETThreadSafeCallbackHelper</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETThreadSafeCallbackHelper</ItemName><ItemPath>ET.ETLicense</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName><TimeStamp>2025-06-27T08:28:35.5062428+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.ETLicense.ETThreadSafeCallbackHelper.SafeInvokeNetworkLicenseUpdatedstatic##void#NetworkLicenseUpdatedEventHandler, ETLicenseInfo, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SafeInvokeNetworkLicenseUpdated</ItemName><ItemPath>ET.ETLicense.ETThreadSafeCallbackHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName><TimeStamp>2025-06-26T23:47:51.3065721+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>ET.Controls.ETAutoResetLabel</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ETAutoResetLabel</ItemName><ItemPath>ET.Controls</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:52:59.7737236+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>ET.Controls.ETAutoResetLabel._resetTimer</ID><ImageSource>img\tvi\x_var-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>_resetTimer</ItemName><ItemPath>ET.Controls.ETAutoResetLabel</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName><TimeStamp>2025-07-22T23:52:59.7724456+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>Common.Utility.ValidatorHelper.IsChinesestatic##bool#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>IsChinese</ItemName><ItemPath>Common.Utility.ValidatorHelper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\Common.Utility\ValidatorHelper.cs</ProjectItemFileName><TimeStamp>2025-07-27T14:21:26.4040828+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>ExtensionsTools</ProjectName></ProjectData>