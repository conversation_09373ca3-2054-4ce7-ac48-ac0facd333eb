using System.Collections.Generic;

namespace ExtensionsTools.ETAIv2.Constants
{
    /// <summary>
    /// AI相关常量定义
    /// </summary>
    public static class AIConstants
    {
        /// <summary>
        /// 支持的文件扩展名
        /// </summary>
        public static readonly HashSet<string> SupportedFileExtensions = new HashSet<string>
        {
            ".pdf", ".txt", ".docx", ".doc", ".rtf", ".md", 
            ".html", ".json", ".csv", ".xml", ".xlsx", ".xls"
        };

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public const long MaxFileSize = 1024 * 1024; // 1MB

        /// <summary>
        /// 默认请求超时时间（秒）
        /// </summary>
        public const int DefaultRequestTimeout = 30;

        /// <summary>
        /// 默认最大并发请求数
        /// </summary>
        public const int DefaultMaxConcurrentRequests = 3;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        public const int DefaultMaxRetries = 3;

        /// <summary>
        /// 默认组大小
        /// </summary>
        public const int DefaultGroupSize = 10;

        /// <summary>
        /// OpenAI模型名称
        /// </summary>
        public static class Models
        {
            public const string GPT4O = "gpt-4o";
            public const string GPT4OMini = "gpt-4o-mini";
            public const string GPT35Turbo = "gpt-3.5-turbo";
            public const string TextEmbedding3Small = "text-embedding-3-small";
            public const string TextEmbedding3Large = "text-embedding-3-large";
        }

        /// <summary>
        /// API端点
        /// </summary>
        public static class Endpoints
        {
            public const string OpenAIBase = "https://api.openai.com/v1/";
            public const string ChatCompletions = "chat/completions";
            public const string Assistants = "assistants";
            public const string Files = "files";
            public const string VectorStores = "vector_stores";
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public static class ErrorMessages
        {
            public const string InvalidConfiguration = "AI模型配置无效";
            public const string FileNotSupported = "不支持的文件类型";
            public const string FileTooLarge = "文件大小超过限制";
            public const string NetworkError = "网络连接错误";
            public const string APIKeyMissing = "API密钥缺失";
            public const string RequestTimeout = "请求超时";
            public const string InvalidResponse = "AI响应格式无效";
            public const string ExcelRangeInvalid = "Excel区域无效";
        }

        /// <summary>
        /// JSON Schema模板
        /// </summary>
        public static class JsonSchemas
        {
            public const string ExcelAnalysisResult = @"{
                ""type"": ""object"",
                ""properties"": {
                    ""results"": {
                        ""type"": ""array"",
                        ""items"": {
                            ""type"": ""object"",
                            ""properties"": {
                                ""groupId"": {""type"": ""string""},
                                ""values"": {
                                    ""type"": ""object"",
                                    ""additionalProperties"": true
                                },
                                ""processingInfo"": {""type"": ""string""},
                                ""confidence"": {
                                    ""type"": ""number"",
                                    ""minimum"": 0,
                                    ""maximum"": 1
                                }
                            },
                            ""required"": [""groupId"", ""values""]
                        }
                    }
                },
                ""required"": [""results""]
            }";
        }

        /// <summary>
        /// 系统提示词模板
        /// </summary>
        public static class SystemPrompts
        {
            public const string DefaultExcelAnalysis = @"你是一个专业的Excel数据分析助手。
请根据提供的数据和文件进行分析，并返回JSON格式的结果。
结果格式应该包含每个数据组的分析结果，按照指定的列要求进行处理。
确保返回的JSON格式严格符合指定的schema。";

            public const string FileAnalysis = @"你是一个文档分析专家。
请仔细分析提供的文档内容，提取关键信息。
根据用户的具体要求，提供准确、有用的分析结果。";
        }

        /// <summary>
        /// 配置文件节名
        /// </summary>
        public static class ConfigSections
        {
            public const string Server = "server";
            public const string Model = "model";
            public const string BaseURL = "BaseURL";
            public const string APIKey = "APIKey";
            public const string ProxyHost = "proxy_host";
            public const string ProxyPort = "proxy_port";
            public const string RequestTimeout = "request_timeout";
            public const string TopP = "top_p";
            public const string Temperature = "temperature";
            public const string ResponseFormatType = "response_format_type";
            public const string BaseGroupSize = "base_group_size";
            public const string MaxJsonRetryCount = "max_json_retry_count";
            public const string MaxConcurrentRequests = "max_concurrent_requests";
            public const string MaxRequestsPerMinute = "max_requests_per_minute";
            public const string TotalMaxRequests = "total_max_requests";
            public const string SystemContent = "system_content";
        }
    }

    /// <summary>
    /// Excel相关常量
    /// </summary>
    public static class ExcelConstants
    {
        /// <summary>
        /// 最大行数
        /// </summary>
        public const int MaxRows = 1048576;

        /// <summary>
        /// 最大列数
        /// </summary>
        public const int MaxColumns = 16384;

        /// <summary>
        /// 默认处理的最大行数
        /// </summary>
        public const int DefaultMaxProcessRows = 1000;

        /// <summary>
        /// 单元格数据类型
        /// </summary>
        public static class DataTypes
        {
            public const string Text = "text";
            public const string Number = "number";
            public const string Date = "date";
            public const string Boolean = "boolean";
            public const string Formula = "formula";
            public const string File = "file";
        }
    }

    /// <summary>
    /// 文件处理相关常量
    /// </summary>
    public static class FileConstants
    {
        /// <summary>
        /// 文件上传目的
        /// </summary>
        public static class UploadPurpose
        {
            public const string Assistants = "assistants";
            public const string FineTune = "fine-tune";
            public const string Batch = "batch";
        }

        /// <summary>
        /// 文件状态
        /// </summary>
        public static class FileStatus
        {
            public const string Uploaded = "uploaded";
            public const string Processed = "processed";
            public const string Error = "error";
            public const string Deleted = "deleted";
        }

        /// <summary>
        /// MIME类型映射
        /// </summary>
        public static readonly Dictionary<string, string> MimeTypes = new Dictionary<string, string>
        {
            { ".pdf", "application/pdf" },
            { ".txt", "text/plain" },
            { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            { ".doc", "application/msword" },
            { ".rtf", "application/rtf" },
            { ".md", "text/markdown" },
            { ".html", "text/html" },
            { ".json", "application/json" },
            { ".csv", "text/csv" },
            { ".xml", "application/xml" },
            { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            { ".xls", "application/vnd.ms-excel" }
        };
    }
}
