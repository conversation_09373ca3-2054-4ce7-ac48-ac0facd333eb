using System;
using System.Windows.Forms;
using ET;
using ET.Controls;
using ExtensionsTools;
using HyExcelVsto.Module.WX.AngleExtractor;

namespace HyExcelVsto.Module.WX.AngleExtractor
{
    /// <summary>
    /// 方向角/下倾角提取器独立窗体 提供方向角和下倾角提取功能
    /// </summary>
    public partial class AngleExtractorForm : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public AngleExtractorForm()
        {
            InitializeComponent();
            InitializeAngleExtractor();

            ETLogManager.Info(this, "AngleExtractorForm初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化方向角提取器
        /// </summary>
        private void InitializeAngleExtractor()
        {
            try
            {
                // 设置Excel应用程序提供者
                etRangeSelectAngleSource.SetExcelApplicationProvider(
                    new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));
                etRangeSelectAzimuthOutput.SetExcelApplicationProvider(
                    new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));
                etRangeSelectTiltOutput.SetExcelApplicationProvider(
                    new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));

                // 初始化日志控件
                etLogDisplayControlAngle.WriteInfo("方向角/下倾角提取器已就绪");
                etLogDisplayControlAngle.WriteInfo(AngleExtractorHelper.GetFunctionDescription());

                ETLogManager.Info(this, "方向角提取器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化方向角提取器失败", ex);
            }
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 提取方向角和下倾角按钮点击事件
        /// </summary>
        private void BtnExtractAngles_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (etRangeSelectAngleSource.SelectedRange == null)
                {
                    MessageBox.Show("请选择包含原始数据的来源范围", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    etRangeSelectAngleSource.Focus();
                    return;
                }

                if (etRangeSelectAzimuthOutput.SelectedRange == null && etRangeSelectTiltOutput.SelectedRange == null)
                {
                    MessageBox.Show("请至少选择一个输出范围（方向角或下倾角）", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    etRangeSelectAzimuthOutput.Focus();
                    return;
                }

                // 禁用按钮，显示进度
                btnExtractAngles.Enabled = false;
                btnExtractAngles.Text = "提取中...";
                etLogDisplayControlAngle.WriteInfo($"[{DateTime.Now:HH:mm:ss}] 开始提取方向角和下倾角...");
                System.Windows.Forms.Application.DoEvents();

                // 创建提取参数
                var parameters = new AngleExtractorHelper.ExtractionParameters
                {
                    SourceRange = etRangeSelectAngleSource.SelectedRange.OptimizeRangeSize(),
                    AzimuthOutputRange = etRangeSelectAzimuthOutput.SelectedRange,
                    TiltAngleOutputRange = etRangeSelectTiltOutput.SelectedRange
                };

                // 执行提取
                var result = AngleExtractorHelper.ExtractAngles(parameters);

                if (result.Success)
                {
                    etLogDisplayControlAngle.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ✅ {result.Message}");

                    MessageBox.Show(
                        $"提取完成！\n\n" +
                        $"处理行数：{result.ProcessedRows}\n" +
                        $"提取方向角：{result.AzimuthCount} 个\n" +
                        $"提取下倾角：{result.TiltAngleCount} 个",
                        "提取成功",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    etLogDisplayControlAngle.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 提取失败：{result.ErrorMessage}");
                    MessageBox.Show($"提取失败：{result.ErrorMessage}", "提取失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                etLogDisplayControlAngle.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 提取异常：{ex.Message}");
                MessageBox.Show($"提取时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error(this, $"提取方向角和下倾角异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnExtractAngles.Enabled = true;
                btnExtractAngles.Text = "提取";
            }
        }

        #endregion 事件处理

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "AngleExtractorForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}