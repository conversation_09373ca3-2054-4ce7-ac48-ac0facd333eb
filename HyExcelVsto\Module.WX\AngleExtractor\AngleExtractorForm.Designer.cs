using System;
using System.Drawing;
using System.Windows.Forms;
using ET.Controls;

namespace HyExcelVsto.Module.WX.AngleExtractor
{
    partial class AngleExtractorForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
                ReleaseCustomResources();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lblAngleExtractorDescription = new System.Windows.Forms.Label();
            this.lblAngleSourceRange = new System.Windows.Forms.Label();
            this.etRangeSelectAngleSource = new ET.Controls.ETRangeSelectControl();
            this.lblAngleAzimuthOutput = new System.Windows.Forms.Label();
            this.etRangeSelectAzimuthOutput = new ET.Controls.ETRangeSelectControl();
            this.lblAngleTiltOutput = new System.Windows.Forms.Label();
            this.etRangeSelectTiltOutput = new ET.Controls.ETRangeSelectControl();
            this.btnExtractAngles = new System.Windows.Forms.Button();
            this.etLogDisplayControlAngle = new ET.Controls.ETLogDisplayControl();
            this.SuspendLayout();
            // 
            // lblAngleExtractorDescription
            // 
            this.lblAngleExtractorDescription.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblAngleExtractorDescription.Location = new System.Drawing.Point(20, 15);
            this.lblAngleExtractorDescription.Name = "lblAngleExtractorDescription";
            this.lblAngleExtractorDescription.Size = new System.Drawing.Size(550, 40);
            this.lblAngleExtractorDescription.TabIndex = 0;
            this.lblAngleExtractorDescription.Text = "本功能：用于提取方向角和下倾角，并填写到指定列\r\n注意：只有1小区的数据无法识别，该功能目前有Bug，不是很准确";
            // 
            // lblAngleSourceRange
            // 
            this.lblAngleSourceRange.AutoSize = true;
            this.lblAngleSourceRange.Location = new System.Drawing.Point(20, 70);
            this.lblAngleSourceRange.Name = "lblAngleSourceRange";
            this.lblAngleSourceRange.Size = new System.Drawing.Size(35, 12);
            this.lblAngleSourceRange.TabIndex = 1;
            this.lblAngleSourceRange.Text = "来源:";
            // 
            // etRangeSelectAngleSource
            // 
            this.etRangeSelectAngleSource.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etRangeSelectAngleSource.EnableEnterThenSelect = false;
            this.etRangeSelectAngleSource.HideParentForm = true;
            this.etRangeSelectAngleSource.InputPromptText = "请选择包含原始数据的范围：";
            this.etRangeSelectAngleSource.Location = new System.Drawing.Point(100, 66);
            this.etRangeSelectAngleSource.Margin = new System.Windows.Forms.Padding(2);
            this.etRangeSelectAngleSource.Name = "etRangeSelectAngleSource";
            this.etRangeSelectAngleSource.SelectedRange = null;
            this.etRangeSelectAngleSource.Size = new System.Drawing.Size(470, 25);
            this.etRangeSelectAngleSource.TabIndex = 2;
            // 
            // lblAngleAzimuthOutput
            // 
            this.lblAngleAzimuthOutput.AutoSize = true;
            this.lblAngleAzimuthOutput.Location = new System.Drawing.Point(20, 105);
            this.lblAngleAzimuthOutput.Name = "lblAngleAzimuthOutput";
            this.lblAngleAzimuthOutput.Size = new System.Drawing.Size(71, 12);
            this.lblAngleAzimuthOutput.TabIndex = 3;
            this.lblAngleAzimuthOutput.Text = "输出方向角:";
            // 
            // etRangeSelectAzimuthOutput
            // 
            this.etRangeSelectAzimuthOutput.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etRangeSelectAzimuthOutput.EnableEnterThenSelect = false;
            this.etRangeSelectAzimuthOutput.HideParentForm = true;
            this.etRangeSelectAzimuthOutput.InputPromptText = "请选择方向角输出列：";
            this.etRangeSelectAzimuthOutput.Location = new System.Drawing.Point(100, 101);
            this.etRangeSelectAzimuthOutput.Margin = new System.Windows.Forms.Padding(2);
            this.etRangeSelectAzimuthOutput.Name = "etRangeSelectAzimuthOutput";
            this.etRangeSelectAzimuthOutput.SelectedRange = null;
            this.etRangeSelectAzimuthOutput.Size = new System.Drawing.Size(470, 25);
            this.etRangeSelectAzimuthOutput.TabIndex = 4;
            // 
            // lblAngleTiltOutput
            // 
            this.lblAngleTiltOutput.AutoSize = true;
            this.lblAngleTiltOutput.Location = new System.Drawing.Point(20, 140);
            this.lblAngleTiltOutput.Name = "lblAngleTiltOutput";
            this.lblAngleTiltOutput.Size = new System.Drawing.Size(71, 12);
            this.lblAngleTiltOutput.TabIndex = 5;
            this.lblAngleTiltOutput.Text = "输出下倾角:";
            // 
            // etRangeSelectTiltOutput
            // 
            this.etRangeSelectTiltOutput.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etRangeSelectTiltOutput.EnableEnterThenSelect = false;
            this.etRangeSelectTiltOutput.HideParentForm = true;
            this.etRangeSelectTiltOutput.InputPromptText = "请选择下倾角输出列：";
            this.etRangeSelectTiltOutput.Location = new System.Drawing.Point(100, 136);
            this.etRangeSelectTiltOutput.Margin = new System.Windows.Forms.Padding(2);
            this.etRangeSelectTiltOutput.Name = "etRangeSelectTiltOutput";
            this.etRangeSelectTiltOutput.SelectedRange = null;
            this.etRangeSelectTiltOutput.Size = new System.Drawing.Size(470, 25);
            this.etRangeSelectTiltOutput.TabIndex = 6;
            // 
            // btnExtractAngles
            // 
            this.btnExtractAngles.Location = new System.Drawing.Point(20, 175);
            this.btnExtractAngles.Name = "btnExtractAngles";
            this.btnExtractAngles.Size = new System.Drawing.Size(120, 30);
            this.btnExtractAngles.TabIndex = 7;
            this.btnExtractAngles.Text = "提取";
            this.btnExtractAngles.UseVisualStyleBackColor = true;
            this.btnExtractAngles.Click += new System.EventHandler(this.BtnExtractAngles_Click);
            // 
            // etLogDisplayControlAngle
            // 
            this.etLogDisplayControlAngle.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etLogDisplayControlAngle.AutoScrollToBottom = true;
            this.etLogDisplayControlAngle.CurrentLogLevel = ET.Controls.ETLogDisplayControl.LogLevel.Info;
            this.etLogDisplayControlAngle.CustomInitialMessage = null;
            this.etLogDisplayControlAngle.Location = new System.Drawing.Point(20, 215);
            this.etLogDisplayControlAngle.LogBackColor = System.Drawing.Color.White;
            this.etLogDisplayControlAngle.LogFont = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.etLogDisplayControlAngle.LogForeColor = System.Drawing.Color.Black;
            this.etLogDisplayControlAngle.Margin = new System.Windows.Forms.Padding(2);
            this.etLogDisplayControlAngle.MaxLogLines = 1000;
            this.etLogDisplayControlAngle.Name = "etLogDisplayControlAngle";
            this.etLogDisplayControlAngle.ShowInitialMessage = true;
            this.etLogDisplayControlAngle.ShowLogLevel = true;
            this.etLogDisplayControlAngle.ShowTimestamp = true;
            this.etLogDisplayControlAngle.Size = new System.Drawing.Size(550, 245);
            this.etLogDisplayControlAngle.TabIndex = 8;
            // 
            // AngleExtractorForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 472);
            this.Controls.Add(this.lblAngleExtractorDescription);
            this.Controls.Add(this.lblAngleSourceRange);
            this.Controls.Add(this.etRangeSelectAngleSource);
            this.Controls.Add(this.lblAngleAzimuthOutput);
            this.Controls.Add(this.etRangeSelectAzimuthOutput);
            this.Controls.Add(this.lblAngleTiltOutput);
            this.Controls.Add(this.etRangeSelectTiltOutput);
            this.Controls.Add(this.btnExtractAngles);
            this.Controls.Add(this.etLogDisplayControlAngle);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MinimumSize = new System.Drawing.Size(600, 510);
            this.Name = "AngleExtractorForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "方向角/下倾角提取器";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Label lblAngleExtractorDescription;
        private Label lblAngleSourceRange;
        private Label lblAngleAzimuthOutput;
        private Label lblAngleTiltOutput;
        private ET.Controls.ETRangeSelectControl etRangeSelectAngleSource;
        private ET.Controls.ETRangeSelectControl etRangeSelectAzimuthOutput;
        private ET.Controls.ETRangeSelectControl etRangeSelectTiltOutput;
        private Button btnExtractAngles;
        private ET.Controls.ETLogDisplayControl etLogDisplayControlAngle;
    }
}
