using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Office.Interop.Excel;
using ExtensionsTools.ETAIv2.Models;
using ExtensionsTools.ETAIv2.Interfaces;
using ExtensionsTools.ETAIv2.Constants;
using ExtensionsTools.ETAIv2.Exceptions;
using ExtensionsTools.ETAIv2.Utils;

namespace ExtensionsTools.ETAIv2.Services.Core
{
    /// <summary>
    /// AI结果回填器
    /// </summary>
    public class AIResultFiller : IAIResultFiller
    {
        private readonly IAILogger _logger;

        public AIResultFiller(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
        }

        /// <summary>
        /// 将AI结果回填到Excel
        /// </summary>
        public async Task FillResultsAsync(AIResponse response, AIDataSourceConfig config)
        {
            try
            {
                _logger.LogInfo($"开始回填AI结果，请求ID: {response.RequestId}");

                if (response == null)
                    throw new ArgumentNullException(nameof(response));

                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                if (!response.Success)
                {
                    _logger.LogWarning($"AI响应失败，跳过回填: {response.ErrorMessage}");
                    return;
                }

                if (response.Results == null || !response.Results.Any())
                {
                    _logger.LogWarning("AI响应结果为空，跳过回填");
                    return;
                }

                // 验证回填数据的有效性
                if (!ValidateResults(response, config))
                {
                    throw new DataValidationException("AI响应结果验证失败");
                }

                var targetRange = config.TargetRange;
                if (targetRange == null)
                {
                    _logger.LogWarning("目标区域为空，跳过回填");
                    return;
                }

                // 执行回填操作
                await PerformFillOperationAsync(response, config);

                _logger.LogExcelOperation("回填AI结果", targetRange.Address,
                    targetRange.Rows.Count, targetRange.Columns.Count, true);

                _logger.LogInfo($"AI结果回填完成，请求ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"AI结果回填失败，请求ID: {response?.RequestId}", ex);
                throw new ExcelIntegrationException("结果回填失败", ex);
            }
        }

        /// <summary>
        /// 验证回填数据的有效性
        /// </summary>
        public bool ValidateResults(AIResponse response, AIDataSourceConfig config)
        {
            try
            {
                if (response?.Results == null || !response.Results.Any())
                {
                    _logger.LogWarning("响应结果为空");
                    return false;
                }

                if (config?.TargetRange == null)
                {
                    _logger.LogWarning("目标区域为空");
                    return false;
                }

                // 验证每个结果组
                foreach (var result in response.Results)
                {
                    if (string.IsNullOrEmpty(result.GroupId))
                    {
                        _logger.LogWarning("结果组ID为空");
                        return false;
                    }

                    if (result.Values == null)
                    {
                        _logger.LogWarning($"结果组 {result.GroupId} 的值为空");
                        return false;
                    }

                    // 验证单元格地址格式
                    foreach (var kvp in result.Values)
                    {
                        if (!IsValidCellAddress(kvp.Key))
                        {
                            _logger.LogWarning($"无效的单元格地址: {kvp.Key}");
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("验证回填数据时发生错误", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行回填操作
        /// </summary>
        private async Task PerformFillOperationAsync(AIResponse response, AIDataSourceConfig config)
        {
            try
            {
                var targetRange = config.TargetRange;
                var worksheet = targetRange.Worksheet;

                // 收集所有需要回填的数据
                var fillData = new Dictionary<string, object>();

                foreach (var result in response.Results)
                {
                    foreach (var kvp in result.Values)
                    {
                        var cellAddress = kvp.Key;
                        var cellValue = kvp.Value;

                        // 根据配置决定是否回填null值
                        if (cellValue == null && !config.FillNullValues)
                        {
                            continue;
                        }

                        // 转换和验证数据类型
                        var convertedValue = ConvertCellValue(cellValue);
                        fillData[cellAddress] = convertedValue;
                    }
                }

                if (!fillData.Any())
                {
                    _logger.LogInfo("没有需要回填的数据");
                    return;
                }

                // 批量回填数据以提高性能
                await BatchFillCellsAsync(worksheet, fillData);

                _logger.LogInfo($"成功回填 {fillData.Count} 个单元格");
            }
            catch (Exception ex)
            {
                throw new ExcelIntegrationException("执行回填操作失败", ex);
            }
        }

        /// <summary>
        /// 批量回填单元格
        /// </summary>
        private async Task BatchFillCellsAsync(Worksheet worksheet, Dictionary<string, object> fillData)
        {
            try
            {
                // 按行分组以优化性能
                var rowGroups = fillData.GroupBy(kvp => GetRowFromAddress(kvp.Key))
                    .OrderBy(g => g.Key);

                foreach (var rowGroup in rowGroups)
                {
                    var rowData = rowGroup.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await FillRowDataAsync(worksheet, rowData);

                    // 添加小延迟以避免Excel COM对象过载
                    await Task.Delay(10);
                }
            }
            catch (Exception ex)
            {
                throw new ExcelIntegrationException("批量回填单元格失败", ex);
            }
        }

        /// <summary>
        /// 回填行数据
        /// </summary>
        private async Task FillRowDataAsync(Worksheet worksheet, Dictionary<string, object> rowData)
        {
            try
            {
                foreach (var kvp in rowData)
                {
                    var cellAddress = kvp.Key;
                    var cellValue = kvp.Value;

                    try
                    {
                        var range = worksheet.Range[cellAddress];
                        range.Value2 = cellValue;

                        // 根据数据类型设置格式
                        SetCellFormat(range, cellValue);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"回填单元格 {cellAddress} 失败: {ex.Message}");
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                throw new ExcelIntegrationException("回填行数据失败", ex);
            }
        }

        /// <summary>
        /// 转换单元格值
        /// </summary>
        private object ConvertCellValue(object value)
        {
            if (value == null)
                return null;

            // 处理字符串
            if (value is string stringValue)
            {
                // 尝试转换为数字
                if (double.TryParse(stringValue, out double doubleResult))
                {
                    return doubleResult;
                }

                // 尝试转换为日期[这里不能转换日期，因为日期格式4/4/4这种格式会被识别为日期]
                //if (DateTime.TryParse(stringValue, out DateTime dateResult))
                //{
                //    return dateResult;
                //}

                // 尝试转换为布尔值
                if (bool.TryParse(stringValue, out bool boolResult))
                {
                    return boolResult;
                }

                return stringValue;
            }

            // 处理数字类型
            if (value is int || value is long || value is float || value is double || value is decimal)
            {
                return Convert.ToDouble(value);
            }

            // 处理日期类型
            if (value is DateTime)
            {
                return value;
            }

            // 处理布尔类型
            if (value is bool)
            {
                return value;
            }

            // 默认转换为字符串
            return value.ToString();
        }

        /// <summary>
        /// 设置单元格格式
        /// </summary>
        private void SetCellFormat(Range range, object value)
        {
            try
            {
                if (value == null)
                    return;

                if (value is double || value is float || value is decimal)
                {
                    // 检查是否为整数
                    var doubleValue = Convert.ToDouble(value);
                    if (doubleValue == Math.Floor(doubleValue))
                    {
                        range.NumberFormat = "0";
                    }
                    else
                    {
                        range.NumberFormat = "0.00";
                    }
                }
                else if (value is bool)
                {
                    range.NumberFormat = "General";
                }
                else
                {
                    range.NumberFormat = "@"; // 文本格式
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"设置单元格格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证单元格地址格式
        /// </summary>
        private bool IsValidCellAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return false;

            try
            {
                // 简单的正则验证：字母+数字格式
                return System.Text.RegularExpressions.Regex.IsMatch(address, @"^[A-Z]+\d+$");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从单元格地址获取行号
        /// </summary>
        private int GetRowFromAddress(string address)
        {
            try
            {
                var match = System.Text.RegularExpressions.Regex.Match(address, @"\d+");
                return match.Success ? int.Parse(match.Value) : 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 从单元格地址获取列号
        /// </summary>
        private string GetColumnFromAddress(string address)
        {
            try
            {
                var match = System.Text.RegularExpressions.Regex.Match(address, @"[A-Z]+");
                return match.Success ? match.Value : "";
            }
            catch
            {
                return "";
            }
        }
    }
}