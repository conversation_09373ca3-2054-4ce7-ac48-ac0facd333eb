using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Linq;
using ET.AI.Models;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json.Linq;

namespace ET.AI.Services.Core
{
    /// <summary>
    /// Excel处理器，负责处理Excel相关操作
    /// </summary>
    /// <remarks>
    /// 该类主要负责：
    /// 1. 从Excel中读取问题和已知信息
    /// 2. 将AI响应结果写回Excel
    /// 3. 处理Excel的行列操作和数据格式化
    /// 4. 处理筛选和隐藏行列的逻辑
    /// </remarks>
    class AIExcelHandler
    {
        /// <summary>
        /// Excel更新操作的同步锁
        /// </summary>
        /// <remarks>
        /// 用于确保Excel的更新操作是线程安全的，防止多线程同时修改Excel导致的问题
        /// </remarks>
        static readonly object _excelUpdateLock = new object();

        /// <summary>
        /// 将AI响应结果填充到Excel中
        /// </summary>
        /// <param name="questionRange">问题所在的单元格区域</param>
        /// <param name="knownInfoRange">已知信息所在的单元格区域</param>
        /// <param name="response">AI的响应结果（JSON格式）</param>
        /// <param name="isQuestionRow">是否按行填充问题</param>
        /// <param name="fillOption">填充选项，控制是否跳过空值</param>
        /// <returns>填充是否成功</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 关闭Excel屏幕刷新以提高性能
        /// 2. 解析JSON响应并按格式填充到对应单元格
        /// 3. 根据fillOption决定是否跳过空值
        /// 4. 处理可能的异常并恢复Excel屏幕刷新
        /// </remarks>
        public bool FillResponseToExcel(
            Range questionRange,
            Range knownInfoRange,
            JObject response,
            bool isQuestionRow,
            ExcelFillOption fillOption = ExcelFillOption.FillAll)
        {
            if (response == null)
                return false;

            try
            {
                lock (_excelUpdateLock)
                {
                    Worksheet worksheet = questionRange.Worksheet;
                    Application application = worksheet.Application;

                    // 开始批量更新前关闭屏幕刷新
                    application.ScreenUpdating = false;

                    try
                    {
                        JArray list = response["list"] as JArray;
                        if (list == null)
                            return false;

                        foreach (JObject group in list)
                        {
                            int listIndex = group["listIndex"]?.Value<int>() ?? -1;
                            JArray questions = group["question"] as JArray;

                            if (questions != null)
                            {
                                foreach (JObject question in questions)
                                {
                                    int questionIndex = question["questionIndex"]?.Value<int>() ?? -1;
                                    string answer = question["answer"]?.Value<string>();

                                    if (listIndex >= 0 && questionIndex >= 0)
                                    {
                                        if (fillOption == ExcelFillOption.SkipNull && string.IsNullOrWhiteSpace(answer))
                                            continue;

                                        if (isQuestionRow)
                                        {
                                            worksheet.Cells[listIndex, questionIndex].Value = answer;
                                        }
                                        else
                                        {
                                            worksheet.Cells[questionIndex, listIndex].Value = answer;
                                        }
                                    }
                                }
                            }
                        }

                        return true;
                    }
                    finally
                    {
                        // 完成一组更新后，打开屏幕刷新
                        application.ScreenUpdating = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error filling response to Excel: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从Excel范围中获取问题组列表
        /// </summary>
        /// <param name="questionRange">问题所在的单元格区域</param>
        /// <param name="knownInfoRange1">已知信息1所在的单元格区域</param>
        /// <param name="knownInfoRange2">已知信息2所在的单元格区域（可选）</param>
        /// <param name="isQuestionRow">输出参数，指示问题是否按行排列</param>
        /// <returns>问题组列表</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 检测并处理筛选行
        /// 2. 处理多个区域（Areas）的情况
        /// 3. 跳过隐藏的行或列
        /// 4. 收集问题和已知信息
        /// 5. 自动处理异常并继续处理其他有效数据
        /// </remarks>
        public List<AIQuestionGroup> GetQuestionGroupsFromRange(
            Range questionRange,
            Range knownInfoRange1,
            Range knownInfoRange2,
            out bool isQuestionRow)
        {
            List<AIQuestionGroup> questionGroups = new List<AIQuestionGroup>();
            isQuestionRow = questionRange.Rows.Count == 1;

            try
            {
                // 获取筛选行
                Range filterRange = HHExcelExtensions.GetAutoFilterRow(knownInfoRange1.Worksheet);
                bool hasFilterRow = filterRange != null;
                int filterRowNum = hasFilterRow ? filterRange.Row : 0;

                // 获取所有Areas并验证
                List<Range> areas1 = knownInfoRange1.Areas.Cast<Range>().ToList();
                if (areas1.Count == 0)
                {
                    return questionGroups;
                }

                List<Range> areas2 = knownInfoRange2?.Areas.Cast<Range>().ToList();

                // 使用第一个Area的行或列作为基准
                Range baseRange = areas1[0];
                Range baseItems = isQuestionRow ? baseRange.Rows : baseRange.Columns;

                foreach (Range baseItem in baseItems)
                {
                    try
                    {
                        // 跳过筛选行和隐藏的行列
                        if (ShouldSkipItem(baseItem, isQuestionRow, hasFilterRow, filterRowNum))
                            continue;

                        // 创建新的问题组
                        AIQuestionGroup group = new AIQuestionGroup
                        {
                            ListIndex = isQuestionRow ? baseItem.Row : baseItem.Column
                        };

                        // 处理已知信息1
                        group.KnownInfo1 = GetKnownInfo1(
                            baseItem, areas1, baseRange, isQuestionRow,
                            hasFilterRow, filterRowNum);

                        // 处理已知信息2
                        if (areas2?.Count > 0)
                        {
                            group.KnownInfo2 = GetKnownInfo2(
                                baseItem, areas2, baseRange, isQuestionRow);
                        }

                        // 添加问题
                        AddQuestionsToGroup(group, questionRange, isQuestionRow);

                        // 只有当组中有问题时才添加到列表
                        if (group.Questions.Count > 0)
                        {
                            questionGroups.Add(group);
                        }
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但继续处理
            }

            return questionGroups;
        }

        /// <summary>
        /// 判断是否应该跳过当前行或列
        /// </summary>
        /// <param name="item">要检查的单元格区域</param>
        /// <param name="isQuestionRow">是否按行处理</param>
        /// <param name="hasFilterRow">是否存在筛选行</param>
        /// <param name="filterRowNum">筛选行的行号</param>
        /// <returns>是否应该跳过</returns>
        /// <remarks>
        /// 跳过的情况包括：
        /// 1. 筛选行
        /// 2. 高度或宽度为0的行列
        /// 3. 被隐藏的行列
        /// </remarks>
        bool ShouldSkipItem(Range item, bool isQuestionRow, bool hasFilterRow, int filterRowNum)
        {
            if (isQuestionRow && hasFilterRow && item.Row == filterRowNum)
                return true;

            if (isQuestionRow)
            {
                return (item.RowHeight == 0) || (bool)item.EntireRow.Hidden;
            }
            else
            {
                return (item.ColumnWidth == 0) || (bool)item.EntireColumn.Hidden;
            }
        }

        /// <summary>
        /// 获取已知信息1的内容
        /// </summary>
        /// <param name="baseItem">基准单元格区域</param>
        /// <param name="areas">要处理的区域列表</param>
        /// <param name="baseRange">基准区域</param>
        /// <param name="isQuestionRow">是否按行处理</param>
        /// <param name="hasFilterRow">是否存在筛选行</param>
        /// <param name="filterRowNum">筛选行的行号</param>
        /// <returns>格式化后的已知信息1文本</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 处理多个区域的内容
        /// 2. 根据行列模式选择不同的处理方式
        /// 3. 合并所有区域的内容
        /// </remarks>
        string GetKnownInfo1(Range baseItem, List<Range> areas, Range baseRange,
            bool isQuestionRow, bool hasFilterRow, int filterRowNum)
        {
            StringBuilder info1Builder = new StringBuilder();

            foreach (Range area in areas)
            {
                try
                {
                    if (isQuestionRow)
                    {
                        ProcessRowInfo1(baseItem, area, baseRange, areas[0],
                            hasFilterRow, filterRowNum, info1Builder);
                    }
                    else
                    {
                        ProcessColumnInfo1(baseItem, area, info1Builder);
                    }
                }
                catch
                {
                    continue;
                }
            }

            return info1Builder.ToString();
        }

        /// <summary>
        /// 处理按行模式的已知信息1
        /// </summary>
        /// <param name="baseItem">基准单元格</param>
        /// <param name="area">当前处理的区域</param>
        /// <param name="baseRange">基准区域</param>
        /// <param name="firstArea">第一个区域</param>
        /// <param name="hasFilterRow">是否存在筛选行</param>
        /// <param name="filterRowNum">筛选行的行号</param>
        /// <param name="info1Builder">用于构建结果的StringBuilder</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 检查目标行是否在当前区域内
        /// 2. 处理区域内的所有列
        /// 3. 根据是否有筛选行选择不同的处理方式
        /// </remarks>
        void ProcessRowInfo1(Range baseItem, Range area, Range baseRange, Range firstArea,
            bool hasFilterRow, int filterRowNum, StringBuilder info1Builder)
        {
            int targetRow = baseItem.Row;
            if (targetRow >= area.Row && targetRow <= area.Row + area.Rows.Count - 1)
            {
                Range rowInArea = area.Rows[targetRow - area.Row + 1];
                for (int col = 1; col <= rowInArea.Columns.Count; col++)
                {
                    Range cell = rowInArea.Cells[1, col];
                    AppendCellValue(cell, info1Builder);
                }
            }
        }

        /// <summary>
        /// 处理按列模式的已知信息1
        /// </summary>
        /// <param name="baseItem">基准单元格</param>
        /// <param name="area">当前处理的区域</param>
        /// <param name="info1Builder">用于构建结果的StringBuilder</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 检查目标列是否在当前区域内
        /// 2. 处理区域内的所有行
        /// 3. 直接追加单元格值
        /// </remarks>
        void ProcessColumnInfo1(Range baseItem, Range area, StringBuilder info1Builder)
        {
            int targetCol = baseItem.Column;
            if (targetCol >= area.Column && targetCol <= area.Column + area.Columns.Count - 1)
            {
                Range colInArea = area.Columns[targetCol - area.Column + 1];
                for (int row = 1; row <= colInArea.Rows.Count; row++)
                {
                    Range cell = colInArea.Cells[row, 1];
                    AppendCellValue(cell, info1Builder);
                }
            }
        }

        /// <summary>
        /// 追加单元格值
        /// </summary>
        /// <param name="cell">要处理的单元格</param>
        /// <param name="builder">用于构建结果的StringBuilder</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取单元格的文本值
        /// 2. 处理空值
        /// 3. 添加分隔符
        /// </remarks>
        void AppendCellValue(Range cell, StringBuilder builder)
        {
            try
            {
                string value = cell?.Text?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(value))
                {
                    if (builder.Length > 0)
                        builder.Append("，");
                    builder.Append(value);
                }
            }
            catch
            {
                // 忽略单个单元格的处理错误
            }
        }

        /// <summary>
        /// 获取已知信息2的内容
        /// </summary>
        /// <param name="baseItem">基准单元格</param>
        /// <param name="areas">要处理的区域列表</param>
        /// <param name="baseRange">基准区域</param>
        /// <param name="isQuestionRow">是否按行处理</param>
        /// <returns>格式化后的已知信息2文本</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 处理多个区域的内容
        /// 2. 根据行列模式选择不同的处理方式
        /// 3. 合并所有区域的内容
        /// 4. 处理可能的异常情况
        /// </remarks>
        string GetKnownInfo2(Range baseItem, List<Range> areas, Range baseRange, bool isQuestionRow)
        {
            StringBuilder info2Builder = new StringBuilder();

            foreach (Range area in areas)
            {
                try
                {
                    if (isQuestionRow)
                    {
                        int targetRow = baseItem.Row;
                        if (targetRow >= area.Row && targetRow <= area.Row + area.Rows.Count - 1)
                        {
                            Range rowInArea = area.Rows[targetRow - area.Row + 1];
                            for (int col = 1; col <= rowInArea.Columns.Count; col++)
                            {
                                AppendCellValue(rowInArea.Cells[1, col], info2Builder);
                            }
                        }
                    }
                    else
                    {
                        int targetCol = baseItem.Column;
                        if (targetCol >= area.Column && targetCol <= area.Column + area.Columns.Count - 1)
                        {
                            Range colInArea = area.Columns[targetCol - area.Column + 1];
                            for (int row = 1; row <= colInArea.Rows.Count; row++)
                            {
                                AppendCellValue(colInArea.Cells[row, 1], info2Builder);
                            }
                        }
                    }
                }
                catch
                {
                    continue;
                }
            }

            return info2Builder.ToString();
        }

        /// <summary>
        /// 向问题组添加问题
        /// </summary>
        /// <param name="group">要添加到的问题组</param>
        /// <param name="questionRange">问题区域</param>
        /// <param name="isQuestionRow">是否按行处理</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 根据行列模式确定问题的位置
        /// 2. 处理问题区域中的每个单元格
        /// 3. 跳过空白问题
        /// 4. 自动处理异常情况
        /// </remarks>
        void AddQuestionsToGroup(AIQuestionGroup group, Range questionRange, bool isQuestionRow)
        {
            try
            {
                Range items = isQuestionRow ? questionRange.Columns : questionRange.Rows;
                foreach (Range item in items)
                {
                    try
                    {
                        string question = item?.Text?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(question))
                        {
                            group.Questions.Add(new AIQuestionItem
                            {
                                QuestionIndex = isQuestionRow ? item.Column : item.Row,
                                Question = question
                            });
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
            }
            catch
            {
                // 忽略整个问题组的处理错误
            }
        }
    }
}