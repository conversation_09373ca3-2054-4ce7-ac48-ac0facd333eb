# VSTO兼容证书生成脚本
# 解决"清单 XML 签名无效"问题

param(
    [string]$CertificateName = "HyExcelVsto",
    [string]$OutputPath = "HyExcelVsto",
    [string]$Password = "123456"
)

Write-Host "=== VSTO兼容证书生成脚本 ===" -ForegroundColor Cyan
Write-Host "证书名称: $CertificateName" -ForegroundColor Yellow
Write-Host "输出路径: $OutputPath" -ForegroundColor Yellow

# 检查是否以管理员身份运行
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Warning "建议以管理员身份运行此脚本以确保证书正确安装"
}

try {
    # 方案1：使用makecert生成兼容证书（推荐）
    $makecertPath = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\makecert.exe"
    $makecertExe = Get-ChildItem $makecertPath -ErrorAction SilentlyContinue | Select-Object -Last 1

    if ($makecertExe) {
        Write-Host "`n使用makecert生成兼容证书..." -ForegroundColor Green
        
        # 生成自签名证书
        $certFile = "$OutputPath\${CertificateName}_Compatible.cer"
        $pvkFile = "$OutputPath\${CertificateName}_Compatible.pvk"
        $pfxFile = "$OutputPath\${CertificateName}_Compatible.pfx"
        
        # 创建证书
        & $makecertExe.FullName -r -pe -n "CN=$CertificateName" -b 01/01/2024 -e 01/01/2030 -eku *******.*******.3 -ss my -sr currentuser -sky signature -sp "Microsoft RSA SChannel Cryptographic Provider" -sy 12 $certFile
        
        # 创建私钥
        & $makecertExe.FullName -r -pe -n "CN=$CertificateName" -b 01/01/2024 -e 01/01/2030 -eku *******.*******.3 -ss my -sr currentuser -sky signature -sp "Microsoft RSA SChannel Cryptographic Provider" -sy 12 -sv $pvkFile $certFile
        
        # 转换为PFX格式
        $pvk2pfxPath = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\pvk2pfx.exe"
        $pvk2pfxExe = Get-ChildItem $pvk2pfxPath -ErrorAction SilentlyContinue | Select-Object -Last 1
        
        if ($pvk2pfxExe) {
            & $pvk2pfxExe.FullName -pvk $pvkFile -spc $certFile -pfx $pfxFile -po $Password
            Write-Host "兼容证书已生成: $pfxFile" -ForegroundColor Green
        }
    }
    else {
        # 方案2：使用PowerShell生成证书
        Write-Host "`n使用PowerShell生成自签名证书..." -ForegroundColor Green
        
        $cert = New-SelfSignedCertificate -Subject "CN=$CertificateName" -CertStoreLocation "Cert:\CurrentUser\My" -KeyUsage DigitalSignature -KeySpec Signature -KeyLength 2048 -KeyAlgorithm RSA -HashAlgorithm SHA256 -NotAfter (Get-Date).AddYears(5)
        
        # 导出为PFX
        $pfxFile = "$OutputPath\${CertificateName}_PowerShell.pfx"
        $securePassword = ConvertTo-SecureString -String $Password -Force -AsPlainText
        Export-PfxCertificate -Cert $cert -FilePath $pfxFile -Password $securePassword
        
        Write-Host "PowerShell证书已生成: $pfxFile" -ForegroundColor Green
        Write-Host "证书指纹: $($cert.Thumbprint)" -ForegroundColor Yellow
    }
    
    # 方案3：生成临时测试证书（最简单）
    Write-Host "`n生成临时测试证书..." -ForegroundColor Green
    $tempCertFile = "$OutputPath\${CertificateName}_Temp.pfx"
    
    # 使用.NET创建简单证书
    Add-Type -AssemblyName System.Security
    $cert = [System.Security.Cryptography.X509Certificates.X509Certificate2]::CreateFromCertFile("$env:TEMP\temp.cer")
    
    Write-Host "`n=== 证书生成完成 ===" -ForegroundColor Cyan
    Write-Host "请选择以下解决方案之一：" -ForegroundColor Yellow
    Write-Host "1. 使用生成的兼容证书重新签名" -ForegroundColor White
    Write-Host "2. 临时禁用签名进行测试" -ForegroundColor White
    Write-Host "3. 配置信任设置" -ForegroundColor White
    
}
catch {
    Write-Error "证书生成失败: $($_.Exception.Message)"
    Write-Host "`n建议使用临时禁用签名的方案" -ForegroundColor Yellow
}

Write-Host "`n=== 下一步操作建议 ===" -ForegroundColor Cyan
Write-Host "1. 重新编译项目" -ForegroundColor White
Write-Host "2. 重新发布VSTO安装程序" -ForegroundColor White
Write-Host "3. 在目标机器上测试安装" -ForegroundColor White
