# 查找makecert工具位置
Write-Host "=== 查找makecert工具 ===" -ForegroundColor Cyan

$possiblePaths = @(
    "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\makecert.exe",
    "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x86\makecert.exe",
    "${env:ProgramFiles(x86)}\Windows Kits\8.1\bin\x64\makecert.exe",
    "${env:ProgramFiles(x86)}\Windows Kits\8.1\bin\x86\makecert.exe",
    "${env:ProgramFiles(x86)}\Microsoft SDKs\Windows\*\bin\makecert.exe"
)

$foundPaths = @()
foreach ($path in $possiblePaths) {
    $found = Get-ChildItem $path -ErrorAction SilentlyContinue
    if ($found) {
        $foundPaths += $found
        Write-Host "找到: $($found.FullName)" -ForegroundColor Green
    }
}

if ($foundPaths.Count -eq 0) {
    Write-Host "未找到makecert工具" -ForegroundColor Red
    Write-Host "请安装Windows SDK: https://developer.microsoft.com/en-us/windows/downloads/windows-sdk/" -ForegroundColor Yellow
} else {
    Write-Host "`n推荐使用: $($foundPaths[0].FullName)" -ForegroundColor Green
    return $foundPaths[0].FullName
}
