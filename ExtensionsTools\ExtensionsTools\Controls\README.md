# ETExcel Controls

ExtensionsTools 库中的 Excel 相关控件集合。

## 控件列表

### ETRangeSelectControl

通用的 Excel 范围选择控件，支持多种 Excel 集成环境。

### ETLogDisplayControl

通用的日志显示控件，支持多级别日志记录和显示。

#### 主要特性

- ✅ **多环境支持**：支持 VSTO、COM 互操作等不同的 Excel 集成环境
- ✅ **事件驱动**：完整的事件机制，支持选择开始、完成等事件
- ✅ **程序化控制**：支持程序化设置范围，可控制是否触发事件
- ✅ **错误处理**：完善的异常处理和日志记录
- ✅ **用户友好**：支持父窗体隐藏、输入提示等用户体验优化
- ✅ **可扩展**：基于提供者模式，易于扩展到新的 Excel 集成环境

#### 快速开始

```csharp
using ET.Controls;

// 1. 创建控件
var rangeControl = new ETRangeSelectControl();

// 2. 设置 Excel 应用程序提供者（VSTO 环境）
rangeControl.SetExcelApplicationProvider(
    new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));

// 3. 绑定事件
rangeControl.SelectedEvent += (sender, e) => {
    var range = rangeControl.SelectedRange;
    if (range != null)
    {
        Console.WriteLine($"选择的范围：{range.Address}");
    }
};

// 4. 程序化设置范围
rangeControl.SetSelectedRangeWithEvent(someRange, true);
```

#### API 参考

##### 属性

- `SelectedRange` - 当前选中的 Excel Range 对象
- `FullSelectedAddress` - 选中范围的完整地址
- `Text` - 控件显示的文本
- `EnableEnterThenSelect` - 是否启用回车键触发选择
- `HideParentForm` - 是否在选择时隐藏父窗体
- `InputPromptText` - 输入提示文本

##### 方法

- `SetSelectedRangeWithEvent(Range, bool)` - 设置范围并可选择是否触发事件
- `SetExcelApplicationProvider(IExcelApplicationProvider)` - 设置 Excel 应用程序提供者

##### 事件

- `SelectedEvent` - 选择完成时触发
- `BeginSelectEvent` - 开始选择时触发
- `EnterEvent` - 文本框获得焦点时触发

#### Excel 应用程序提供者

控件使用提供者模式来适配不同的 Excel 集成环境：

##### DefaultExcelApplicationProvider
```csharp
// 通过 COM 互操作获取活动的 Excel 实例
var provider = new DefaultExcelApplicationProvider();
```

##### VSTOExcelApplicationProvider
```csharp
// VSTO 环境中使用
var provider = new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application);
```

##### 自定义提供者
```csharp
public class CustomProvider : IExcelApplicationProvider
{
    public Application GetExcelApplication()
    {
        // 自定义获取逻辑
        return myExcelApp;
    }
}
```

#### 使用场景

1. **VSTO 插件开发**
   - Excel 插件中的范围选择功能
   - 数据处理工具的输入界面

2. **独立应用程序**
   - 与 Excel 交互的桌面应用
   - 数据分析工具

3. **自动化工具**
   - Excel 自动化脚本的用户界面
   - 批处理工具的参数输入

#### 最佳实践

1. **正确设置提供者**
   ```csharp
   // 在 VSTO 环境中
   control.SetExcelApplicationProvider(
       new VSTOExcelApplicationProvider(() => (object)Globals.ThisAddIn.Application));

   // 在 COM 互操作环境中（使用默认提供者）
   // 无需额外设置，控件会自动使用 DefaultExcelApplicationProvider
   ```

2. **异常处理**
   ```csharp
   rangeControl.SelectedEvent += (sender, e) => {
       try
       {
           var range = rangeControl.SelectedRange;
           // 处理范围选择
       }
       catch (Exception ex)
       {
           // 处理异常
           MessageBox.Show($"处理范围时出错：{ex.Message}");
       }
   };
   ```

3. **资源管理**
   ```csharp
   // 控件会自动处理资源释放，但建议在窗体关闭时清理事件
   protected override void OnFormClosed(FormClosedEventArgs e)
   {
       rangeControl.SelectedEvent -= RangeControl_SelectedEvent;
       base.OnFormClosed(e);
   }
   ```

#### 故障排除

**问题：控件无法获取 Excel 实例**
- 确保已正确设置 Excel 应用程序提供者
- 检查 Excel 是否正在运行
- 验证 COM 互操作权限

**问题：事件不触发**
- 确保已正确绑定事件处理器
- 检查是否在 UI 线程中处理事件
- 验证 Excel 应用程序提供者是否正常工作

**问题：范围选择对话框不显示**
- 检查 Excel 应用程序是否可访问
- 确认父窗体隐藏设置是否正确
- 验证 Excel 窗口是否被其他窗口遮挡

#### 版本历史

- **v1.0** - 初始版本，从基站数据处理器模块迁移
- **v1.1** - 添加提供者模式支持
- **v1.2** - 增强事件触发控制功能

#### 许可证

本控件遵循项目的整体许可证协议。

#### 贡献

欢迎提交 Issue 和 Pull Request 来改进这个控件。

---

## ETLogDisplayControl 详细说明

### 主要特性

- ✅ **多级别日志** - 支持 Debug、Info、Warning、Error 四个级别
- ✅ **级别过滤** - 可设置最低显示级别，过滤低级别日志
- ✅ **自动管理** - 自动清理旧日志，防止内存溢出
- ✅ **线程安全** - 支持多线程环境下的安全UI更新
- ✅ **可自定义样式** - 支持自定义字体、颜色等外观
- ✅ **灵活初始化** - 可选择是否显示初始化消息

### 快速开始

```csharp
using ET.Controls;

// 1. 创建控件
var logControl = new ETLogDisplayControl();

// 2. 写入不同级别的日志
logControl.WriteInfo("这是信息日志");
logControl.WriteWarning("这是警告日志");
logControl.WriteError("这是错误日志", exception);
logControl.WriteDebug("这是调试日志");

// 3. 自定义样式
logControl.LogFont = new Font("Consolas", 10F);
logControl.LogBackColor = Color.Black;
logControl.LogForeColor = Color.Green;

// 4. 设置过滤级别
logControl.CurrentLogLevel = ETLogDisplayControl.LogLevel.Info;
```

### 构造函数选项

```csharp
// 默认构造函数（显示默认初始化消息）
var logControl = new ETLogDisplayControl();

// 不显示初始化消息
var logControl = new ETLogDisplayControl(false);

// 使用自定义初始化消息
var logControl = new ETLogDisplayControl("## 我的应用日志\n准备开始...");
```

### API 参考

#### 属性
- `CurrentLogLevel` - 当前日志级别过滤设置
- `MaxLogLines` - 最大日志行数限制
- `AutoScrollToBottom` - 是否自动滚动到底部
- `ShowTimestamp` - 是否显示时间戳
- `ShowLogLevel` - 是否显示日志级别
- `LogFont` - 日志文本框的字体
- `LogBackColor` - 日志文本框的背景色
- `LogForeColor` - 日志文本框的前景色

#### 方法
- `WriteInfo(string)` - 写入信息级别日志
- `WriteWarning(string)` - 写入警告级别日志
- `WriteError(string, Exception)` - 写入错误级别日志
- `WriteDebug(string)` - 写入调试级别日志
- `WriteLog(LogLevel, string)` - 写入指定级别日志
- `Clear()` - 清空日志显示
- `SaveLogToFile(string)` - 保存日志到文件
- `SetInitialMessage(string, bool)` - 设置初始化消息

### 使用场景

1. **应用程序日志显示**
2. **调试信息输出**
3. **操作过程记录**
4. **错误信息展示**
5. **系统状态监控**

---

#### 相关链接

- [ETRangeSelectControl 使用示例](ETRangeSelectControl使用示例.cs)
- [ETRangeSelectControl 迁移说明](ETRangeSelectControl迁移说明.md)
- [ETLogDisplayControl 使用示例](ETLogDisplayControl使用示例.cs)
- [ETLogDisplayControl 迁移说明](ETLogDisplayControl迁移说明.md)
- [ExtensionsTools 文档](../../README.md)
