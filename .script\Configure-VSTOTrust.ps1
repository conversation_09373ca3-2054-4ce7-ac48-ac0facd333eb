# VSTO信任配置脚本
# 解决VSTO安装和运行的信任问题

param(
    [string]$VSTOPath = "",
    [switch]$EnableFullTrust = $false,
    [switch]$DisableSignatureCheck = $false
)

Write-Host "=== VSTO信任配置脚本 ===" -ForegroundColor Cyan

# 检查管理员权限
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Error "此脚本需要管理员权限运行"
    exit 1
}

try {
    # 1. 配置.NET Framework信任设置
    Write-Host "`n配置.NET Framework信任设置..." -ForegroundColor Green
    
    # 添加信任的发布者
    $trustPublisher = @"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\.NETFramework\Security\TrustManager\PromptingLevel]
"MyComputer"="Enabled"
"LocalIntranet"="Enabled"
"Internet"="Disabled"
"TrustedSites"="Enabled"
"UntrustedSites"="Disabled"

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\.NETFramework\Security\TrustManager\PromptingLevel]
"MyComputer"="Enabled"
"LocalIntranet"="Enabled"
"Internet"="Disabled"
"TrustedSites"="Enabled"
"UntrustedSites"="Disabled"
"@
    
    $regFile = "$env:TEMP\vsto_trust.reg"
    $trustPublisher | Out-File -FilePath $regFile -Encoding ASCII
    
    Write-Host "应用注册表设置..." -ForegroundColor Yellow
    Start-Process -FilePath "regedit.exe" -ArgumentList "/s", $regFile -Wait
    
    # 2. 配置Office信任中心设置
    Write-Host "`n配置Office信任中心设置..." -ForegroundColor Green
    
    # Excel信任设置
    $excelTrust = @"
Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Excel\Security\Trusted Locations\Location99]
"Path"="C:\\"
"Description"="本地驱动器C"
"AllowSubFolders"=dword:00000001

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\15.0\Excel\Security\Trusted Locations\Location99]
"Path"="C:\\"
"Description"="本地驱动器C"
"AllowSubFolders"=dword:00000001

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\16.0\Excel\Security]
"VBAWarnings"=dword:00000001
"AccessVBOM"=dword:00000001

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Office\15.0\Excel\Security]
"VBAWarnings"=dword:00000001
"AccessVBOM"=dword:00000001
"@
    
    $excelRegFile = "$env:TEMP\excel_trust.reg"
    $excelTrust | Out-File -FilePath $excelRegFile -Encoding ASCII
    Start-Process -FilePath "regedit.exe" -ArgumentList "/s", $excelRegFile -Wait
    
    # 3. 配置VSTO特定信任设置
    if ($VSTOPath -ne "") {
        Write-Host "`n配置VSTO路径信任: $VSTOPath" -ForegroundColor Green
        
        # 使用VSTOInstaller配置信任
        $vstoInstaller = "${env:ProgramFiles(x86)}\Common Files\Microsoft Shared\VSTO\10.0\VSTOInstaller.exe"
        if (Test-Path $vstoInstaller) {
            Write-Host "使用VSTOInstaller配置信任..." -ForegroundColor Yellow
            & $vstoInstaller /Install $VSTOPath /Silent
        }
    }
    
    # 4. 禁用签名检查（如果需要）
    if ($DisableSignatureCheck) {
        Write-Host "`n禁用签名检查..." -ForegroundColor Green
        
        $signatureDisable = @"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\.NETFramework\Security\Policy\v4.0]
"SchUseStrongCrypto"=dword:00000000

[HKEY_LOCAL_MACHINE\SOFTWARE\Wow6432Node\Microsoft\.NETFramework\Security\Policy\v4.0]
"SchUseStrongCrypto"=dword:00000000
"@
        
        $sigRegFile = "$env:TEMP\disable_signature.reg"
        $signatureDisable | Out-File -FilePath $sigRegFile -Encoding ASCII
        Start-Process -FilePath "regedit.exe" -ArgumentList "/s", $sigRegFile -Wait
    }
    
    # 5. 启用完全信任（如果需要）
    if ($EnableFullTrust) {
        Write-Host "`n启用完全信任..." -ForegroundColor Green
        
        $fullTrust = @"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\.NETFramework\Security\TrustManager]
"PromptingLevel"="Enabled"

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\.NETFramework\Security\TrustManager]
"PromptingLevel"="Enabled"
"@
        
        $trustRegFile = "$env:TEMP\full_trust.reg"
        $fullTrust | Out-File -FilePath $trustRegFile -Encoding ASCII
        Start-Process -FilePath "regedit.exe" -ArgumentList "/s", $trustRegFile -Wait
    }
    
    Write-Host "`n=== 信任配置完成 ===" -ForegroundColor Cyan
    Write-Host "建议重启Office应用程序以使设置生效" -ForegroundColor Yellow
    
    # 清理临时文件
    Remove-Item -Path "$env:TEMP\vsto_trust.reg" -ErrorAction SilentlyContinue
    Remove-Item -Path "$env:TEMP\excel_trust.reg" -ErrorAction SilentlyContinue
    Remove-Item -Path "$env:TEMP\disable_signature.reg" -ErrorAction SilentlyContinue
    Remove-Item -Path "$env:TEMP\full_trust.reg" -ErrorAction SilentlyContinue
    
}
catch {
    Write-Error "配置失败: $($_.Exception.Message)"
}

Write-Host "`n=== 使用说明 ===" -ForegroundColor Cyan
Write-Host "1. 重新安装VSTO插件" -ForegroundColor White
Write-Host "2. 如果仍有问题，请尝试以下命令：" -ForegroundColor White
Write-Host "   .\Configure-VSTOTrust.ps1 -VSTOPath 'C:\path\to\your.vsto' -EnableFullTrust -DisableSignatureCheck" -ForegroundColor Gray
