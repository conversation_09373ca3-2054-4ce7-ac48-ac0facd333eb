<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectAllTimeMostUsedData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w></ProjectAllTimeMostUsedData><ProjectExpandedStateData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w></ProjectExpandedStateData><ProjectFavoriteData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w></ProjectFavoriteData><ProjectHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>39</Char><CodeMapItemPath>MultiPartFormDataBinaryContent#CreateBoundary</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T15:02:44.2813899+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>238</Char><CodeMapItemPath>MultiPartFormDataBinaryContent#Add</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:03:15.5798848+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Stream, string, string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>47</Char><CodeMapItemPath>Argument#AssertNotNull</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:03:10.1107999+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>T, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w></ProjectHistoryData><ProjectInCodeHighlightData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w></ProjectInCodeHighlightData><ProjectMiniViewData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.Protocol.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.Protocol.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\TelemetryDetails.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\TelemetryDetails.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\MultiPartFormDataBinaryContent.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\MultiPartFormDataBinaryContent.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\OpenAIFileClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\OpenAIFileClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Files\Internal\InternalFileUploadOptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Files\Internal\InternalFileUploadOptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\OpenAIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\OpenAIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\CancellationTokenExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\CancellationTokenExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Utility\GenericActionPipelinePolicy.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Utility\GenericActionPipelinePolicy.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Generated\Internal\Argument.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Generated\Internal\Argument.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Custom\Internal\ClientPipelineExtensions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Custom\Internal\ClientPipelineExtensions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w></ProjectMiniViewData><ProjectName>OpenAI</ProjectName></ProjectData>