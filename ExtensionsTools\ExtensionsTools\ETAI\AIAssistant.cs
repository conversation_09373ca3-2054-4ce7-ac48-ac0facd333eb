using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json.Linq;
using System.Threading;

namespace ET
{
    /// <summary>
    /// Excel回填选项
    /// </summary>
    public enum ExcelFillOption
    {
        /// <summary>
        /// 回填所有结果到Excel
        /// </summary>
        FillAll,

        /// <summary>
        /// 跳过空值不回填
        /// </summary>
        SkipNull,

        /// <summary>
        /// 不回填到Excel
        /// </summary>
        NoFill
    }

    /// <summary>
    /// AI助手静态类，提供Excel与AI服务交互的统一接口
    /// </summary>
    /// <remarks>
    /// 该类封装了所有与AI服务相关的操作，包括：
    /// <list type="bullet">
    /// <item><description>批量问题处理和回复</description></item>
    /// <item><description>单个问题的处理</description></item>
    /// <item><description>Excel数据的自动填充</description></item>
    /// </list>
    /// </remarks>
    public static class AIAssistant
    {
        #region 私有字段

        /// <summary>
        /// AI服务实例，用于处理所有AI相关的请求
        /// </summary>
        /// <remarks>
        /// 使用静态只读字段确保线程安全和单例模式
        /// </remarks>
        static readonly AI.Services.AIService _aiService = new AI.Services.AIService();

        /// <summary>
        /// Excel处理器实例，用于处理Excel相关操作
        /// </summary>
        static readonly AI.Services.Core.AIExcelHandler _excelHandler = new AI.Services.Core.AIExcelHandler();

        /// <summary>
        /// 用于控制异步任务取消的CancellationTokenSource
        /// </summary>
        static CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        #endregion

        #region 事件

        /// <summary>
        /// AI查询进度事件，用于通知UI层当前处理状态
        /// </summary>
        /// <remarks>
        /// 该事件在以下情况下触发：
        /// <list type="bullet">
        /// <item><description>开始处理新的查询组时</description></item>
        /// <item><description>完成一个查询组的处理时</description></item>
        /// <item><description>发生错误时</description></item>
        /// <item><description>更新Excel数据时</description></item>
        /// </list>
        /// </remarks>
        public static event AI.Services.AIService.QueryProgressEventHandler OnQueryProgress
        {
            add => _aiService.OnQueryProgress += value;
            remove => _aiService.OnQueryProgress -= value;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 取消所有正在进行的AI请求
        /// </summary>
        public static void CancelAllRequests()
        {
            CancellationTokenSource oldTokenSource = Interlocked.Exchange(ref _cancellationTokenSource, new CancellationTokenSource());
            oldTokenSource?.Cancel();
            oldTokenSource?.Dispose();
        }

        /// <summary>
        /// 获取AI回复并自动填充到Excel（批量问题）
        /// </summary>
        /// <param name="questionRange">问题区域，可以是Excel的行或列</param>
        /// <param name="knownInfoRange1">已知信息区域1，用于提供问题的上下文</param>
        /// <param name="knownInfoRange2">已知信息区域2（可选），用于提供额外的上下文信息</param>
        /// <param name="modelFileName">AI模型配置文件名，指定使用的AI模型及其参数</param>
        /// <param name="rulesFileName">规则文件名，定义AI回答的规则和格式</param>
        /// <param name="systemContentFile">系统提示文件名（可选），用于设置AI的行为和角色</param>
        /// <param name="fillOption">Excel回填选项</param>
        /// <returns>包含AI响应的元组(响应JSON, 是否为问题行, 请求JSON)</returns>
        /// <exception cref="Exception">当处理过程中发生错误时抛出</exception>
        /// <remarks>
        /// 该方法会自动处理以下任务：
        /// <list type="bullet">
        /// <item><description>读取Excel中的问题和已知信息</description></item>
        /// <item><description>构建AI请求</description></item>
        /// <item><description>获取AI响应</description></item>
        /// <item><description>将响应填充回Excel</description></item>
        /// </list>
        /// </remarks>
        public static async Task<(JObject Response, bool IsQuestionRow, JObject RequestJson)> BatchProcessAIResponsesToExcelAsync(
            Range questionRange,
            Range knownInfoRange1,
            Range knownInfoRange2,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null,
            ExcelFillOption fillOption = ExcelFillOption.FillAll)
        {
            bool isQuestionRow;
            List<AI.Models.AIQuestionGroup> questionGroups = _excelHandler.GetQuestionGroupsFromRange(
                questionRange, knownInfoRange1, knownInfoRange2, out isQuestionRow);

            JObject response = await GetAIResponseAsync(
                questionGroups,
                modelFileName,
                rulesFileName,
                systemContentFile,
                (result, success) =>
                {
                    if (success && fillOption != ExcelFillOption.NoFill)
                    {
                        _aiService.FillResponseToExcel(questionRange, knownInfoRange1, result, isQuestionRow, fillOption);
                    }
                },
                _cancellationTokenSource.Token);

            return (response, isQuestionRow, null);
        }

        /// <summary>
        /// 获取AI回复的JSON结果（批量问题组）
        /// </summary>
        /// <param name="questionGroups">问题组列表</param>
        /// <param name="modelFileName">AI模型配置文件名</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="systemContentFile">系统提示文件名（可选）</param>
        /// <param name="onResponseReceived">响应接收回调（可选），用于处理中间结果</param>
        /// <param name="cancellationToken">用于取消操作的CancellationToken</param>
        /// <returns>包含所有问题回答的JSON对象</returns>
        /// <exception cref="Exception">当处理过程中发生错误时抛出</exception>
        /// <remarks>
        /// 该方法用于批量处理问题组：
        /// <list type="bullet">
        /// <item><description>支持并行处理多个问题组</description></item>
        /// <item><description>通过回调函数实时处理响应</description></item>
        /// <item><description>自动管理API密钥和请求限制</description></item>
        /// <item><description>合并所有响应为最终结果</description></item>
        /// </list>
        /// </remarks>
        [System.Runtime.InteropServices.ComVisible(false)]
        public static async Task<JObject> GetAIResponseAsync(
            List<AI.Models.AIQuestionGroup> questionGroups,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null,
            Action<JObject, bool> onResponseReceived = null,
            CancellationToken cancellationToken = default)
        {
            return await _aiService.GetAIResponseAsync(
                questionGroups,
                modelFileName,
                rulesFileName,
                systemContentFile,
                onResponseReceived,
                cancellationToken);
        }

        /// <summary>
        /// 获取单个问题的AI回复
        /// </summary>
        /// <param name="question">问题文本</param>
        /// <param name="modelFileName">AI模型配置文件名</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="systemContentFile">系统提示文件名（可选）</param>
        /// <returns>AI的回复文本</returns>
        /// <exception cref="Exception">当处理过程中发生错误时抛出</exception>
        /// <remarks>
        /// 该方法适用于处理单个独立的问题，不涉及Excel交互。
        /// 返回的文本格式将根据规则文件的定义进行格式化。
        /// </remarks>
        [System.Runtime.InteropServices.ComVisible(false)]
        public static async Task<string> GetSingleQuestionResponseAsync(
            string question,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null)
        {
            return await _aiService.GetSingleQuestionResponseAsync(
                question,
                modelFileName,
                rulesFileName,
                systemContentFile);
        }

        /// <summary>
        /// 将AI响应结果填充到Excel中
        /// </summary>
        /// <param name="questionRange">问题区域，可以是行或列</param>
        /// <param name="knownInfoRange">已知信息区域</param>
        /// <param name="response">AI响应的JSON对象</param>
        /// <param name="isQuestionRow">是否为问题行模式</param>
        /// <returns>填充是否成功</returns>
        /// <remarks>
        /// 该方法使用线程安全的方式更新Excel单元格。
        /// 如果response为null或格式不正确，将返回false。
        /// 支持两种模式：
        /// <list type="bullet">
        /// <item><description>问题行模式：问题在行中，答案填充在相应的列</description></item>
        /// <item><description>问题列模式：问题在列中，答案填充在相应的行</description></item>
        /// </list>
        /// </remarks>
        public static bool FillResponseToExcel(
            Range questionRange,
            Range knownInfoRange,
            JObject response,
            bool isQuestionRow)
        {
            return _aiService.FillResponseToExcel(
                questionRange,
                knownInfoRange,
                response,
                isQuestionRow);
        }
        #endregion
    }
}