using System;
using System.IO;
using System.Text;
using ET.AI.Models;
using OpenAI.Chat;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace ET.AI.Services.Core
{
    /// <summary>
    /// AI配置读取器，负责读取和解析配置文件
    /// </summary>
    /// <remarks>
    /// 该类主要负责：
    /// 1. 读取和解析AI配置文件
    /// 2. 读取系统内容文件
    /// 3. 验证配置的有效性
    /// 4. 处理配置文件的格式和编码
    /// </remarks>
    class AIConfigReader
    {
        /// <summary>
        /// 读取AI配置文件
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>解析后的AI配置对象，如果读取或解析失败则返回null</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 查找配置文件（支持相对路径和配置目录）
        /// 2. 解析INI格式的配置文件
        /// 3. 读取并处理系统内容文件
        /// 4. 验证必要的配置项
        /// 
        /// 配置文件格式示例：
        /// [model]
        /// gpt-4
        /// [temperature]
        /// 0.2
        /// [apikey]
        /// key1
        /// key2
        /// </remarks>
        /// <exception cref="Exception">当缺少必要的配置项时抛出</exception>
        public static AIConfig ReadConfig(string filePath)
        {
            try
            {
                string configPath = filePath;

                if (!File.Exists(configPath))
                {
                    configPath = ETConfig.GetConfigPath(Path.GetFileName(filePath), "AiModel");
                }

                if (!File.Exists(configPath))
                    return null;

                AIConfig config = new AIConfig();
                string currentKey = null;
                StringBuilder valueBuilder = new StringBuilder();

                // 读取配置文件
                string[] lines = File.ReadAllLines(configPath, Encoding.UTF8);
                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                        continue;

                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        // 如果有之前的键值对，先处理它
                        if (currentKey != null && valueBuilder.Length > 0)
                        {
                            SetConfigValue(config, currentKey, valueBuilder.ToString().Trim());
                            valueBuilder.Clear();
                        }

                        // 提取新的键
                        currentKey = trimmedLine.Substring(1, trimmedLine.Length - 2);
                    }
                    else if (currentKey != null)
                    {
                        // 累积值
                        if (valueBuilder.Length > 0)
                            valueBuilder.AppendLine();
                        valueBuilder.Append(trimmedLine);
                    }
                }

                // 处理最后一个键值对
                if (currentKey != null && valueBuilder.Length > 0)
                {
                    SetConfigValue(config, currentKey, valueBuilder.ToString().Trim());
                }

                // 尝试读取系统内容文件
                if (!string.IsNullOrEmpty(config.SystemContent))
                {
                    string systemContentPath = config.SystemContent;
                    if (!File.Exists(systemContentPath))
                    {
                        systemContentPath = ETConfig.GetConfigPath(Path.GetFileName(systemContentPath), "AiModel");
                    }

                    if (File.Exists(systemContentPath))
                    {
                        config.SystemContent = File.ReadAllText(systemContentPath, Encoding.UTF8);
                    }
                }

                // 验证必要的配置项
                if (string.IsNullOrEmpty(config.Model))
                {
                    throw new Exception("未配置模型名称！");
                }

                if (config.APIKeys.Count == 0)
                {
                    throw new Exception("未配置API密钥！");
                }

                return config;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 读取指定文件的内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容，如果读取失败则返回null</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 验证文件路径的有效性
        /// 2. 检查文件是否存在
        /// 3. 根据文件扩展名选择处理方式
        /// 4. 使用UTF-8编码读取文件内容
        /// 
        /// 目前支持的文件类型：
        /// - .txt：文本文件
        /// </remarks>
        public static string ReadContent(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return null;
            }

            try
            {
                if (!File.Exists(filePath))
                {
                    return null;
                }

                string extension = Path.GetExtension(filePath).ToLower();
                return extension switch
                {
                    ".txt" => File.ReadAllText(filePath, Encoding.UTF8),
                    _ => null
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="config">AI配置对象</param>
        /// <param name="key">配置键名</param>
        /// <param name="value">配置值</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 根据键名选择对应的配置项
        /// 2. 进行必要的类型转换
        /// 3. 处理特殊格式（如浮点数的'f'后缀）
        /// 4. 处理多行值（如API密钥列表）
        /// 
        /// 支持的配置项：
        /// - model：模型名称
        /// - temperature：温度参数（0-1的浮点数）
        /// - top_p：top-p参数（0-1的浮点数）
        /// - response_format_type：响应格式类型
        /// - system_content：系统内容
        /// - total_max_requests：总请求数限制
        /// - baseurl：基础URL
        /// - apikey：API密钥（支持多行）
        /// - max_concurrent_requests：最大并发请求数
        /// - max_requests_per_minute：每分钟最大请求数
        /// - proxy_host：代理主机
        /// - proxy_port：代理端口
        /// - max_json_retry_count：JSON重试次数
        /// - request_timeout：请求超时时间
        /// - base_group_size：基础组大小
        /// </remarks>
        static void SetConfigValue(AIConfig config, string key, string value)
        {
            switch (key.ToLower())
            {
                case "model":
                    config.Model = value;
                    break;

                case "temperature":
                    if (value.EndsWith("f", StringComparison.OrdinalIgnoreCase))
                        value = value.Substring(0, value.Length - 1);
                    if (float.TryParse(value, out float temp))
                        config.Temperature = temp;
                    break;

                case "top_p":
                    if (value.EndsWith("f", StringComparison.OrdinalIgnoreCase))
                        value = value.Substring(0, value.Length - 1);
                    if (float.TryParse(value, out float topP))
                        config.TopP = topP;
                    break;

                case "response_format_type":
                    // 根据格式类型设置 ResponseFormat
                    switch (value?.ToLower())
                    {
                        case "json_object":
                            config.ResponseFormat = ChatResponseFormat.CreateJsonObjectFormat();
                            break;
                        case "json_schema":
                            // 创建一个最简单的 JSON Schema，只包含一个字符串类型的结果
                            BinaryData jsonSchema = BinaryData.FromBytes("""
                            {
                                "type": "string"
                            }
                            """U8.ToArray());
                            config.ResponseFormat = ChatResponseFormat.CreateJsonSchemaFormat(
                                jsonSchemaFormatName: "ChatCompletion",
                                jsonSchema: jsonSchema,
                                jsonSchemaFormatDescription: "简单的字符串响应格式",
                                jsonSchemaIsStrict: true
                            );
                            break;
                        case "text":
                            config.ResponseFormat = ChatResponseFormat.CreateTextFormat();
                            break;
                    }
                    break;

                case "system_content":
                    config.SystemContent = value;
                    break;

                case "total_max_requests":
                    if (int.TryParse(value, out int totalMaxRequests))
                        config.TotalMaxRequests = totalMaxRequests;
                    break;

                case "baseurl":
                    config.BaseURL = value;
                    break;

                case "apikey":
                    // 处理可能包含多行的API密钥
                    string[] keys = value.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (string apiKeyItem in keys)
                    {
                        if (!string.IsNullOrWhiteSpace(apiKeyItem))
                        {
                            config.APIKeys.Add(apiKeyItem.Trim());
                        }
                    }
                    break;

                case "max_concurrent_requests":
                    if (int.TryParse(value, out int maxConcurrentRequests))
                        config.MaxConcurrentRequests = maxConcurrentRequests;
                    break;

                case "max_requests_per_minute":
                    if (int.TryParse(value, out int maxRequestsPerMinute))
                        config.MaxRequestsPerMinute = maxRequestsPerMinute;
                    break;

                case "proxy_host":
                    config.ProxyHost = value;
                    break;

                case "proxy_port":
                    if (int.TryParse(value, out int proxyPort))
                        config.ProxyPort = proxyPort;
                    break;

                case "max_json_retry_count":
                    if (int.TryParse(value, out int maxJsonRetryCount))
                        config.MaxJsonRetryCount = maxJsonRetryCount;
                    break;

                case "request_timeout":
                    if (int.TryParse(value, out int timeout))
                        config.RequestTimeout = timeout;
                    break;

                case "base_group_size":
                    if (int.TryParse(value, out int baseGroupSize))
                        config.BaseGroupSize = baseGroupSize;
                    break;
            }
        }
    }
}