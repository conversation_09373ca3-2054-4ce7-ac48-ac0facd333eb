using System.Collections.Generic;

namespace ET.AI.Services.Core.Models
{
    /// <summary>
    /// 查询组
    /// </summary>
    class QueryGroup
    {
        /// <summary>
        /// 组索引
        /// </summary>
        public int GroupIndex { get; set; }

        /// <summary>
        /// 组内项目列表
        /// </summary>
        public List<QueryItem> Items { get; set; }
    }

    /// <summary>
    /// 查询项
    /// </summary>
    class QueryItem
    {
        /// <summary>
        /// 列表索引
        /// </summary>
        public int ListIndex { get; set; }

        /// <summary>
        /// 已知信息1
        /// </summary>
        public string KnownInfo1 { get; set; }

        /// <summary>
        /// 已知信息2路径
        /// </summary>
        public string KnownInfo2Path { get; set; }
    }
}