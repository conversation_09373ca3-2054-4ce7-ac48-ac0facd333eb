using ET;
using HyExcelVsto.Module.Wenzi.Models;
using HyExcelVsto.Module.Wenzi.Services;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Xml;
using System.Xml.Serialization;

namespace HyExcelVsto.Module.Wenzi.Utils
{
    /// <summary>
    /// 配置管理工具类
    /// </summary>
    public class ConfigManager : IDisposable
    {
        private readonly string _configPath;
        private readonly Logging.ILogger _logger;
        private AttendanceRuleConfig _config;
        private readonly FileSystemWatcher _watcher;
        private readonly ReaderWriterLockSlim _configLock;
        private readonly ExcelService _excelService;

        public event EventHandler<AttendanceRuleConfig> ConfigChanged;

        public ConfigManager(string configPath = null, Logging.ILogger logger = null, ExcelService excelService = null)
        {
            _configPath = configPath ?? ETConfig.GetConfigDirectory("查找文件预置目录.config", ".xw");
            _logger = logger;
            _excelService = excelService;
            _configLock = new ReaderWriterLockSlim();

            // 确保配置目录存在
            string configDir = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            // 初始化文件监视器
            _watcher = new FileSystemWatcher(configDir, Path.GetFileName(_configPath))
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime
            };

            _watcher.Changed += OnConfigFileChanged;
            _watcher.EnableRaisingEvents = true;
        }

        /// <summary>
        /// 获取所有配置参数及其默认值
        /// </summary>
        public List<(string Name, string Value, string Comment)> GetParameterList()
        {
            AttendanceRuleConfig defaultConfig = CreateDefaultConfig();
            return
            [
                ("AllowedLateMinutes", defaultConfig.AllowedLateMinutes.ToString(), "允许迟到的分钟数"),
                ("LateToMissingMinutes", defaultConfig.LateToMissingMinutes.ToString(), "迟到转缺卡的分钟数（超过此时间算缺卡）"),
                ("AllowedEarlyLeaveMinutes", defaultConfig.AllowedEarlyLeaveMinutes.ToString(), "下班允许提前的分钟数"),
                ("EarlyLeaveToMissingMinutes", defaultConfig.EarlyLeaveToMissingMinutes.ToString(), "早退转缺卡的分钟数（超过此时间算缺卡）"),
                ("FourTimesCardNoonOutAllowedMinutes", defaultConfig.FourTimesCardNoonOutAllowedMinutes.ToString(), "四次卡中午第一个时段下班后允许打卡的时间（分钟）"),
                ("FourTimesCardNoonInAllowedMinutes", defaultConfig.FourTimesCardNoonInAllowedMinutes.ToString(), "四次卡中午第二个时段上班前允许打卡的时间（分钟）"),
                ("DelayedWorkMinutes", defaultConfig.DelayedWorkMinutes.ToString(), "延迟下班判定时间（分钟）"),
                ("DelayedWorkGranularity", defaultConfig.DelayedWorkGranularity.ToString(), "延迟下班时间统计颗粒度（分钟）"),
                ("EarlyArrivalMinutes", defaultConfig.EarlyArrivalMinutes.ToString(), "提前上班判定时间（分钟）"),
                ("EarlyArrivalGranularity", defaultConfig.EarlyArrivalGranularity.ToString(), "提前上班时间统计颗粒度（分钟）"),
                ("NoonFlexibleMinutes", defaultConfig.NoonFlexibleMinutes.ToString(), "普通卡中午打卡弹性时间（分钟）")
            ];
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        private List<ValidationResult> ValidateConfig(AttendanceRuleConfig config)
        {
            List<ValidationResult> results = [];
            ValidationContext context = new(config);
            Validator.TryValidateObject(config, context, results, true);

            // 验证默认时间
            //if (config.DefaultWorkStartTime < TimeSpan.Zero || config.DefaultWorkStartTime >= TimeSpan.FromHours(24))
            //    results.Add(new ValidationResult("默认上班时间必须在0-24小时之间"));
            //if (config.DefaultWorkEndTime < TimeSpan.Zero || config.DefaultWorkEndTime >= TimeSpan.FromHours(24))
            //    results.Add(new ValidationResult("默认下班时间必须在0-24小时之间"));

            // 验证午休时间
            //if (config.DefaultLunchStartTime < TimeSpan.Zero || config.DefaultLunchStartTime >= TimeSpan.FromHours(24))
            //    results.Add(new ValidationResult("默认午休开始时间必须在0-24小时之间"));
            //if (config.DefaultLunchEndTime < TimeSpan.Zero || config.DefaultLunchEndTime >= TimeSpan.FromHours(24))
            //    results.Add(new ValidationResult("默认午休结束时间必须在0-24小时之间"));
            //if (config.DefaultLunchEndTime <= config.DefaultLunchStartTime)
            //    results.Add(new ValidationResult("默认午休结束时间必须晚于默认午休开始时间"));

            return results;
        }

        /// <summary>
        /// 配置文件变更处理
        /// </summary>
        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                Thread.Sleep(100); // 等待文件写入完成
                AttendanceRuleConfig newConfig = LoadConfigFromFile();
                List<ValidationResult> validationResults = ValidateConfig(newConfig);

                if (validationResults.Count > 0)
                {
                    _logger?.LogError($"配置验证失败: {string.Join(", ", validationResults)}");
                    return;
                }

                _configLock.EnterWriteLock();
                try
                {
                    _config = newConfig;
                    ConfigChanged?.Invoke(this, _config);
                    _logger?.LogInformation("配置已更新");
                }
                finally
                {
                    _configLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("更新配置失败", ex);
            }
        }

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        private AttendanceRuleConfig LoadConfigFromFile()
        {
            using (FileStream stream = new(_configPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            {
                XmlSerializer serializer = new(typeof(AttendanceRuleConfig));
                return (AttendanceRuleConfig)serializer.Deserialize(stream);
            }
        }

        /// <summary>
        /// 从Excel加载配置
        /// </summary>
        private AttendanceRuleConfig LoadConfigFromExcel()
        {
            if (_excelService == null)
                return null;

            try
            {
                AttendanceRuleConfig config = new();
                Worksheet worksheet = _excelService.GetWorksheet(ExcelSheetConfig.SheetParameters);
                if (worksheet == null)
                {
                    _logger?.LogWarning("未找到参数配置表，将使用默认配置");
                    return null;
                }

                Range usedRange = worksheet.UsedRange;
                object[,] values = usedRange.Value2;
                int rowCount = usedRange.Rows.Count;
                int configCount = 0;

                // 从第2行开始读取（跳过表头）
                for (int row = 2; row <= rowCount; row++)
                {
                    string name = Convert.ToString(values[row, ExcelSheetConfig.ParameterColumns.VariableName])?.Trim();
                    string value = Convert.ToString(values[row, ExcelSheetConfig.ParameterColumns.VariableValue])?.Trim();

                    if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(value))
                        continue;

                    try
                    {
                        SetConfigValue(config, name, value);
                        configCount++;
                        //_logger?.LogInformation($"已读取配置项：{name} = {value}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning($"读取配置项 {name} 失败：{ex.Message}");
                    }
                }

                if (configCount == 0)
                {
                    _logger?.LogWarning("未读取到任何有效配置项，将使用默认配置");
                    return null;
                }

                // 验证所有必需的配置项是否都已设置
                bool hasAllConfigs = true;
                foreach ((string Name, string Value, string Comment) param in GetParameterList())
                {
                    PropertyInfo propertyInfo = typeof(AttendanceRuleConfig).GetProperty(param.Name);
                    if (propertyInfo != null)
                    {
                        object value = propertyInfo.GetValue(config);
                        if (value == null || (value is int intValue && intValue == 0))
                        {
                            _logger?.LogWarning($"配置项 {param.Name} 未在Excel中设置，将使用默认值：{param.Value}");
                            hasAllConfigs = false;
                        }
                    }
                }

                if (!hasAllConfigs)
                {
                    _logger?.LogWarning("部分配置项未设置，建议检查Excel配置表");
                }

                _logger?.LogInformation($"成功从Excel加载了{configCount}个配置项");
                return config;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"从Excel加载配置失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        private void SetConfigValue(AttendanceRuleConfig config, string name, string value)
        {
            try
            {
                switch (name)
                {
                    case "AllowedLateMinutes":
                        config.AllowedLateMinutes = int.Parse(value);
                        break;

                    case "LateToMissingMinutes":
                        config.LateToMissingMinutes = int.Parse(value);
                        break;

                    case "AllowedEarlyLeaveMinutes":
                        config.AllowedEarlyLeaveMinutes = int.Parse(value);
                        break;

                    case "EarlyLeaveToMissingMinutes":
                        config.EarlyLeaveToMissingMinutes = int.Parse(value);
                        break;

                    case "FourTimesCardNoonOutAllowedMinutes":
                        config.FourTimesCardNoonOutAllowedMinutes = int.Parse(value);
                        break;

                    case "FourTimesCardNoonInAllowedMinutes":
                        config.FourTimesCardNoonInAllowedMinutes = int.Parse(value);
                        break;

                    case "DelayedWorkMinutes":
                        config.DelayedWorkMinutes = int.Parse(value);
                        break;

                    case "DelayedWorkGranularity":
                        config.DelayedWorkGranularity = int.Parse(value);
                        break;

                    case "EarlyArrivalMinutes":
                        config.EarlyArrivalMinutes = int.Parse(value);
                        break;

                    case "EarlyArrivalGranularity":
                        config.EarlyArrivalGranularity = int.Parse(value);
                        break;

                    case "NoonFlexibleMinutes":
                        config.NoonFlexibleMinutes = int.Parse(value);
                        break;

                    case "ClockOutFlexibleMinutes": // 兼容旧配置，读取后转换为新参数
                        config.AllowedEarlyLeaveMinutes = int.Parse(value);
                        _logger?.LogInformation($"已将旧参数ClockOutFlexibleMinutes的值{value}转换为AllowedEarlyLeaveMinutes");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"设置配置值失败: {name} = {value}, {ex.Message}");
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public AttendanceRuleConfig LoadConfig()
        {
            _configLock.EnterUpgradeableReadLock();
            try
            {
                if (_config != null)
                {
                    return _config;
                }

                _configLock.EnterWriteLock();
                try
                {
                    // 尝试从Excel加载配置
                    _config = LoadConfigFromExcel();
                    if (_config != null)
                    {
                        List<ValidationResult> validationResults = ValidateConfig(_config);
                        if (validationResults.Count == 0)
                        {
                            return _config;
                        }
                        _logger?.LogError($"Excel配置验证失败: {string.Join(", ", validationResults)}");
                    }

                    // 如果从Excel加载失败，尝试从文件加载
                    if (File.Exists(_configPath))
                    {
                        _config = LoadConfigFromFile();
                        List<ValidationResult> validationResults = ValidateConfig(_config);
                        if (validationResults.Count > 0)
                        {
                            _logger?.LogError($"配置文件验证失败: {string.Join(", ", validationResults)}");
                            _config = CreateDefaultConfig();
                            SaveConfigInternal(_config); // 保存默认配置以避免下次重复验证失败
                        }
                    }
                    else
                    {
                        _logger?.LogInformation("配置文件不存在，创建默认配置");
                        _config = CreateDefaultConfig();
                        SaveConfigInternal(_config);
                    }

                    return _config;
                }
                finally
                {
                    _configLock.ExitWriteLock();
                }
            }
            finally
            {
                _configLock.ExitUpgradeableReadLock();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig(AttendanceRuleConfig config)
        {
            List<ValidationResult> validationResults = ValidateConfig(config);
            if (validationResults.Count > 0)
            {
                throw new ValidationException($"配置验证失败: {string.Join(", ", validationResults)}");
            }

            _configLock.EnterWriteLock();
            try
            {
                SaveConfigInternal(config);
            }
            finally
            {
                _configLock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 内部保存配置方法（假设已经持有写锁）
        /// </summary>
        private void SaveConfigInternal(AttendanceRuleConfig config)
        {
            using (FileStream stream = new(_configPath, FileMode.Create))
            using (XmlTextWriter writer = new(stream, Encoding.UTF8))
            {
                writer.Formatting = Formatting.Indented;
                XmlSerializer serializer = new(typeof(AttendanceRuleConfig));
                serializer.Serialize(writer, config);
            }

            _config = config;
            _logger?.LogInformation("配置已保存");
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private AttendanceRuleConfig CreateDefaultConfig()
        {
            return new AttendanceRuleConfig
            {
                AllowedLateMinutes = 1,
                LateToMissingMinutes = 40,
                AllowedEarlyLeaveMinutes = 1,
                EarlyLeaveToMissingMinutes = 30,
                FourTimesCardNoonOutAllowedMinutes = 60,
                FourTimesCardNoonInAllowedMinutes = 30,
                DelayedWorkMinutes = 120,
                DelayedWorkGranularity = 30,
                EarlyArrivalMinutes = 60,
                EarlyArrivalGranularity = 30,
                NoonFlexibleMinutes = 30
            };
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            try
            {
                _config = CreateDefaultConfig();
                SaveConfig(_config);
                _logger?.LogInformation("配置已重置为默认值");
            }
            catch (Exception ex)
            {
                _logger?.LogError("重置配置失败", ex);
                throw;
            }
        }

        public void Dispose()
        {
            _watcher?.Dispose();
            _configLock?.Dispose();
        }
    }
}