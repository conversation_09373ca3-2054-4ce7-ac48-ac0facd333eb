using System;
using System.Windows.Forms;
using ExtensionsTools;
using HyExcelVsto.Module.WX.StationDataProcessor;
using HyExcelVsto.Module.WX.StationConverter;
using HyExcelVsto.Module.WX.AngleExtractor;
using HyExcelVsto.Module.WX.TowerAccountProcessor;
using ET;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// 基站数据处理系统主窗体 提供各个功能模块的入口
    /// </summary>
    public partial class StationDataProcessorMainForm : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StationDataProcessorMainForm()
        {
            InitializeComponent();
            InitializeMainForm();

            ETLogManager.Info(this, "StationDataProcessorMainForm初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化主窗体
        /// </summary>
        private void InitializeMainForm()
        {
            // 设置窗体标题
            this.Text = "无线基站信息表数据处理系统";

            // 设置窗体图标（如果有的话） this.Icon = Properties.Resources.AppIcon;
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 基站数据处理按钮点击事件
        /// </summary>
        private void BtnStationDataProcessor_Click(object sender, EventArgs e)
        {
            try
            {
                var form = new StationDataProcessorForm();
                form.Show();
                ETLogManager.Info(this, "打开基站数据处理器窗体");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开基站数据处理器窗体失败", ex);
                MessageBox.Show($"打开基站数据处理器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 站点数据转换按钮点击事件
        /// </summary>
        private void BtnStationConverter_Click(object sender, EventArgs e)
        {
            try
            {
                var form = new StationConverterForm();
                form.Show();
                ETLogManager.Info(this, "打开站点数据转换器窗体");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开站点数据转换器窗体失败", ex);
                MessageBox.Show($"打开站点数据转换器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 方向角提取器按钮点击事件
        /// </summary>
        private void BtnAngleExtractor_Click(object sender, EventArgs e)
        {
            try
            {
                var form = new AngleExtractorForm();
                form.Show();
                ETLogManager.Info(this, "打开方向角/下倾角提取器窗体");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开方向角/下倾角提取器窗体失败", ex);
                MessageBox.Show($"打开方向角/下倾角提取器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 铁塔台账处理器按钮点击事件
        /// </summary>
        private void BtnTowerAccountProcessor_Click(object sender, EventArgs e)
        {
            try
            {
                var form = new TowerAccountProcessorForm();
                form.Show();
                ETLogManager.Info(this, "打开铁塔内部台账梳理窗体");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开铁塔内部台账梳理窗体失败", ex);
                MessageBox.Show($"打开铁塔内部台账梳理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 关于按钮点击事件
        /// </summary>
        private void BtnAbout_Click(object sender, EventArgs e)
        {
            try
            {
                string aboutMessage = "无线基站信息表数据处理系统\n\n" +
                                    "版本：2.0\n" +
                                    "功能模块：\n" +
                                    "• 基站数据处理器\n" +
                                    "• 站点数据转换器\n" +
                                    "• 方向角/下倾角提取器\n" +
                                    "• 铁塔内部台账梳理\n\n" +
                                    "开发：HyHelper团队";

                MessageBox.Show(aboutMessage, "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "显示关于信息失败", ex);
            }
        }

        #endregion 事件处理

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "StationDataProcessorMainForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}