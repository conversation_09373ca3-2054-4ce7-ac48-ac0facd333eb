using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ExtensionsTools.ETAIv2.Models;
using ExtensionsTools.ETAIv2.Interfaces;
using ExtensionsTools.ETAIv2.Constants;
using ExtensionsTools.ETAIv2.Exceptions;
using ET;

namespace ExtensionsTools.ETAIv2.Utils
{
    /// <summary>
    /// AI配置管理器，兼容现有.ai文件格式
    /// </summary>
    public class AIConfigManager : IAIConfigManager
    {
        private readonly string _configBasePath;
        private readonly ConcurrentDictionary<string, string> _configCache;

        public AIConfigManager()
        {
            // 使用现有的ETConfig获取配置路径
            _configBasePath = Path.GetDirectoryName(ETConfig.GetConfigDirectory("dummy.txt", ".ai"));
            _configCache = new ConcurrentDictionary<string, string>();
        }

        /// <summary>
        /// 加载模型配置
        /// </summary>
        public AIModelConfig LoadModelConfig(string configFile)
        {
            try
            {
                if (string.IsNullOrEmpty(configFile))
                    throw new ArgumentException("配置文件名不能为空", nameof(configFile));

                var configPath = GetConfigPath(configFile);
                if (!File.Exists(configPath))
                    throw new FileNotFoundException($"配置文件不存在: {configPath}");

                var config = new AIModelConfig();
                var lines = File.ReadAllLines(configPath);

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                        continue;

                    // 解析配置节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        var sectionName = trimmedLine.Substring(1, trimmedLine.Length - 2);
                        var valueIndex = Array.FindIndex(lines, l => l.Trim() == trimmedLine) + 1;

                        if (valueIndex < lines.Length && !string.IsNullOrEmpty(lines[valueIndex]))
                        {
                            var value = lines[valueIndex].Trim();
                            SetConfigValue(config, sectionName, value);
                        }
                    }
                }

                // 验证配置
                if (!config.IsValid())
                    throw new ConfigurationException("配置文件格式无效或缺少必要参数", configFile);

                return config;
            }
            catch (Exception ex) when (!(ex is ConfigurationException))
            {
                throw new ConfigurationException($"加载配置文件失败: {ex.Message}", ex, configFile);
            }
        }

        /// <summary>
        /// 加载全局提示词
        /// </summary>
        public string LoadGlobalPrompt(string promptFile)
        {
            try
            {
                if (string.IsNullOrEmpty(promptFile))
                    return string.Empty;

                var promptPath = GetConfigPath(promptFile);
                if (!File.Exists(promptPath))
                    return string.Empty;

                return File.ReadAllText(promptPath).Trim();
            }
            catch (Exception ex)
            {
                throw new ConfigurationException($"加载提示词文件失败: {ex.Message}", ex, promptFile);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig(string configFile, AIModelConfig config)
        {
            try
            {
                if (string.IsNullOrEmpty(configFile))
                    throw new ArgumentException("配置文件名不能为空", nameof(configFile));

                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                var configPath = GetConfigPath(configFile);
                var configContent = BuildConfigContent(config);

                File.WriteAllText(configPath, configContent);
            }
            catch (Exception ex)
            {
                throw new ConfigurationException($"保存配置文件失败: {ex.Message}", ex, configFile);
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        public string GetConfigPath(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException("文件名不能为空", nameof(fileName));

            return Path.Combine(_configBasePath, fileName);
        }

        /// <summary>
        /// 列出可用的配置文件
        /// </summary>
        public List<string> ListConfigFiles(string pattern = "*.ai")
        {
            try
            {
                if (!Directory.Exists(_configBasePath))
                    return new List<string>();

                return Directory.GetFiles(_configBasePath, pattern)
                    .Select(Path.GetFileName)
                    .Where(f => !string.IsNullOrEmpty(f))
                    .OrderBy(f => f)
                    .ToList();
            }
            catch (Exception ex)
            {
                throw new ConfigurationException($"列出配置文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 列出可用的规则文件
        /// </summary>
        public List<string> ListRuleFiles(string pattern = "*.rule")
        {
            try
            {
                if (!Directory.Exists(_configBasePath))
                    return new List<string>();

                return Directory.GetFiles(_configBasePath, pattern)
                    .Select(Path.GetFileName)
                    .Where(f => !string.IsNullOrEmpty(f))
                    .OrderBy(f => f)
                    .ToList();
            }
            catch (Exception ex)
            {
                throw new ConfigurationException($"列出规则文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        private void SetConfigValue(AIModelConfig config, string sectionName, string value)
        {
            switch (sectionName.ToLower())
            {
                case "server":
                    config.Server = value;
                    break;

                case "model":
                    config.Model = value;
                    break;

                case "baseurl":
                    config.BaseURL = value;
                    break;

                case "apikey":
                    config.APIKey = value;
                    break;

                case "proxy_host":
                    config.ProxyHost = value;
                    break;

                case "proxy_port":
                    if (int.TryParse(value, out int proxyPort))
                        config.ProxyPort = proxyPort;
                    break;

                case "request_timeout":
                    if (int.TryParse(value, out int timeout))
                        config.RequestTimeout = timeout;
                    break;

                case "top_p":
                    if (float.TryParse(value.TrimEnd('f'), out float topP))
                        config.TopP = topP;
                    break;

                case "temperature":
                    if (float.TryParse(value.TrimEnd('f'), out float temperature))
                        config.Temperature = temperature;
                    break;

                case "response_format_type":
                    config.ResponseFormatType = value;
                    break;

                case "base_group_size":
                    if (int.TryParse(value, out int groupSize))
                        config.BaseGroupSize = groupSize;
                    break;

                case "max_json_retry_count":
                    if (int.TryParse(value, out int retryCount))
                        config.MaxJsonRetryCount = retryCount;
                    break;

                case "max_concurrent_requests":
                    if (int.TryParse(value, out int maxConcurrent))
                        config.MaxConcurrentRequests = maxConcurrent;
                    break;

                case "max_requests_per_minute":
                    if (int.TryParse(value, out int maxPerMinute))
                        config.MaxRequestsPerMinute = maxPerMinute;
                    break;

                case "total_max_requests":
                    if (int.TryParse(value, out int totalMax))
                        config.TotalMaxRequests = totalMax;
                    break;

                case "system_content":
                    config.SystemContent = value;
                    break;
            }
        }

        /// <summary>
        /// 构建配置文件内容
        /// </summary>
        private string BuildConfigContent(AIModelConfig config)
        {
            var content = new List<string>
            {
                "[server]",
                config.Server ?? "",
                "",
                "[model]",
                config.Model ?? "",
                "",
                "[BaseURL]",
                config.BaseURL ?? "",
                "",
                "[APIKey]",
                config.APIKey ?? "",
                "",
                "[proxy_host]",
                config.ProxyHost ?? "",
                "",
                "[proxy_port]",
                config.ProxyPort.ToString(),
                "",
                "[request_timeout]",
                config.RequestTimeout.ToString(),
                "",
                "[top_p]",
                $"{config.TopP}f",
                "",
                "[temperature]",
                $"{config.Temperature}f",
                "",
                "[response_format_type]",
                config.ResponseFormatType ?? "",
                "",
                "[base_group_size]",
                config.BaseGroupSize.ToString(),
                "",
                "[max_json_retry_count]",
                config.MaxJsonRetryCount.ToString(),
                "",
                "[max_concurrent_requests]",
                config.MaxConcurrentRequests.ToString(),
                "",
                "[max_requests_per_minute]",
                config.MaxRequestsPerMinute.ToString(),
                "",
                "[total_max_requests]",
                config.TotalMaxRequests.ToString(),
                "",
                "[system_content]",
                config.SystemContent ?? ""
            };

            return string.Join(Environment.NewLine, content);
        }

        /// <summary>
        /// 异步获取API密钥
        /// </summary>
        public async Task<string> GetApiKeyAsync()
        {
            try
            {
                // 优先从缓存获取
                if (_configCache.TryGetValue("APIKey", out string cachedKey) && !string.IsNullOrEmpty(cachedKey))
                {
                    return cachedKey;
                }

                // 从配置文件获取
                string apiKey = await LoadConfigValueAsync("APIKey");

                if (!string.IsNullOrEmpty(apiKey))
                {
                    _configCache.TryAdd("APIKey", apiKey);
                    return apiKey;
                }

                throw new ConfigurationException("API密钥未配置或为空");
            }
            catch (Exception ex)
            {
                throw new ConfigurationException($"API密钥获取失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 异步获取基础URL
        /// </summary>
        public async Task<string> GetBaseUrlAsync()
        {
            try
            {
                if (_configCache.TryGetValue("BaseURL", out string cachedUrl))
                {
                    return cachedUrl;
                }

                string baseUrl = await LoadConfigValueAsync("BaseURL");
                if (!string.IsNullOrEmpty(baseUrl))
                {
                    _configCache.TryAdd("BaseURL", baseUrl);
                }

                return baseUrl ?? "https://api.openai.com/v1/";
            }
            catch (Exception)
            {
                return "https://api.openai.com/v1/";
            }
        }

        /// <summary>
        /// 验证API配置是否有效
        /// </summary>
        public async Task<bool> ValidateApiConfigAsync()
        {
            try
            {
                string apiKey = await GetApiKeyAsync();
                string baseUrl = await GetBaseUrlAsync();

                // 基础验证
                if (string.IsNullOrEmpty(apiKey))
                    return false;

                // 可以添加更多验证逻辑，如测试API连接
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从配置文件异步加载配置值
        /// </summary>
        private async Task<string> LoadConfigValueAsync(string key)
        {
            // 这里需要根据实际的配置文件格式实现 假设是从.ai文件中读取配置
            await Task.CompletedTask; // 保持异步签名

            // 实际实现需要根据现有的配置文件格式 例如：从INI文件、JSON文件或其他格式中读取
            return GetConfigValue(key); // 调用现有的同步方法
        }

        /// <summary>
        /// 获取配置值（同步方法，用于异步方法调用）
        /// </summary>
        private string GetConfigValue(string key)
        {
            try
            {
                // 尝试从配置文件中读取，优先选择OpenAI配置
                var configFiles = ListConfigFiles("*.ai");
                if (configFiles.Any())
                {
                    // 优先选择OpenAI配置文件
                    var openaiConfigFile = configFiles.FirstOrDefault(f =>
                        f.ToLower().Contains("openai") ||
                        f.ToLower().Contains("gpt"));

                    var selectedConfigFile = openaiConfigFile ?? configFiles.First();

                    // 记录使用的配置文件
                    if (key.ToLower() == "apikey")
                    {
                        Console.WriteLine($"[AIConfigManager] 使用配置文件: {selectedConfigFile}");
                    }

                    var config = LoadModelConfig(selectedConfigFile);

                    switch (key.ToLower())
                    {
                        case "apikey":
                            return config.APIKey;

                        case "baseurl":
                            return config.BaseURL;

                        case "server":
                            return config.Server;

                        default:
                            return null;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[AIConfigManager] 获取配置值失败: {ex.Message}");
                return null;
            }
        }
    }
}