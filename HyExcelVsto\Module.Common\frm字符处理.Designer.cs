﻿namespace HyExcelVsto.Module.Common
{
    partial class frm字符处理
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage规整字符串 = new System.Windows.Forms.TabPage();
            this.checkBox去除所有空格 = new System.Windows.Forms.CheckBox();
            this.checkBox制表符 = new System.Windows.Forms.CheckBox();
            this.button去干扰字符 = new System.Windows.Forms.Button();
            this.checkBox结尾换行符 = new System.Windows.Forms.CheckBox();
            this.checkBoxToDBC = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txt去除字符 = new System.Windows.Forms.TextBox();
            this.contextMenuStrip替换字符 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.contextMenuStrip正则表达式 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem打开配置文件 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem刷新配置 = new System.Windows.Forms.ToolStripMenuItem();
            this.checkBox换行符 = new System.Windows.Forms.CheckBox();
            this.checkBox首尾空格 = new System.Windows.Forms.CheckBox();
            this.tabPage标记 = new System.Windows.Forms.TabPage();
            this.listBox标记 = new System.Windows.Forms.ListBox();
            this.button标记 = new System.Windows.Forms.Button();
            this.tabPagez正则表达式提取字符 = new System.Windows.Forms.TabPage();
            this.listView内置正则表达式 = new System.Windows.Forms.ListView();
            this.columnHeader名称 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader正则表达式 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader返回第几组 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.button处理正则表达式 = new System.Windows.Forms.Button();
            this.textBox第几组 = new System.Windows.Forms.TextBox();
            this.textBox正则输入框 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.tabControl1.SuspendLayout();
            this.tabPage规整字符串.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage标记.SuspendLayout();
            this.tabPagez正则表达式提取字符.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage规整字符串);
            this.tabControl1.Controls.Add(this.tabPage标记);
            this.tabControl1.Controls.Add(this.tabPagez正则表达式提取字符);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.Padding = new System.Drawing.Point(0, 0);
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(382, 263);
            this.tabControl1.TabIndex = 12;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.TabControl_SelectedIndexChanged);
            // 
            // tabPage规整字符串
            // 
            this.tabPage规整字符串.Controls.Add(this.checkBox去除所有空格);
            this.tabPage规整字符串.Controls.Add(this.checkBox制表符);
            this.tabPage规整字符串.Controls.Add(this.button去干扰字符);
            this.tabPage规整字符串.Controls.Add(this.checkBox结尾换行符);
            this.tabPage规整字符串.Controls.Add(this.checkBoxToDBC);
            this.tabPage规整字符串.Controls.Add(this.groupBox1);
            this.tabPage规整字符串.Controls.Add(this.checkBox换行符);
            this.tabPage规整字符串.Controls.Add(this.checkBox首尾空格);
            this.tabPage规整字符串.Location = new System.Drawing.Point(4, 22);
            this.tabPage规整字符串.Name = "tabPage规整字符串";
            this.tabPage规整字符串.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage规整字符串.Size = new System.Drawing.Size(374, 237);
            this.tabPage规整字符串.TabIndex = 3;
            this.tabPage规整字符串.Text = "规整字符串";
            this.tabPage规整字符串.UseVisualStyleBackColor = true;
            // 
            // checkBox去除所有空格
            // 
            this.checkBox去除所有空格.AutoSize = true;
            this.checkBox去除所有空格.Location = new System.Drawing.Point(6, 28);
            this.checkBox去除所有空格.Name = "checkBox去除所有空格";
            this.checkBox去除所有空格.Size = new System.Drawing.Size(72, 16);
            this.checkBox去除所有空格.TabIndex = 9;
            this.checkBox去除所有空格.Text = "所有空格";
            this.checkBox去除所有空格.UseVisualStyleBackColor = true;
            // 
            // checkBox制表符
            // 
            this.checkBox制表符.AutoSize = true;
            this.checkBox制表符.Checked = true;
            this.checkBox制表符.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox制表符.Location = new System.Drawing.Point(186, 6);
            this.checkBox制表符.Name = "checkBox制表符";
            this.checkBox制表符.Size = new System.Drawing.Size(72, 16);
            this.checkBox制表符.TabIndex = 1;
            this.checkBox制表符.Text = "制表符\\t";
            this.checkBox制表符.UseVisualStyleBackColor = true;
            // 
            // button去干扰字符
            // 
            this.button去干扰字符.Location = new System.Drawing.Point(291, 28);
            this.button去干扰字符.Name = "button去干扰字符";
            this.button去干扰字符.Size = new System.Drawing.Size(75, 23);
            this.button去干扰字符.TabIndex = 8;
            this.button去干扰字符.Text = "清  除";
            this.button去干扰字符.UseVisualStyleBackColor = true;
            this.button去干扰字符.Click += new System.EventHandler(this.button_CleanInterferenceCharacters_Click);
            // 
            // checkBox结尾换行符
            // 
            this.checkBox结尾换行符.AutoSize = true;
            this.checkBox结尾换行符.Checked = true;
            this.checkBox结尾换行符.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox结尾换行符.Location = new System.Drawing.Point(84, 6);
            this.checkBox结尾换行符.Name = "checkBox结尾换行符";
            this.checkBox结尾换行符.Size = new System.Drawing.Size(96, 16);
            this.checkBox结尾换行符.TabIndex = 2;
            this.checkBox结尾换行符.Text = "结尾换行符\\n";
            this.checkBox结尾换行符.UseVisualStyleBackColor = true;
            // 
            // checkBoxToDBC
            // 
            this.checkBoxToDBC.AutoSize = true;
            this.checkBoxToDBC.Checked = true;
            this.checkBoxToDBC.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxToDBC.Location = new System.Drawing.Point(84, 28);
            this.checkBoxToDBC.Name = "checkBoxToDBC";
            this.checkBoxToDBC.Size = new System.Drawing.Size(60, 16);
            this.checkBoxToDBC.TabIndex = 7;
            this.checkBoxToDBC.Text = "半角化";
            this.checkBoxToDBC.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.txt去除字符);
            this.groupBox1.Location = new System.Drawing.Point(6, 52);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(364, 179);
            this.groupBox1.TabIndex = 6;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "要替换的字符  \"|\"或tab分隔  一条规则一行";
            // 
            // txt去除字符
            // 
            this.txt去除字符.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txt去除字符.ContextMenuStrip = this.contextMenuStrip替换字符;
            this.txt去除字符.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txt去除字符.Font = new System.Drawing.Font("新宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txt去除字符.Location = new System.Drawing.Point(3, 17);
            this.txt去除字符.Multiline = true;
            this.txt去除字符.Name = "txt去除字符";
            this.txt去除字符.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txt去除字符.Size = new System.Drawing.Size(358, 159);
            this.txt去除字符.TabIndex = 0;
            // 
            // contextMenuStrip替换字符
            //
            this.contextMenuStrip替换字符.Name = "contextMenuStrip替换字符";
            this.contextMenuStrip替换字符.Size = new System.Drawing.Size(61, 4);
            //
            // contextMenuStrip正则表达式
            //
            this.contextMenuStrip正则表达式.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem打开配置文件,
            this.toolStripMenuItem刷新配置});
            this.contextMenuStrip正则表达式.Name = "contextMenuStrip正则表达式";
            this.contextMenuStrip正则表达式.Size = new System.Drawing.Size(149, 48);
            //
            // toolStripMenuItem打开配置文件
            //
            this.toolStripMenuItem打开配置文件.Name = "toolStripMenuItem打开配置文件";
            this.toolStripMenuItem打开配置文件.Size = new System.Drawing.Size(148, 22);
            this.toolStripMenuItem打开配置文件.Text = "打开配置文件";
            this.toolStripMenuItem打开配置文件.Click += new System.EventHandler(this.toolStripMenuItem打开配置文件_Click);
            //
            // toolStripMenuItem刷新配置
            //
            this.toolStripMenuItem刷新配置.Name = "toolStripMenuItem刷新配置";
            this.toolStripMenuItem刷新配置.Size = new System.Drawing.Size(148, 22);
            this.toolStripMenuItem刷新配置.Text = "刷新配置";
            this.toolStripMenuItem刷新配置.Click += new System.EventHandler(this.toolStripMenuItem刷新配置_Click);
            // 
            // checkBox换行符
            // 
            this.checkBox换行符.AutoSize = true;
            this.checkBox换行符.Location = new System.Drawing.Point(264, 6);
            this.checkBox换行符.Name = "checkBox换行符";
            this.checkBox换行符.Size = new System.Drawing.Size(72, 16);
            this.checkBox换行符.TabIndex = 2;
            this.checkBox换行符.Text = "换行符\\n";
            this.checkBox换行符.UseVisualStyleBackColor = true;
            // 
            // checkBox首尾空格
            // 
            this.checkBox首尾空格.AutoSize = true;
            this.checkBox首尾空格.Checked = true;
            this.checkBox首尾空格.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox首尾空格.Location = new System.Drawing.Point(6, 6);
            this.checkBox首尾空格.Name = "checkBox首尾空格";
            this.checkBox首尾空格.Size = new System.Drawing.Size(72, 16);
            this.checkBox首尾空格.TabIndex = 3;
            this.checkBox首尾空格.Text = "首尾空格";
            this.checkBox首尾空格.UseVisualStyleBackColor = true;
            // 
            // tabPage标记
            // 
            this.tabPage标记.Controls.Add(this.listBox标记);
            this.tabPage标记.Controls.Add(this.button标记);
            this.tabPage标记.Location = new System.Drawing.Point(4, 22);
            this.tabPage标记.Margin = new System.Windows.Forms.Padding(0);
            this.tabPage标记.Name = "tabPage标记";
            this.tabPage标记.Size = new System.Drawing.Size(374, 237);
            this.tabPage标记.TabIndex = 2;
            this.tabPage标记.Text = "标记";
            this.tabPage标记.UseVisualStyleBackColor = true;
            // 
            // listBox标记
            // 
            this.listBox标记.FormattingEnabled = true;
            this.listBox标记.ItemHeight = 12;
            this.listBox标记.Items.AddRange(new object[] {
            "第1个值",
            "最后1个值",
            "---------------",
            "每个重复值:所有",
            "每个重复值:第1个",
            "每个重复值:第2个",
            "每个重复值:第2个后面部分"});
            this.listBox标记.Location = new System.Drawing.Point(3, 3);
            this.listBox标记.Name = "listBox标记";
            this.listBox标记.Size = new System.Drawing.Size(368, 208);
            this.listBox标记.TabIndex = 13;
            this.listBox标记.DoubleClick += new System.EventHandler(this.ListBoxMark_DoubleClick);
            // 
            // button标记
            // 
            this.button标记.Location = new System.Drawing.Point(3, 212);
            this.button标记.Name = "button标记";
            this.button标记.Size = new System.Drawing.Size(368, 22);
            this.button标记.TabIndex = 12;
            this.button标记.Text = "标记";
            this.button标记.UseVisualStyleBackColor = true;
            this.button标记.Click += new System.EventHandler(this.button_MarkSelectedCellValues_Click);
            // 
            // tabPagez正则表达式提取字符
            // 
            this.tabPagez正则表达式提取字符.Controls.Add(this.listView内置正则表达式);
            this.tabPagez正则表达式提取字符.Controls.Add(this.button处理正则表达式);
            this.tabPagez正则表达式提取字符.Controls.Add(this.textBox第几组);
            this.tabPagez正则表达式提取字符.Controls.Add(this.textBox正则输入框);
            this.tabPagez正则表达式提取字符.Controls.Add(this.label4);
            this.tabPagez正则表达式提取字符.Controls.Add(this.label3);
            this.tabPagez正则表达式提取字符.Controls.Add(this.label2);
            this.tabPagez正则表达式提取字符.Location = new System.Drawing.Point(4, 22);
            this.tabPagez正则表达式提取字符.Name = "tabPagez正则表达式提取字符";
            this.tabPagez正则表达式提取字符.Size = new System.Drawing.Size(374, 237);
            this.tabPagez正则表达式提取字符.TabIndex = 4;
            this.tabPagez正则表达式提取字符.Text = "正则提取字符";
            this.tabPagez正则表达式提取字符.UseVisualStyleBackColor = true;
            // 
            // listView内置正则表达式
            //
            this.listView内置正则表达式.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader名称,
            this.columnHeader正则表达式,
            this.columnHeader返回第几组});
            this.listView内置正则表达式.ContextMenuStrip = this.contextMenuStrip正则表达式;
            this.listView内置正则表达式.FullRowSelect = true;
            this.listView内置正则表达式.HideSelection = false;
            this.listView内置正则表达式.Location = new System.Drawing.Point(3, 110);
            this.listView内置正则表达式.Name = "listView内置正则表达式";
            this.listView内置正则表达式.Size = new System.Drawing.Size(368, 124);
            this.listView内置正则表达式.TabIndex = 11;
            this.listView内置正则表达式.UseCompatibleStateImageBehavior = false;
            this.listView内置正则表达式.View = System.Windows.Forms.View.Details;
            this.listView内置正则表达式.SelectedIndexChanged += new System.EventHandler(this.ListViewRegex_SelectedIndexChanged);
            this.listView内置正则表达式.DoubleClick += new System.EventHandler(this.ListViewRegex_DoubleClick);
            // 
            // columnHeader名称
            // 
            this.columnHeader名称.Text = "名称";
            this.columnHeader名称.Width = 156;
            // 
            // columnHeader正则表达式
            // 
            this.columnHeader正则表达式.Text = "正则表达式";
            this.columnHeader正则表达式.Width = 146;
            // 
            // columnHeader返回第几组
            // 
            this.columnHeader返回第几组.Text = "组";
            this.columnHeader返回第几组.Width = 29;
            // 
            // button处理正则表达式
            // 
            this.button处理正则表达式.Location = new System.Drawing.Point(297, 47);
            this.button处理正则表达式.Name = "button处理正则表达式";
            this.button处理正则表达式.Size = new System.Drawing.Size(74, 39);
            this.button处理正则表达式.TabIndex = 9;
            this.button处理正则表达式.Text = "处  理";
            this.button处理正则表达式.UseVisualStyleBackColor = true;
            this.button处理正则表达式.Click += new System.EventHandler(this.button_ProcessRegexMatchForCells_Click);
            // 
            // textBox第几组
            // 
            this.textBox第几组.Location = new System.Drawing.Point(297, 21);
            this.textBox第几组.Multiline = true;
            this.textBox第几组.Name = "textBox第几组";
            this.textBox第几组.Size = new System.Drawing.Size(74, 20);
            this.textBox第几组.TabIndex = 12;
            this.textBox第几组.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.TextBoxGroupIndex_KeyPress);
            // 
            // textBox正则输入框
            // 
            this.textBox正则输入框.Location = new System.Drawing.Point(3, 19);
            this.textBox正则输入框.Multiline = true;
            this.textBox正则输入框.Name = "textBox正则输入框";
            this.textBox正则输入框.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox正则输入框.Size = new System.Drawing.Size(288, 67);
            this.textBox正则输入框.TabIndex = 0;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(3, 6);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(263, 12);
            this.label4.TabIndex = 13;
            this.label4.Text = "注:注意存档文件，避免正则表达式错误无法回退";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(295, 6);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(71, 12);
            this.label3.TabIndex = 10;
            this.label3.Text = "返回第几组:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(3, 95);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 10;
            this.label2.Text = "预设内置项：";
            // 
            // frm字符处理
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(382, 263);
            this.Controls.Add(this.tabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm字符处理";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "字符处理";
            this.Load += new System.EventHandler(this.frm提取字符_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage规整字符串.ResumeLayout(false);
            this.tabPage规整字符串.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage标记.ResumeLayout(false);
            this.tabPagez正则表达式提取字符.ResumeLayout(false);
            this.tabPagez正则表达式提取字符.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.Button button标记;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ListBox listBox标记;
        public System.Windows.Forms.TabPage tabPage标记;
        public System.Windows.Forms.TabPage tabPage规整字符串;
        public System.Windows.Forms.TabControl tabControl1;
        public System.Windows.Forms.CheckBox checkBox去除所有空格;
        public System.Windows.Forms.CheckBox checkBox首尾空格;
        public System.Windows.Forms.CheckBox checkBoxToDBC;
        public System.Windows.Forms.CheckBox checkBox制表符;
        public System.Windows.Forms.CheckBox checkBox结尾换行符;
        public System.Windows.Forms.CheckBox checkBox换行符;
        public System.Windows.Forms.Button button去干扰字符;
        private System.Windows.Forms.TextBox txt去除字符;
        public System.Windows.Forms.Button button处理正则表达式;
        private System.Windows.Forms.ColumnHeader columnHeader名称;
        private System.Windows.Forms.ColumnHeader columnHeader正则表达式;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ColumnHeader columnHeader返回第几组;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        public System.Windows.Forms.TextBox textBox正则输入框;
        public System.Windows.Forms.TextBox textBox第几组;
        public System.Windows.Forms.ListView listView内置正则表达式;
        public System.Windows.Forms.TabPage tabPagez正则表达式提取字符;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip替换字符;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip正则表达式;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem打开配置文件;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem刷新配置;
    }
}