﻿using System;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// 自动重置标签控件，可在指定时间后自动恢复默认文本
    /// </summary>
    public class ETAutoResetLabel : Label
    {
        private Timer _resetTimer;
        private string _defaultText = string.Empty;
        private int _resetDelay = 5000;

        /// <summary>
        /// 初始化 AutoResetLabel 的新实例
        /// </summary>
        public ETAutoResetLabel()
        {
            InitializeTimer();
        }

        /// <summary>
        /// 获取或设置默认文本
        /// </summary>
        public string DefaultText
        {
            get => _defaultText;
            set
            {
                _defaultText = value;
                Text = _defaultText;
            }
        }

        /// <summary>
        /// 获取或设置重置延迟时间（毫秒）
        /// </summary>
        public int DefaultDelay
        {
            get => _resetDelay;
            set
            {
                _resetDelay = value;
                _resetTimer.Interval = _resetDelay;
            }
        }

        /// <summary>
        /// 获取或设置标签文本，非默认文本时启动重置计时器
        /// </summary>
        public new string Text
        {
            get => base.Text;
            set
            {
                base.Text = value;
                if (value != _defaultText)
                {
                    RestartResetTimer();
                }
            }
        }

        /// <summary>
        /// 初始化重置计时器
        /// </summary>
        private void InitializeTimer()
        {
            _resetTimer = new Timer
            {
                Interval = _resetDelay
            };
            _resetTimer.Tick += ResetTimer_Tick;
        }

        /// <summary>
        /// 重新启动重置计时器
        /// </summary>
        private void RestartResetTimer()
        {
            _resetTimer.Stop();
            _resetTimer.Start();
        }

        /// <summary>
        /// 重置计时器触发事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void ResetTimer_Tick(object sender, EventArgs e)
        {
            _resetTimer.Stop();
            base.Text = _defaultText;
        }

        /// <summary>
        /// 释放由 AutoResetLabel 使用的资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _resetTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}