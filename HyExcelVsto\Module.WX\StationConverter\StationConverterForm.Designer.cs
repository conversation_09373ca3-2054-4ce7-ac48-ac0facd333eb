using System;
using System.Drawing;
using System.Windows.Forms;
using ET.Controls;

namespace HyExcelVsto.Module.WX.StationConverter
{
    partial class StationConverterForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
                ReleaseCustomResources();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCreateWorkbook = new System.Windows.Forms.Button();
            this.btnExecuteConversion = new System.Windows.Forms.Button();
            this.lblConverterDescription = new System.Windows.Forms.Label();
            this.etLogDisplayControlConverter = new ET.Controls.ETLogDisplayControl();
            this.SuspendLayout();
            // 
            // btnCreateWorkbook
            // 
            this.btnCreateWorkbook.Location = new System.Drawing.Point(20, 80);
            this.btnCreateWorkbook.Name = "btnCreateWorkbook";
            this.btnCreateWorkbook.Size = new System.Drawing.Size(150, 35);
            this.btnCreateWorkbook.TabIndex = 0;
            this.btnCreateWorkbook.Text = "创建空白工作簿";
            this.btnCreateWorkbook.UseVisualStyleBackColor = true;
            this.btnCreateWorkbook.Click += new System.EventHandler(this.BtnCreateWorkbook_Click);
            // 
            // btnExecuteConversion
            // 
            this.btnExecuteConversion.Location = new System.Drawing.Point(190, 80);
            this.btnExecuteConversion.Name = "btnExecuteConversion";
            this.btnExecuteConversion.Size = new System.Drawing.Size(150, 35);
            this.btnExecuteConversion.TabIndex = 1;
            this.btnExecuteConversion.Text = "执行站点转换";
            this.btnExecuteConversion.UseVisualStyleBackColor = true;
            this.btnExecuteConversion.Click += new System.EventHandler(this.BtnExecuteConversion_Click);
            // 
            // lblConverterDescription
            // 
            this.lblConverterDescription.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblConverterDescription.Location = new System.Drawing.Point(20, 15);
            this.lblConverterDescription.Name = "lblConverterDescription";
            this.lblConverterDescription.Size = new System.Drawing.Size(550, 60);
            this.lblConverterDescription.TabIndex = 2;
            this.lblConverterDescription.Text = "站点数据转换功能：\r\n• 创建空白工作簿：自动创建包含\"数据来源\"和\"数据输出\"表的标准工作簿\r\n• 执行站点转换：将逻辑站点数据转换为物理站点汇总数据\r\n• 支" +
    "持智能站点分组、动态频段配置和高性能GPS算法";
            // 
            // etLogDisplayControlConverter
            // 
            this.etLogDisplayControlConverter.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etLogDisplayControlConverter.AutoScrollToBottom = true;
            this.etLogDisplayControlConverter.CurrentLogLevel = ET.Controls.ETLogDisplayControl.LogLevel.Info;
            this.etLogDisplayControlConverter.CustomInitialMessage = null;
            this.etLogDisplayControlConverter.Location = new System.Drawing.Point(20, 130);
            this.etLogDisplayControlConverter.LogBackColor = System.Drawing.Color.White;
            this.etLogDisplayControlConverter.LogFont = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.etLogDisplayControlConverter.LogForeColor = System.Drawing.Color.Black;
            this.etLogDisplayControlConverter.Margin = new System.Windows.Forms.Padding(2);
            this.etLogDisplayControlConverter.MaxLogLines = 1000;
            this.etLogDisplayControlConverter.Name = "etLogDisplayControlConverter";
            this.etLogDisplayControlConverter.ShowInitialMessage = true;
            this.etLogDisplayControlConverter.ShowLogLevel = true;
            this.etLogDisplayControlConverter.ShowTimestamp = true;
            this.etLogDisplayControlConverter.Size = new System.Drawing.Size(550, 330);
            this.etLogDisplayControlConverter.TabIndex = 3;
            // 
            // StationConverterForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 472);
            this.Controls.Add(this.btnCreateWorkbook);
            this.Controls.Add(this.btnExecuteConversion);
            this.Controls.Add(this.lblConverterDescription);
            this.Controls.Add(this.etLogDisplayControlConverter);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MinimumSize = new System.Drawing.Size(600, 510);
            this.Name = "StationConverterForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "站点数据转换器";
            this.ResumeLayout(false);

        }

        #endregion

        private Button btnCreateWorkbook;
        private Button btnExecuteConversion;
        private Label lblConverterDescription;
        private ET.Controls.ETLogDisplayControl etLogDisplayControlConverter;
    }
}
