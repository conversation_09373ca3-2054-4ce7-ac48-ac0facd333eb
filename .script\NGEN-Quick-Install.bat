@echo off
echo ===============================================
echo NGEN Quick Install for HyExcelVsto
echo ===============================================
echo.
echo This script will install native images for faster loading.
echo You must run this as Administrator!
echo.
pause

REM Check for Administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - OK
    echo.
) else (
    echo ERROR: This script must be run as Administrator!
    echo Right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Run the PowerShell script
echo Starting NGEN installation...
echo.
PowerShell -ExecutionPolicy Bypass -File "%~dp0NGEN-Install-VSTO.ps1" -BuildConfiguration Debug

echo.
echo ===============================================
echo NGEN Installation Completed
echo ===============================================
echo.
echo Next steps:
echo 1. Restart Excel
echo 2. Test VSTO plugin loading speed
echo 3. Run NGEN-Verify-VSTO.ps1 to check status
echo.
pause
