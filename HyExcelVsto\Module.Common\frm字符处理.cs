﻿using ET;
using ET.ETLicense.Extensions;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 字符处理窗体类，提供字符标记、正则处理和干扰字符清除等功能
    /// </summary>
    /// <remarks>
    /// 主要功能：
    /// 1. 标记选中单元格中的特定值
    /// 2. 处理正则表达式匹配
    /// 3. 清除选中单元格中的干扰字符
    /// </remarks>
    public partial class frm字符处理 : Form
    {
        /// <summary>
        /// 窗体高度
        /// </summary>
        private int _formHeight;

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 正则表达式配置读取器
        /// </summary>
        private ETSectionConfigReader _regexConfigReader;

        /// <summary>
        /// 正则表达式规则数据结构
        /// </summary>
        public class RegexRule
        {
            /// <summary>
            /// 规则显示名称
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// 正则表达式模式
            /// </summary>
            public string Pattern { get; set; }

            /// <summary>
            /// 返回第几组
            /// </summary>
            public int Group { get; set; }

            /// <summary>
            /// 规则描述
            /// </summary>
            public string Description { get; set; }

            /// <summary>
            /// 构造函数
            /// </summary>
            public RegexRule(string name, string pattern, int group = 0, string description = "")
            {
                Name = name;
                Pattern = pattern;
                Group = group;
                Description = description;
            }
        }

        /// <summary>
        /// 标记选中单元格中的特定值，根据选择的模式高亮显示相应的单元格
        /// </summary>
        /// <param name="sender">触发事件的控件对象</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取当前选中的单元格范围
        /// 2. 验证选择范围和标记模式的有效性
        /// 3. 根据选择的模式（第1个值、重复值等）确定要高亮的单元格
        /// 4. 应用条件格式进行高亮显示 支持的标记模式：第1个值、最后1个值、每个重复值的不同组合
        /// </remarks>
        /// <exception cref="ETException">当选择范围无效或标记模式未选择时抛出</exception>
        private void button_MarkSelectedCellValues_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的单元格范围
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectionRange);

                // 验证并获取用户选择的标记模式
                string selectedMode = ValidateAndGetSelectedMode();

                // 设置Excel为快速模式，提高处理性能
                ETExcelExtensions.SetAppFastMode();

                // 构建值与地址的映射字典，用于分析重复值
                Dictionary<string, List<string>> valueAddressMap = selectionRange.GetValueAddressListDic();

                // 根据选择的模式获取需要高亮的单元格范围
                List<Range> rangesToHighlight = GetRangesToHighlight(valueAddressMap, selectedMode);

                // 一次性调用函数进行标色，使用提醒色高亮显示
                ETExcelExtensions.Format条件格式警示色(rangesToHighlight, EnumWarningColor.提醒);
            }
            catch (ETException ex)
            {
                // 显示业务异常信息
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                // 包装系统异常为业务异常
                throw new ETException("标记处理失败", "单元格标记操作", ex);
            }
            finally
            {
                // 恢复Excel正常模式，确保界面响应
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 验证选择范围是否有效，确保用户已选择要处理的单元格
        /// </summary>
        /// <param name="selectionRange">用户选择的单元格范围对象</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 检查选择范围是否为null
        /// 2. 如果无效则抛出友好的提示异常 用于确保后续操作有有效的单元格范围可以处理
        /// </remarks>
        /// <exception cref="ETException">当选择范围为null时抛出，提示用户选择单元格范围</exception>
        private void ValidateSelectionRange(Range selectionRange)
        {
            // 检查选择范围是否为空，如果为空则提示用户
            if (selectionRange == null)
            {
                throw new ETException("请先选择要处理的单元格范围");
            }
        }

        /// <summary>
        /// 验证并获取用户选中的标记模式，确保已选择有效的处理模式
        /// </summary>
        /// <returns>用户选择的标记模式字符串</returns>
        /// <remarks>
        /// 建议函数名: ValidateAndGetSelectedMode 该方法会：
        /// 1. 从标记列表框获取当前选中项
        /// 2. 验证选中项是否有效
        /// 3. 返回选中的模式字符串供后续处理使用 支持的模式包括：第1个值、最后1个值、各种重复值处理模式
        /// </remarks>
        /// <exception cref="ETException">当未选择任何标记模式时抛出</exception>
        private string ValidateAndGetSelectedMode()
        {
            // 获取列表框中当前选中的标记模式
            string selectedMode = listBox标记.SelectedItem?.ToString();

            // 验证是否已选择有效的模式
            if (string.IsNullOrEmpty(selectedMode))
            {
                throw new ETException("请先选择标记模式");
            }

            return selectedMode;
        }

        /// <summary>
        /// 根据用户选择的标记模式，从值地址映射中筛选出需要高亮显示的单元格范围
        /// </summary>
        /// <param name="valueAddressMap">单元格值与其地址列表的映射字典，键为单元格值，值为包含该值的所有单元格地址</param>
        /// <param name="selectedMode">用户选择的标记模式字符串</param>
        /// <returns>需要进行高亮显示的单元格范围对象列表</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 遍历值地址映射字典中的每个值及其对应的地址列表
        /// 2. 根据不同的标记模式筛选出符合条件的单元格地址
        /// 3. 将地址转换为Range对象并添加到结果列表中
        ///
        /// 支持的标记模式：
        /// - "第1个值": 标记每个值的第一个出现位置
        /// - "最后1个值": 标记每个值的最后一个出现位置
        /// - "每个重复值:所有": 标记所有重复值的所有出现位置
        /// - "每个重复值:第1个": 标记重复值的第一个出现位置
        /// - "每个重复值:第2个": 标记重复值的第二个出现位置
        /// - "每个重复值:第2个后面部分": 标记重复值从第二个开始的所有位置
        /// </remarks>
        private List<Range> GetRangesToHighlight(Dictionary<string, List<string>> valueAddressMap, string selectedMode)
        {
            // 初始化结果列表，用于存储需要高亮的单元格范围
            List<Range> rangesToHighlight = [];

            // 遍历值地址映射字典中的每个键值对
            foreach (KeyValuePair<string, List<string>> valuePair in valueAddressMap)
            {
                // 获取当前值对应的所有单元格地址列表
                List<string> addresses = valuePair.Value;

                // 根据选择的标记模式处理地址列表
                switch (selectedMode)
                {
                    case "第1个值":
                        // 标记每个值的第一个出现位置
                        rangesToHighlight.Add(addresses[0].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:所有":
                        // 只有当值出现多次时才标记所有位置
                        if (addresses.Count > 1)
                            rangesToHighlight.AddRange(addresses.Select(address => address.GetRangeByFormulaParser()));
                        break;

                    case "每个重复值:第1个":
                        // 只有当值出现多次时才标记第一个位置
                        if (addresses.Count > 1)
                            rangesToHighlight.Add(addresses[0].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:第2个":
                        // 只有当值出现多次且至少有第二个时才标记第二个位置
                        if (addresses.Count > 1)
                            rangesToHighlight.Add(addresses[1].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:第2个后面部分":
                        // 只有当值出现多次时才标记从第二个开始的所有位置
                        if (addresses.Count > 1)
                            rangesToHighlight.AddRange(addresses.Skip(1).Select(address => address.GetRangeByFormulaParser()));
                        break;

                    case "最后1个值":
                        // 标记每个值的最后一个出现位置
                        rangesToHighlight.Add(addresses[addresses.Count - 1].GetRangeByFormulaParser());
                        break;
                }
            }

            return rangesToHighlight;
        }

        /// <summary>
        /// 使用正则表达式处理选中单元格的内容，提取匹配的文本并替换原内容
        /// </summary>
        /// <param name="sender">触发事件的控件对象</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取当前选中的单元格范围
        /// 2. 验证选择范围和正则表达式的有效性
        /// 3. 对每个单元格应用正则表达式匹配
        /// 4. 将匹配结果替换原单元格内容 支持指定返回第几个匹配组，默认返回第0组（整个匹配）
        /// </remarks>
        /// <exception cref="ETException">当选择范围无效或正则表达式为空时抛出</exception>
        public void button_ProcessRegexMatchForCells_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的单元格范围
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectionRange);

                // 验证并获取用户输入的正则表达式
                string regexPattern = ValidateAndGetRegexPattern();

                // 设置Excel为快速模式，提高处理性能
                ETExcelExtensions.SetAppFastMode(true);

                // 对选中范围应用正则表达式处理
                ProcessRegexMatchForRange(selectionRange, regexPattern);
            }
            catch (ETException ex)
            {
                // 显示业务异常信息
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                // 包装系统异常为业务异常
                throw new ETException("正则表达式处理失败", "单元格内容处理", ex);
            }
            finally
            {
                // 恢复Excel正常模式，确保界面响应
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 验证并获取用户输入的正则表达式模式，确保表达式有效
        /// </summary>
        /// <returns>验证通过的正则表达式模式字符串</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 从正则表达式输入框获取用户输入
        /// 2. 验证输入是否为空或仅包含空白字符
        /// 3. 返回有效的正则表达式字符串供后续使用
        /// </remarks>
        /// <exception cref="ETException">当正则表达式输入为空或仅包含空白字符时抛出</exception>
        private string ValidateAndGetRegexPattern()
        {
            // 获取用户在正则表达式输入框中输入的内容
            string regexPattern = textBox正则输入框.Text;

            // 验证正则表达式是否为空
            if (string.IsNullOrWhiteSpace(regexPattern))
            {
                throw new ETException("请输入正则表达式");
            }

            return regexPattern;
        }

        /// <summary>
        /// 对指定单元格范围内的每个单元格应用正则表达式匹配，提取匹配内容并替换原值
        /// </summary>
        /// <param name="selectionRange">要处理的单元格范围对象</param>
        /// <param name="regexPattern">要应用的正则表达式模式字符串</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取用户指定的匹配组索引（默认为0，即整个匹配）
        /// 2. 遍历范围内的每个单元格
        /// 3. 跳过空单元格和无内容的单元格
        /// 4. 对每个有效单元格应用正则表达式匹配
        /// 5. 将匹配结果（指定组的内容）替换原单元格值 如果匹配失败，单元格将被设置为空字符串
        /// </remarks>
        private void ProcessRegexMatchForRange(Range selectionRange, string regexPattern)
        {
            // 如果正则表达式为空，直接返回
            if (regexPattern.IsNullOrWhiteSpace()) return;

            // 获取用户指定的匹配组索引，默认为0（整个匹配），用户输入从1开始计数
            int groupIndex = string.IsNullOrWhiteSpace(textBox第几组.Text) ? 0 : Math.Max(0, int.Parse(textBox第几组.Text) - 1);

            // 遍历选中范围内的每个单元格
            foreach (Range cell in selectionRange.Cells)
            {
                // 跳过空单元格
                if (cell.IsCellEmpty()) continue;

                // 获取单元格的字符串值
                string cellValue = cell.Value?.ToString();
                if (cellValue.IsNullOrEmpty()) continue;

                // 使用正则表达式匹配文本，获取指定组的内容
                string matchResult = ETString.RegexMatchText(cellValue, regexPattern, groupIndex);

                // 将匹配结果写回单元格，如果匹配失败则设置为空字符串
                cell.Value = matchResult ?? string.Empty;
            }
        }

        /// <summary>
        /// 清除选中单元格中的各种干扰字符，包括空格、换行符、制表符等
        /// </summary>
        /// <param name="sender">触发事件的控件对象</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取当前选中的单元格范围
        /// 2. 验证选择范围的有效性
        /// 3. 根据用户勾选的选项清理各种干扰字符
        /// 4. 应用自定义替换规则进行进一步清理 支持的清理选项：首尾空格、所有空格、换行符、制表符、半角化等
        /// </remarks>
        /// <exception cref="ETException">当选择范围无效时抛出</exception>
        public void button_CleanInterferenceCharacters_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的单元格范围
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectedRange);

                // 设置Excel为快速模式，提高处理性能
                ETExcelExtensions.SetAppFastMode();

                // 处理选中范围内的干扰字符
                ProcessInterferenceCharacters(selectedRange);
            }
            catch (ETException ex)
            {
                // 显示业务异常信息
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                // 包装系统异常为业务异常
                throw new ETException("清除干扰字符失败", "单元格内容清理", ex);
            }
            finally
            {
                // 恢复Excel正常模式，确保界面响应
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 遍历指定单元格范围，对每个文本单元格应用干扰字符清理规则
        /// </summary>
        /// <param name="selectedRange">要处理的单元格范围对象</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 遍历范围内的每个单元格
        /// 2. 跳过非文本类型的单元格
        /// 3. 对每个有效的文本单元格依次应用：
        /// - 基础字符清理规则（空格、换行符等）
        /// - 自定义替换规则（用户定义的替换对）
        /// 4. 将处理后的结果写回单元格
        /// </remarks>
        private void ProcessInterferenceCharacters(Range selectedRange)
        {
            // 遍历选中范围内的每个单元格
            foreach (Range cell in selectedRange.Cells)
            {
                // 跳过非文本类型的单元格（如数字、日期等）
                if (cell.IsCellNonText()) continue;

                // 获取单元格的字符串值
                string cellValue = cell.Value?.ToString();
                if (cellValue.IsNullOrEmpty()) continue;

                // 应用基础字符清理规则（根据复选框状态）
                cellValue = ApplyCharacterCleanupRules(cellValue);

                // 应用用户自定义的替换规则
                cellValue = ApplyCustomReplacements(cellValue);

                // 将处理后的值写回单元格
                cell.Value = cellValue;
            }
        }

        /// <summary>
        /// 根据用户勾选的选项，对输入字符串应用各种字符清理规则
        /// </summary>
        /// <param name="input">要清理的输入字符串</param>
        /// <returns>应用清理规则后的字符串</returns>
        /// <remarks>
        /// 该方法会根据界面复选框的勾选状态，依次应用以下清理规则：
        /// 1. 首尾空格清理 - 移除字符串开头和结尾的空白字符
        /// 2. 换行符清理 - 移除所有换行符（\r\n, \r, \n）
        /// 3. 制表符清理 - 移除所有制表符（\t）
        /// 4. 结尾换行符清理 - 移除字符串结尾的换行符
        /// 5. 半角化处理 - 将全角字符转换为半角字符
        /// 6. 所有空格清理 - 移除字符串中的所有空白字符 处理顺序很重要，确保各规则之间不会产生冲突
        /// </remarks>
        private string ApplyCharacterCleanupRules(string input)
        {
            // 如果输入为空，直接返回
            if (input.IsNullOrEmpty()) return input;

            // 根据复选框状态依次应用各种清理规则
            if (checkBox首尾空格.Checked) input = input.Trim();                    // 清理首尾空格
            if (checkBox换行符.Checked) input = input.RemoveNewLines();             // 清理换行符
            if (checkBox制表符.Checked) input = input.Replace("\t", string.Empty);  // 清理制表符
            if (checkBox结尾换行符.Checked) input = input.TrimEnd();               // 清理结尾换行符
            if (checkBoxToDBC.Checked) input = input.ToDBC().ToEn();               // 半角化处理
            if (checkBox去除所有空格.Checked) input = input.RemoveWhiteSpace();      // 清理所有空格

            return input;
        }

        /// <summary>
        /// 根据用户在文本框中定义的自定义替换规则，对输入字符串进行批量替换操作
        /// </summary>
        /// <param name="input">要进行替换处理的输入字符串</param>
        /// <returns>应用所有自定义替换规则后的字符串</returns>
        /// <remarks>
        /// 该方法会：
        /// 1. 从txt去除字符文本框获取用户定义的替换规则
        /// 2. 兼容处理不同操作系统的换行符（\r\n, \r, \n）
        /// 3. 解析每行规则，支持|和制表符作为分隔符
        /// 4. 对输入字符串依次应用所有有效的替换规则
        ///
        /// 规则格式：
        /// - 每行一个替换规则
        /// - 格式：搜索文本|替换文本 或 搜索文本[TAB]替换文本
        /// - 如果只有搜索文本，则替换为空字符串（删除效果）
        /// - 空行和仅包含空白字符的行会被忽略
        ///
        /// 示例规则： 旧文本|新文本 删除这个| 空格|
        /// </remarks>
        private string ApplyCustomReplacements(string input)
        {
            // 如果输入为空，直接返回
            if (input.IsNullOrEmpty()) return input;

            // 获取用户在文本框中输入的自定义替换规则
            string customRules = txt去除字符.Text;
            if (customRules.IsNullOrWhiteSpace()) return input;

            // 使用多种换行符进行分割，确保兼容不同操作系统的换行符格式
            // Windows: \r\n, Unix/Linux: \n, Mac: \r
            string[] lines = customRules.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

            // 遍历每一行替换规则
            foreach (string line in lines)
            {
                // 去除行首尾的空白字符
                string trimmedLine = line.Trim();
                if (trimmedLine.IsNullOrWhiteSpace()) continue;

                // 使用|或制表符分割替换规则：搜索文本|替换文本
                string[] replacementParts = trimmedLine.Split(new[] { "|", "\t" }, StringSplitOptions.None);

                // 如果分割后没有内容，跳过此行
                if (replacementParts.Length == 0) continue;

                // 获取搜索文本和替换文本
                string searchText = replacementParts[0];
                string replaceText = replacementParts.Length > 1 ? replacementParts[1] : string.Empty;

                // 执行字符串替换操作
                if (!searchText.IsNullOrEmpty())
                {
                    input = input.Replace(searchText, replaceText);
                }
            }

            return input;
        }

        /// <summary>
        /// 双击标记列表项时自动触发标记操作，提供快捷操作方式
        /// </summary>
        /// <param name="sender">触发事件的列表框控件</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 响应用户双击标记列表项的操作
        /// 2. 直接调用标记按钮的点击事件处理方法
        /// 3. 为用户提供更便捷的操作方式，无需先选择再点击按钮
        /// </remarks>
        private void ListBoxMark_DoubleClick(object sender, EventArgs e)
        {
            // 直接调用标记按钮的点击事件，实现快捷操作
            button_MarkSelectedCellValues_Click(sender, e);
        }

        /// <summary>
        /// 双击内置正则表达式列表项时自动应用选中的正则表达式规则
        /// </summary>
        /// <param name="sender">触发事件的列表视图控件</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 获取用户双击选中的正则表达式项
        /// 2. 自动填充正则表达式输入框和组号输入框
        /// 3. 立即执行正则表达式处理操作
        /// 4. 为用户提供一键应用内置正则表达式的便捷功能
        /// </remarks>
        private void ListViewRegex_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的列表项
                ListView.SelectedListViewItemCollection selectedItems = listView内置正则表达式.SelectedItems;
                if (selectedItems.Count == 0) return;

                // 获取选中项的详细信息
                ListViewItem selectedItem = selectedItems[0];

                // 从Tag中获取RegexRule对象（优先使用）
                if (selectedItem.Tag is RegexRule rule)
                {
                    // 使用配置文件中的规则数据
                    textBox正则输入框.Text = rule.Pattern;
                    textBox第几组.Text = rule.Group.ToString();

                    ETLogManager.Info(this, $"应用正则表达式规则：{rule.Name} - {rule.Pattern}");
                }
                else
                {
                    // 兼容旧的硬编码方式（从SubItems读取）
                    textBox正则输入框.Text = selectedItem.SubItems.Count > 1 ? selectedItem.SubItems[1].Text : string.Empty;
                    textBox第几组.Text = selectedItem.SubItems.Count > 2 ? selectedItem.SubItems[2].Text : "0";

                    ETLogManager.Warning(this, "使用兼容模式读取正则表达式规则");
                }

                // 立即执行正则表达式处理
                button_ProcessRegexMatchForCells_Click(sender, e);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "双击应用正则表达式规则失败", ex);
                MessageBox.Show($"应用正则表达式规则失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 限制"第几组"文本框只能输入数字字符，确保输入的有效性
        /// </summary>
        /// <param name="sender">触发事件的文本框控件</param>
        /// <param name="e">按键事件参数，包含用户按下的字符信息</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 检查用户输入的字符是否为数字或退格键
        /// 2. 如果是非法字符（非数字且非退格），则阻止输入
        /// 3. 确保组号输入框只接受有效的数字输入
        /// 4. 提高用户体验，避免输入无效数据导致的错误
        /// </remarks>
        private void TextBoxGroupIndex_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 检查输入字符：只允许退格键（\b）和数字字符（0-9）
            if (!(e.KeyChar == '\b' || (e.KeyChar >= '0' && e.KeyChar <= '9')))
            {
                // 如果是非法字符，设置Handled为true阻止输入
                e.Handled = true;
            }
        }

        /// <summary>
        /// 清空自定义替换规则文本框的内容，重置用户的替换规则设置
        /// </summary>
        /// <param name="sender">触发事件的菜单项控件</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 响应右键菜单中"清空"选项的点击事件
        /// 2. 将自定义替换规则文本框的内容设置为空字符串
        /// 3. 为用户提供快速清空替换规则的便捷功能
        /// </remarks>
        private void ClearCustomRules_Click(object sender, EventArgs e)
        {
            // 清空自定义替换规则文本框
            txt去除字符.Text = string.Empty;
        }

        /// <summary>
        /// 初始化字符处理窗体，设置基本属性和Excel应用程序引用
        /// </summary>
        /// <remarks>
        /// 建议函数名: InitializeCharacterProcessingForm 该构造函数会：
        /// 1. 调用InitializeComponent()初始化窗体控件
        /// 2. 获取Excel应用程序实例的引用
        /// 3. 保存窗体的初始高度，用于后续的界面调整
        /// 4. 为窗体的正常运行做好基础准备工作
        /// </remarks>
        public frm字符处理()
        {
            // 初始化窗体设计器生成的控件
            InitializeComponent();

            // 获取Excel应用程序实例引用
            XlApp = Globals.ThisAddIn.Application;

            // 保存窗体初始高度，用于界面调整
            _formHeight = Height;
        }

        /// <summary>
        /// 从配置文件加载正则表达式规则
        /// </summary>
        /// <returns>正则表达式规则列表</returns>
        /// <exception cref="ETException">当配置文件加载失败时抛出</exception>
        private List<RegexRule> LoadRegexRulesFromConfig()
        {
            try
            {
                List<RegexRule> rules = new List<RegexRule>();

                // 初始化配置读取器
                string configPath = ETConfig.GetConfigDirectory("正则表达式预置.config");
                _regexConfigReader = new ETSectionConfigReader(configPath);

                // 获取所有配置节名称
                List<string> sectionNames = _regexConfigReader.GetSectionNames();

                foreach (string sectionName in sectionNames)
                {
                    try
                    {
                        // 读取每个配置节的键值对
                        Dictionary<string, string> sectionData = _regexConfigReader.ReadSectionKeyValues(sectionName);

                        // 提取规则信息
                        string name = sectionData.GetValueOrDefault("name", sectionName);
                        string pattern = sectionData.GetValueOrDefault("pattern", "");
                        string groupStr = sectionData.GetValueOrDefault("group", "0");
                        string description = sectionData.GetValueOrDefault("description", "");

                        // 验证必要字段
                        if (string.IsNullOrWhiteSpace(pattern))
                        {
                            ETLogManager.Warning(this, $"配置节 [{sectionName}] 缺少pattern字段，跳过");
                            continue;
                        }

                        // 解析组号
                        if (!int.TryParse(groupStr, out int group))
                        {
                            group = 0;
                            ETLogManager.Warning(this, $"配置节 [{sectionName}] 的group字段无效，使用默认值0");
                        }

                        // 创建规则对象
                        RegexRule rule = new RegexRule(name, pattern, group, description);
                        rules.Add(rule);

                        ETLogManager.Debug(this, $"成功加载正则表达式规则：{name} - {pattern}");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(this, $"加载配置节 [{sectionName}] 失败", ex);
                    }
                }

                ETLogManager.Info(this, $"成功加载 {rules.Count} 个正则表达式规则");
                return rules;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "加载正则表达式配置文件失败", ex);
                throw new ETException("加载正则表达式配置文件失败", "配置操作", ex);
            }
        }

        /// <summary>
        /// 加载正则表达式规则到ListView控件
        /// </summary>
        /// <param name="rules">正则表达式规则列表</param>
        private void LoadRegexRulesToListView(List<RegexRule> rules)
        {
            try
            {
                // 清空现有项目
                listView内置正则表达式.Items.Clear();

                // 添加规则到ListView
                foreach (RegexRule rule in rules)
                {
                    ListViewItem item = new ListViewItem(new string[]
                    {
                        rule.Name,           // 名称列
                        rule.Pattern,        // 正则表达式列
                        rule.Group.ToString() // 组号列
                    });

                    // 将规则对象存储在Tag中，方便后续使用
                    item.Tag = rule;

                    listView内置正则表达式.Items.Add(item);
                }

                ETLogManager.Info(this, $"成功加载 {rules.Count} 个正则表达式规则到ListView");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "加载正则表达式规则到ListView失败", ex);
                throw new ETException("加载正则表达式规则到ListView失败", "界面操作", ex);
            }
        }

        /// <summary>
        /// 窗体加载事件处理，初始化Excel应用程序引用和加载配置文件
        /// </summary>
        /// <param name="sender">触发事件的窗体对象</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 重新获取Excel应用程序实例（确保引用有效）
        /// 2. 从配置文件加载字符规整预置规则
        /// 3. 从配置文件加载正则表达式规则
        /// 4. 为自定义替换规则文本框加载右键菜单
        /// 5. 提供预设的字符处理规则供用户快速选择
        /// </remarks>
        /// <exception cref="ETException">当配置文件加载失败或窗体初始化失败时抛出</exception>
        private void frm提取字符_Load(object sender, EventArgs e)
        {
            try
            {
                // 重新获取Excel应用程序实例，确保引用有效
                XlApp = ThisAddIn.ExcelApplication;

                // 使用ETForm类的新方法直接绑定config文件加载右键菜单
                ETForm.LoadContextMenuStripFromConfig(contextMenuStrip替换字符, "字符规整预置.config", txt去除字符);

                // 从配置文件加载正则表达式规则
                List<RegexRule> regexRules = LoadRegexRulesFromConfig();

                // 将规则加载到ListView控件
                LoadRegexRulesToListView(regexRules);
            }
            catch (Exception ex)
            {
                // 包装异常为业务异常，提供更友好的错误信息
                throw new ETException("窗体加载失败", "窗体初始化操作", ex);
            }
        }

        /// <summary>
        /// 标签页选择改变事件处理，响应用户在不同功能标签页间的切换
        /// </summary>
        /// <param name="sender">触发事件的标签控件</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 响应用户在"规整字符串"、"标记"、"正则表达式提取字符"标签页间的切换
        /// 2. 可以在此处添加标签页切换时的特殊处理逻辑
        /// 3. 目前为预留方法，可根据需要扩展功能
        /// </remarks>
        private void TabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 标签页切换时的处理逻辑（如果需要） 可以在此处添加：
            // - 切换到不同标签页时的界面调整
            // - 特定标签页的初始化操作
            // - 用户操作统计和日志记录
        }

        /// <summary>
        /// 内置正则表达式列表选择改变事件处理，响应用户对预设正则表达式的选择
        /// </summary>
        /// <param name="sender">触发事件的列表视图控件</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 响应用户在内置正则表达式列表中的选择变化
        /// 2. 可以在此处添加选择变化时的预览或提示功能
        /// 3. 目前为预留方法，可根据需要扩展功能 注意：实际的应用操作在双击事件中处理
        /// </remarks>
        private void ListViewRegex_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 列表选择改变时的处理逻辑（如果需要） 可以在此处添加：
            // - 选中项的预览功能
            // - 正则表达式的说明提示
            // - 匹配示例的实时显示
        }

        #region 正则表达式配置文件右键菜单事件

        /// <summary>
        /// 打开正则表达式配置文件
        /// </summary>
        /// <param name="sender">触发事件的菜单项</param>
        /// <param name="e">事件参数</param>
        /// <remarks>使用系统默认程序（通常是记事本）打开正则表达式预置.config配置文件 如果配置文件不存在，会提示用户</remarks>
        private void toolStripMenuItem打开配置文件_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取配置文件路径
                string configPath = ETConfig.GetConfigDirectory("正则表达式预置.config");

                if (File.Exists(configPath))
                {
                    // 使用系统默认程序打开配置文件
                    Process.Start(configPath);
                    ETLogManager.Info(this, $"打开正则表达式配置文件：{configPath}");
                }
                else
                {
                    ETLogManager.Warning(this, $"正则表达式配置文件不存在：{configPath}");
                    MessageBox.Show("正则表达式配置文件不存在，请检查配置文件是否正确创建。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "打开正则表达式配置文件时发生错误", ex);
                MessageBox.Show($"打开配置文件失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新正则表达式配置
        /// </summary>
        /// <param name="sender">触发事件的菜单项</param>
        /// <param name="e">事件参数</param>
        /// <remarks>重新从配置文件加载正则表达式规则，并更新ListView显示 用于在修改配置文件后立即生效，无需重启窗体</remarks>
        private void toolStripMenuItem刷新配置_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info(this, "开始刷新正则表达式配置");

                // 重新从配置文件加载正则表达式规则
                List<RegexRule> regexRules = LoadRegexRulesFromConfig();

                // 将规则重新加载到ListView控件
                LoadRegexRulesToListView(regexRules);

                ETLogManager.Info(this, $"正则表达式配置刷新完成，共加载 {regexRules.Count} 个规则");

                // 显示成功提示
                //MessageBox.Show($"配置刷新成功！共加载 {regexRules.Count} 个正则表达式规则。", "刷新完成",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "刷新正则表达式配置时发生错误", ex);
                MessageBox.Show($"刷新配置失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 正则表达式配置文件右键菜单事件
    }
}