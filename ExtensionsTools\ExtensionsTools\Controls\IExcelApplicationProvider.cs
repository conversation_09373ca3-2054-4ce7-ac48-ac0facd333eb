using Microsoft.Office.Interop.Excel;

namespace ET.Controls
{
    /// <summary>
    /// Excel应用程序提供者接口 用于在不同的Excel集成环境中提供Excel应用程序实例
    /// </summary>
    public interface IExcelApplicationProvider
    {
        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        Application GetExcelApplication();
    }

    /// <summary>
    /// 默认的Excel应用程序提供者 通过COM互操作获取活动的Excel实例
    /// </summary>
    public class DefaultExcelApplicationProvider : IExcelApplicationProvider
    {
        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                // 通过COM互操作获取活动的Excel实例
                return (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// VSTO环境的Excel应用程序提供者 需要在VSTO项目中实现具体的获取逻辑
    /// </summary>
    public class VSTOExcelApplicationProvider : IExcelApplicationProvider
    {
        private readonly System.Func<object> _applicationGetter;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="applicationGetter">获取Excel应用程序的委托（返回object以避免跨程序集边界的互操作类型问题）</param>
        public VSTOExcelApplicationProvider(System.Func<object> applicationGetter)
        {
            _applicationGetter = applicationGetter;
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                var app = _applicationGetter?.Invoke();
                return app as Application;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 专门针对WPS环境的Excel应用程序提供者
    /// 使用ETExcelExtensions.App作为主要获取方式
    /// </summary>
    public class WpsDirectApplicationProvider : IExcelApplicationProvider
    {
        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: 开始获取Excel应用程序实例");

                // 策略1：直接使用ETExcelExtensions.App（这是项目中最可靠的方式）
                try
                {
                    var etApp = ET.ETExcelExtensions.App;
                    if (etApp != null)
                    {
                        System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: ✅ 通过ETExcelExtensions.App获取成功");
                        return etApp;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: ❌ ETExcelExtensions.App为null");
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"WpsDirectApplicationProvider: ❌ ETExcelExtensions.App获取失败: {ex.Message}");
                }

                // 策略2：尝试通过HyExcelVsto.ThisAddIn.ExcelApplication
                try
                {
                    var thisAddInType = System.Type.GetType("HyExcelVsto.ThisAddIn, HyExcelVsto");
                    if (thisAddInType != null)
                    {
                        var excelAppProperty = thisAddInType.GetProperty("ExcelApplication",
                            System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                        if (excelAppProperty != null)
                        {
                            var app = excelAppProperty.GetValue(null) as Application;
                            if (app != null)
                            {
                                System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: ✅ 通过HyExcelVsto.ThisAddIn.ExcelApplication获取成功");
                                return app;
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"WpsDirectApplicationProvider: ❌ HyExcelVsto方式失败: {ex.Message}");
                }

                // 策略3：标准COM互操作
                try
                {
                    var app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                    if (app != null)
                    {
                        System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: ✅ 标准COM方式获取成功");
                        return app;
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"WpsDirectApplicationProvider: ❌ 标准COM方式失败: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine("WpsDirectApplicationProvider: ❌ 所有获取策略都失败");
                return null;
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"WpsDirectApplicationProvider: ❌ 获取应用程序时发生未预期错误: {ex.Message}");
                return null;
            }
        }
    }

    /// <summary>
    /// 简化版WPS兼容Excel应用程序提供者
    /// 专门为HyHelper项目优化，支持Excel和WPS环境的自动适配
    /// </summary>
    public class HyHelperWpsCompatibleProvider : IExcelApplicationProvider
    {
        private readonly System.Func<object> _vstoApplicationGetter;
        private Application _cachedApplication;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="vstoApplicationGetter">VSTO环境下的应用程序获取委托</param>
        public HyHelperWpsCompatibleProvider(System.Func<object> vstoApplicationGetter = null)
        {
            _vstoApplicationGetter = vstoApplicationGetter;
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                // 如果有缓存且有效，直接返回
                if (_cachedApplication != null && IsApplicationValid(_cachedApplication))
                {
                    System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 使用缓存的应用程序实例");
                    return _cachedApplication;
                }

                Application app = null;
                System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 开始获取Excel应用程序实例");

                // 策略1：优先使用VSTO方式（在WPS环境下最可靠）
                if (_vstoApplicationGetter != null)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 尝试VSTO方式获取应用程序");
                        app = _vstoApplicationGetter.Invoke() as Application;
                        if (app != null && IsApplicationValid(app))
                        {
                            _cachedApplication = app;
                            System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ✅ VSTO方式成功获取应用程序实例");
                            return app;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ❌ VSTO方式获取的应用程序无效");
                        }
                    }
                    catch (System.Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"HyHelperWpsCompatibleProvider: ❌ VSTO方式失败: {ex.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 跳过VSTO方式（未提供获取委托）");
                }

                // 策略2：尝试标准COM互操作
                try
                {
                    System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 尝试标准COM互操作方式");
                    app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                    if (app != null && IsApplicationValid(app))
                    {
                        _cachedApplication = app;
                        System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ✅ 标准COM方式成功获取应用程序实例");
                        return app;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ❌ 标准COM方式获取的应用程序无效");
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HyHelperWpsCompatibleProvider: ❌ 标准COM方式失败: {ex.Message}");
                }

                // 策略3：尝试WPS特定的COM对象名称
                try
                {
                    System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: 尝试WPS特定COM方式");
                    app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("ET.Application");
                    if (app != null && IsApplicationValid(app))
                    {
                        _cachedApplication = app;
                        System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ✅ WPS特定COM方式成功获取应用程序实例");
                        return app;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ❌ WPS特定COM方式获取的应用程序无效");
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HyHelperWpsCompatibleProvider: ❌ WPS特定COM方式失败: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine("HyHelperWpsCompatibleProvider: ❌ 所有获取策略都失败，返回null");
                return null;
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HyHelperWpsCompatibleProvider: ❌ 获取应用程序时发生未预期错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证应用程序实例是否有效
        /// </summary>
        private bool IsApplicationValid(Application app)
        {
            try
            {
                if (app == null) return false;
                var _ = app.Name; // 尝试访问基本属性
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public void ClearCache()
        {
            _cachedApplication = null;
        }
    }

    /// <summary>
    /// WPS兼容的智能Excel应用程序提供者
    /// 自动检测环境并选择最适合的获取策略，支持Excel和WPS环境
    /// </summary>
    public class WpsCompatibleExcelApplicationProvider : IExcelApplicationProvider
    {
        private readonly System.Func<object> _vstoApplicationGetter;
        private Application _cachedApplication;
        private bool _isWpsEnvironment;
        private bool _environmentDetected;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="vstoApplicationGetter">VSTO环境下的应用程序获取委托，可选参数</param>
        public WpsCompatibleExcelApplicationProvider(System.Func<object> vstoApplicationGetter = null)
        {
            _vstoApplicationGetter = vstoApplicationGetter;
            _environmentDetected = false;
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                // 如果有缓存的应用程序实例，先尝试验证是否仍然有效
                if (_cachedApplication != null && IsApplicationValid(_cachedApplication))
                {
                    return _cachedApplication;
                }

                // 检测环境类型（只在第一次调用时检测）
                if (!_environmentDetected)
                {
                    DetectEnvironment();
                }

                // 根据环境类型选择获取策略
                Application app = null;

                if (_isWpsEnvironment)
                {
                    // WPS环境：优先使用VSTO方式，回退到COM互操作
                    app = GetApplicationForWps();
                }
                else
                {
                    // Excel环境：优先使用COM互操作，回退到VSTO方式
                    app = GetApplicationForExcel();
                }

                // 缓存有效的应用程序实例
                if (app != null && IsApplicationValid(app))
                {
                    _cachedApplication = app;
                }

                return app;
            }
            catch (System.Exception ex)
            {
                // 记录错误但不抛出异常，返回null让调用方处理
                System.Diagnostics.Debug.WriteLine($"WpsCompatibleExcelApplicationProvider获取应用程序失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检测当前环境是Excel还是WPS
        /// </summary>
        private void DetectEnvironment()
        {
            try
            {
                _environmentDetected = true;

                // 策略1：尝试通过VSTO获取应用程序并检查路径
                if (_vstoApplicationGetter != null)
                {
                    var app = _vstoApplicationGetter.Invoke() as Application;
                    if (app != null)
                    {
                        string startupPath = app.StartupPath?.ToLower() ?? "";
                        _isWpsEnvironment = startupPath.Contains("wps");
                        return;
                    }
                }

                // 策略2：尝试通过COM互操作获取并检查
                try
                {
                    var comApp = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                    if (comApp != null)
                    {
                        string startupPath = comApp.StartupPath?.ToLower() ?? "";
                        _isWpsEnvironment = startupPath.Contains("wps");
                        return;
                    }
                }
                catch
                {
                    // COM方式失败，可能是WPS环境
                }

                // 策略3：检查进程名称
                var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                string processName = currentProcess.ProcessName.ToLower();
                _isWpsEnvironment = processName.Contains("wps") || processName.Contains("et");
            }
            catch
            {
                // 检测失败，默认假设为Excel环境
                _isWpsEnvironment = false;
            }
        }

        /// <summary>
        /// 为WPS环境获取应用程序实例
        /// </summary>
        private Application GetApplicationForWps()
        {
            // 策略1：优先使用VSTO方式（WPS环境下最可靠）
            if (_vstoApplicationGetter != null)
            {
                try
                {
                    var app = _vstoApplicationGetter.Invoke() as Application;
                    if (app != null && IsApplicationValid(app))
                    {
                        return app;
                    }
                }
                catch
                {
                    // VSTO方式失败，继续尝试其他方式
                }
            }

            // 策略2：尝试COM互操作（某些WPS版本可能支持）
            try
            {
                var app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                if (app != null && IsApplicationValid(app))
                {
                    return app;
                }
            }
            catch
            {
                // COM方式失败
            }

            // 策略3：尝试WPS特定的COM对象名称
            try
            {
                var app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("ET.Application");
                if (app != null && IsApplicationValid(app))
                {
                    return app;
                }
            }
            catch
            {
                // WPS特定方式也失败
            }

            return null;
        }

        /// <summary>
        /// 为Excel环境获取应用程序实例
        /// </summary>
        private Application GetApplicationForExcel()
        {
            // 策略1：优先使用COM互操作（Excel环境下最常用）
            try
            {
                var app = (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
                if (app != null && IsApplicationValid(app))
                {
                    return app;
                }
            }
            catch
            {
                // COM方式失败，继续尝试其他方式
            }

            // 策略2：回退到VSTO方式
            if (_vstoApplicationGetter != null)
            {
                try
                {
                    var app = _vstoApplicationGetter.Invoke() as Application;
                    if (app != null && IsApplicationValid(app))
                    {
                        return app;
                    }
                }
                catch
                {
                    // VSTO方式失败
                }
            }

            return null;
        }

        /// <summary>
        /// 验证应用程序实例是否有效
        /// </summary>
        private bool IsApplicationValid(Application app)
        {
            try
            {
                if (app == null) return false;

                // 尝试访问一个基本属性来验证对象是否有效
                var _ = app.Name;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 清除缓存的应用程序实例
        /// </summary>
        public void ClearCache()
        {
            _cachedApplication = null;
        }

        /// <summary>
        /// 获取当前检测到的环境类型
        /// </summary>
        public bool IsWpsEnvironment => _isWpsEnvironment;
    }
}