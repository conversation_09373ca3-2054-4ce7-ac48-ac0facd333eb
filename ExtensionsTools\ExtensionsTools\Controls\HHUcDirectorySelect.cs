﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// 目录选择用户控件（带历史记录功能）
    /// </summary>
    /// <remarks>
    /// 此用户控件提供了一个友好的目录选择界面，包括：
    /// 1. ComboBox显示当前选择的路径并支持历史记录下拉选择
    /// 2. 选择按钮打开文件夹浏览对话框
    /// 3. 路径变更事件通知
    /// 4. 自适应界面布局
    /// 5. 智能历史记录管理
    ///
    /// 主要功能：
    /// - 目录路径的显示和编辑
    /// - 文件夹浏览对话框集成
    /// - 路径选择事件处理
    /// - 界面自适应调整
    /// - 默认路径管理
    /// - 历史记录自动保存和加载
    /// - 最近使用路径快速选择
    ///
    /// 历史记录特性：
    /// - 自动保存最近30个使用的目录路径
    /// - 按使用时间排序，最新的在最前面
    /// - 支持自动填充最近使用的路径
    /// - 历史记录文件按窗体和控件名称独立存储
    /// - 重复路径自动去重并移至最前
    ///
    /// 使用场景：
    /// - 文件保存路径选择
    /// - 工作目录设置
    /// - 配置文件路径选择
    /// - 数据导入导出路径设置
    /// - 需要频繁切换目录的场景
    ///
    /// 界面特性：
    /// - 分割容器布局
    /// - 自适应尺寸调整
    /// - 简洁的用户界面
    /// - 直观的操作体验
    /// - 下拉历史记录选择
    /// </remarks>
    public partial class HHUcDirectorySelect : UserControl
    {
        /// <summary>
        /// 路径选择事件处理委托
        /// </summary>
        /// <param name="filePath">选择的文件路径</param>
        public delegate void PathSelectedHandler(string filePath);

        /// <summary>
        /// 最大历史记录数量
        /// </summary>
        private const int MAX_HISTORY_COUNT = 30;

        /// <summary>
        /// 是否自动加载最近的值
        /// </summary>
        public bool AutoFillLatestValue { get; set; } = true;

        /// <summary>
        /// 默认文件目录
        /// </summary>
        private readonly string _defaultFileDirectory = @"D:\";

        /// <summary>
        /// 获取或设置控件显示的文本（路径）
        /// </summary>
        public override string Text
        {
            get => comboBox路径.Text;
            set => comboBox路径.Text = value;
        }

        /// <summary>
        /// 路径选择完成事件
        /// </summary>
        public event PathSelectedHandler OnPathSelected;

        /// <summary>
        /// 文本内容变更事件
        /// </summary>
        public new event EventHandler TextChanged;

        /// <summary>
        /// 选择按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void Button选择_Click(object sender, EventArgs e)
        {
            PresentFileDialog();
        }

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查当前ComboBox中的路径是否有效
        /// 2. 设置对话框的初始目录
        /// 3. 显示文件夹浏览对话框
        /// 4. 处理用户选择结果
        /// 5. 保存路径到历史记录
        /// 6. 触发路径选择事件
        /// </remarks>
        private void PresentFileDialog()
        {
            string initialDirectory = Directory.Exists(comboBox路径.Text.Trim()) ? comboBox路径.Text.Trim() : _defaultFileDirectory;

            FolderBrowserDialog folderBrowserDialog = new()
            {
                ShowNewFolderButton = true,
                SelectedPath = initialDirectory
            };
            if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
            {
                comboBox路径.Text = folderBrowserDialog.SelectedPath;
                SavePathToHistory(folderBrowserDialog.SelectedPath);
                OnPathSelected?.Invoke(comboBox路径.Text);
            }
        }

        /// <summary>
        /// 路径ComboBox内容变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ComboBox路径_TextChanged(object sender, EventArgs e)
        {
            TextChanged?.Invoke(comboBox路径.Text, e);
        }

        #region 初始化及界面控制

        /// <summary>
        /// 构造函数，初始化目录选择控件
        /// </summary>
        public HHUcDirectorySelect()
        {
            InitializeComponent();

            // 添加Load事件处理器
            Load += HHUcDirectorySelect_Load;

            Uc初始化();
        }

        /// <summary>
        /// 控件加载完成事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void HHUcDirectorySelect_Load(object sender, EventArgs e)
        {
            // 确保在控件完全加载后加载历史记录
            LoadPathHistory();
        }

        /// <summary>
        /// 控件尺寸变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void UcExcelRangeSelect_SizeChanged(object sender, EventArgs e)
        {
            Uc初始化();
        }

        /// <summary>
        /// 用户控件初始化设置
        /// </summary>
        /// <remarks>
        /// 此方法执行以下初始化操作：
        /// 1. 设置控件高度为21像素
        /// 2. 移除边框样式
        /// 3. 设置最小宽度为50像素
        /// 4. 配置分割容器填充整个控件
        /// </remarks>
        private void Uc初始化()
        {
            Height = 21;
            BorderStyle = System.Windows.Forms.BorderStyle.None; // 使用完整命名空间
            Width = Math.Max(50, Width);

            splitContainer1.Dock = DockStyle.Fill;
        }

        /// <summary>
        /// 分割容器尺寸变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>自动调整分割容器的分割距离，确保右侧按钮保持正方形</remarks>
        private void SplitContainer1_SizeChanged(object sender, EventArgs e)
        {
            splitContainer1.SplitterDistance = splitContainer1.Width - splitContainer1.Panel2.Height + 1;
        }

        #endregion 初始化及界面控制

        #region 历史记录管理

        /// <summary>
        /// 获取历史记录文件路径
        /// </summary>
        /// <returns>历史记录文件的完整路径</returns>
        private string GetHistoryFilePath()
        {
            string baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ET", "Cache", "HHUcDirectorySelect");

            // 确保目录存在
            if (!Directory.Exists(baseDir))
            {
                Directory.CreateDirectory(baseDir);
            }

            // 获取当前控件所在窗体的类名
            string formClassName = FindForm()?.GetType().Name ?? "Unknown";

            // 使用窗体类名和控件名生成文件名
            string fileName = $"{formClassName}_{Name}.data";

            return Path.Combine(baseDir, fileName);
        }

        /// <summary>
        /// 加载历史路径记录到ComboBox
        /// </summary>
        private void LoadPathHistory()
        {
            // 历史文件路径
            string historyFilePath = GetHistoryFilePath();

            // 确保文件存在
            if (File.Exists(historyFilePath))
            {
                try
                {
                    // 读取所有行
                    string[] paths = File.ReadAllLines(historyFilePath);

                    // 清空下拉列表
                    comboBox路径.Items.Clear();

                    // 将每个非空路径添加到下拉列表
                    foreach (string path in paths)
                    {
                        if (!string.IsNullOrWhiteSpace(path))
                        {
                            comboBox路径.Items.Add(path);
                        }
                    }

                    // 只有在AutoFillLatestValue为true且有历史记录时才填充最近的值
                    if (AutoFillLatestValue && comboBox路径.Items.Count > 0)
                    {
                        comboBox路径.Text = comboBox路径.Items[0].ToString();
                    }
                }
                catch (Exception ex)
                {
                    // 读取失败时静默处理，不影响控件正常使用
                    System.Diagnostics.Debug.WriteLine($"加载历史记录失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 保存当前路径到历史记录
        /// </summary>
        /// <param name="path">要保存的目录路径</param>
        private void SavePathToHistory(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return;

            // 获取当前所有项并转为List以便操作
            List<string> historyPaths = [];
            foreach (object item in comboBox路径.Items)
            {
                historyPaths.Add(item.ToString());
            }

            // 如果路径已存在，先移除
            historyPaths.Remove(path);

            // 将新路径添加到最前面
            historyPaths.Insert(0, path);

            // 限制历史记录数量
            if (historyPaths.Count > MAX_HISTORY_COUNT)
            {
                historyPaths = historyPaths.Take(MAX_HISTORY_COUNT).ToList();
            }

            // 更新ComboBox
            comboBox路径.Items.Clear();
            foreach (string historyPath in historyPaths)
            {
                comboBox路径.Items.Add(historyPath);
            }

            // 保存到文件
            SaveHistoryToFile(historyPaths);
        }

        /// <summary>
        /// 将历史记录保存到文件
        /// </summary>
        /// <param name="historyPaths">历史路径列表</param>
        private void SaveHistoryToFile(List<string> historyPaths)
        {
            try
            {
                string historyFilePath = GetHistoryFilePath();
                File.WriteAllLines(historyFilePath, historyPaths);
            }
            catch (Exception ex)
            {
                // 保存失败时静默处理，不影响控件正常使用
                System.Diagnostics.Debug.WriteLine($"保存历史记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存当前ComboBox中的路径到历史记录文件
        /// </summary>
        public void SavePathHistoryToFile()
        {
            // 如果当前有文本，先保存
            if (!string.IsNullOrWhiteSpace(comboBox路径.Text))
            {
                SavePathToHistory(comboBox路径.Text);
            }
            else
            {
                // 直接保存现有的项
                List<string> historyPaths = [];
                foreach (object item in comboBox路径.Items)
                {
                    historyPaths.Add(item.ToString());
                }
                SaveHistoryToFile(historyPaths);
            }
        }

        #endregion 历史记录管理
    }
}