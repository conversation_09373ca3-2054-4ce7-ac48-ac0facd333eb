﻿using System;
using System.Collections;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;

//MD5

//在引用中添加.NET选项卡中的System.Management类库

namespace Common.Utility
{
    //获取硬件信息的类
    class HWInfo
    {
        public string getComputerInfo()
        {
            string hwinfo = string.Empty;
            //HWInfo hwInfo = new HWInfo();
            hwinfo = $"{GetMainBoard()}{GetHDSN()}{GetBiosSN()}";
            ArrayList allNics = GetAllNic();
            foreach (object Nicid in allNics)
            {
                hwinfo = $"{hwinfo}{GetNicAddress(Nicid.ToString()).Trim()}";
            }
            return hwinfo;
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool DeviceIoControl(
            IntPtr HDevice,
            uint dwIoControlCode,
            IntPtr lpInBuffer,
            uint nInBufferSize,
            IntPtr lpOutBuffer,
            uint nOutBufferSize,
            ref uint lpBytesReturned,
            IntPtr lpOverlapped);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr CreateFile(
            string lpFileName,
            uint dwDesiredAccess,
            uint dwShareMode,
            IntPtr lpSecurityAttributes,
            uint dwCreationDisposition,
            uint dwFlagsAndAttributes,
            IntPtr hTemplateFile);

        public string GetNicAddress(string NicId)
        {
            IntPtr hDevice = CreateFile($"\\\\.\\{NicId}",
                                                0x80000000 | 0x40000000,
                                                0,
                                                IntPtr.Zero,
                                                3,
                                                4,
                                                IntPtr.Zero
                                                );

            if (hDevice.ToInt32() == -1)
            {
                return null;
            }

            uint Len = 0;
            IntPtr Buffer = Marshal.AllocHGlobal(256);

            Marshal.WriteInt32(Buffer, 0x01010101);

            if (!DeviceIoControl(hDevice,
                              0x170002,
                              Buffer,
                              4,
                              Buffer,
                              256,
                              ref Len,
                              IntPtr.Zero))
            {
                Marshal.FreeHGlobal(Buffer);
                CloseHandle(hDevice);
                return null;
            }

            byte[] macBytes = new byte[6];
            Marshal.Copy(Buffer, macBytes, 0, 6);

            Marshal.FreeHGlobal(Buffer);
            CloseHandle(hDevice);
            return new PhysicalAddress(macBytes).ToString();
        }

        //取CPU编号
        public String GetCpuID()
        {
            try
            {
                ManagementClass mc = new("Win32_Processor");
                ManagementObjectCollection moc = mc.GetInstances();

                String strCpuID = null;
                foreach (ManagementObject mo in moc)
                {
                    strCpuID = mo.Properties["ProcessorId"].Value.ToString();
                    break;
                }
                return strCpuID;
            }
            catch
            {
                return string.Empty;
            }
        }//end

        //获取主板编号
        public String GetMainBoard()
        {
            ManagementClass mc = new("Win32_BaseBoard");
            ManagementObjectCollection moc = mc.GetInstances();
            string strID = null;
            foreach (ManagementObject mo in moc)
            {
                strID = mo.Properties["SerialNumber"].Value.ToString();
                break;
            }
            return strID;
        }//End

        //获取主板编号
        public String GetBiosSN()
        {
            ManagementClass mc = new("Win32_BIOS");
            ManagementObjectCollection moc = mc.GetInstances();
            string strID = null;
            foreach (ManagementObject mo in moc)
            {
                strID = mo.Properties["SerialNumber"].Value.ToString();
                break;
            }
            return strID;
        }//End

        //获取硬盘编号
        public String GetHDSN()
        {
            ManagementClass mc = new("Win32_PhysicalMedia");
            //网上有提到，用Win32_DiskDrive，但是用Win32_DiskDrive获得的硬盘信息中并不包含SerialNumber属性。
            ManagementObjectCollection moc = mc.GetInstances();
            string strID = null;
            foreach (ManagementObject mo in moc)
            {
                strID = mo.Properties["SerialNumber"].Value.ToString().Trim();
                break;
            }
            return strID;
        }//End

        //以下函数获取本机所有的以太网卡的ID
        public ArrayList GetAllNic()
        {
            NetworkInterface[] Nics = NetworkInterface.GetAllNetworkInterfaces();
            ArrayList EtherNics = new(20);

            foreach (NetworkInterface nic in Nics)
            {
                if (nic.NetworkInterfaceType == NetworkInterfaceType.Ethernet && !nic.Name.Contains("Bluetooth"))  //Wireless80211，无线
                //if (nic.NetworkInterfaceType.ToString().Equals("Ethernet")) //会把蓝牙找出来
                {
                    EtherNics.Add(nic.Id);
                }
            }
            return EtherNics;
        }
    }
}