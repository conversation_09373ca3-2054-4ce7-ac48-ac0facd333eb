using ET;
using System;
using System.Windows.Forms;

namespace ExtensionsTools.Examples
{
    /// <summary>
    /// TextBox菜单文本截取功能测试示例
    /// </summary>
    /// <remarks>
    /// 演示历史记录菜单项文本截取功能的效果
    /// </remarks>
    public partial class TextBoxMenuTruncateTest : Form
    {
        private TextBox textBoxTest;
        private Button btnAddLongText;
        private Button btnAddMixedText;
        private Button btnAddShortText;
        private Label lblInstructions;

        public TextBoxMenuTruncateTest()
        {
            InitializeComponent();
            InitializeTextBoxHistory();
            AddTestData();
        }

        /// <summary>
        /// 初始化窗体控件
        /// </summary>
        private void InitializeComponent()
        {
            this.textBoxTest = new TextBox();
            this.btnAddLongText = new Button();
            this.btnAddMixedText = new Button();
            this.btnAddShortText = new Button();
            this.lblInstructions = new Label();
            this.SuspendLayout();

            // 
            // textBoxTest
            // 
            this.textBoxTest.Location = new System.Drawing.Point(30, 50);
            this.textBoxTest.Name = "textBoxTest";
            this.textBoxTest.Size = new System.Drawing.Size(500, 23);
            this.textBoxTest.TabIndex = 0;

            // 
            // btnAddLongText
            // 
            this.btnAddLongText.Location = new System.Drawing.Point(30, 100);
            this.btnAddLongText.Name = "btnAddLongText";
            this.btnAddLongText.Size = new System.Drawing.Size(120, 30);
            this.btnAddLongText.TabIndex = 1;
            this.btnAddLongText.Text = "添加长中文文本";
            this.btnAddLongText.UseVisualStyleBackColor = true;
            this.btnAddLongText.Click += BtnAddLongText_Click;

            // 
            // btnAddMixedText
            // 
            this.btnAddMixedText.Location = new System.Drawing.Point(170, 100);
            this.btnAddMixedText.Name = "btnAddMixedText";
            this.btnAddMixedText.Size = new System.Drawing.Size(120, 30);
            this.btnAddMixedText.TabIndex = 2;
            this.btnAddMixedText.Text = "添加中英混合文本";
            this.btnAddMixedText.UseVisualStyleBackColor = true;
            this.btnAddMixedText.Click += BtnAddMixedText_Click;

            // 
            // btnAddShortText
            // 
            this.btnAddShortText.Location = new System.Drawing.Point(310, 100);
            this.btnAddShortText.Name = "btnAddShortText";
            this.btnAddShortText.Size = new System.Drawing.Size(120, 30);
            this.btnAddShortText.TabIndex = 3;
            this.btnAddShortText.Text = "添加短文本";
            this.btnAddShortText.UseVisualStyleBackColor = true;
            this.btnAddShortText.Click += BtnAddShortText_Click;

            // 
            // lblInstructions
            // 
            this.lblInstructions.Location = new System.Drawing.Point(30, 20);
            this.lblInstructions.Name = "lblInstructions";
            this.lblInstructions.Size = new System.Drawing.Size(500, 20);
            this.lblInstructions.TabIndex = 4;
            this.lblInstructions.Text = "点击按钮添加不同长度的文本，然后右键查看历史菜单的截取效果";

            // 
            // TextBoxMenuTruncateTest
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(580, 180);
            this.Controls.Add(this.lblInstructions);
            this.Controls.Add(this.btnAddShortText);
            this.Controls.Add(this.btnAddMixedText);
            this.Controls.Add(this.btnAddLongText);
            this.Controls.Add(this.textBoxTest);
            this.Name = "TextBoxMenuTruncateTest";
            this.Text = "TextBox菜单文本截取测试";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        /// <summary>
        /// 初始化TextBox历史记录功能
        /// </summary>
        private void InitializeTextBoxHistory()
        {
            ETForm.BindTextBox(textBoxTest, maxHistoryCount: 10, autoFillLatestValue: false);
        }

        /// <summary>
        /// 添加测试数据
        /// </summary>
        private void AddTestData()
        {
            // 可以预先添加一些测试数据
        }

        private void BtnAddLongText_Click(object sender, EventArgs e)
        {
            textBoxTest.Text = "这是一个非常长的中文文本示例，用来测试菜单项文本截取功能是否正常工作。当文本超过50个中文字符时，应该会被截取并在末尾添加省略号。这段文本应该会被截取。";
        }

        private void BtnAddMixedText_Click(object sender, EventArgs e)
        {
            textBoxTest.Text = "Mixed中英文Text混合内容Testing截取功能Whether it works properly when mixing Chinese and English characters in the same text string.";
        }

        private void BtnAddShortText_Click(object sender, EventArgs e)
        {
            textBoxTest.Text = "短文本示例";
        }
    }

    /// <summary>
    /// 测试程序入口点
    /// </summary>
    public class TruncateTestProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TextBoxMenuTruncateTest());
        }
    }
}
