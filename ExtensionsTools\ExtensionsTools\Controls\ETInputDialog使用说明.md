# ETInputDialog - 通用输入对话框

## 📋 功能概述

ETInputDialog是一个功能强大的通用输入对话框控件，支持单行/多行输入、滚动条配置、自动文本折行等特性。

## 🎯 主要特性

### ✅ 核心功能
- **单行/多行输入**: 通过`IsMultiline`属性控制
- **滚动条支持**: 支持垂直、水平或双向滚动条
- **自动文本折行**: 提示文本超过最大宽度时自动折行
- **可配置标题**: 自定义对话框标题
- **默认值支持**: 可设置输入框的默认值
- **智能布局**: 根据内容自动调整对话框大小

### 🎨 界面特性
- **响应式布局**: 自动适应内容大小
- **用户友好**: 支持Enter确认、Esc取消
- **焦点管理**: 自动选中输入框内容
- **居中显示**: 相对于父窗体居中显示

## 🔧 属性说明

### 公共属性

| 属性名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `MaxPromptWidth` | int | 提示文本最大宽度（像素） | 400 |
| `IsMultiline` | bool | 是否为多行输入模式 | false |
| `InputScrollBars` | ScrollBars | 滚动条类型 | None |
| `DialogTitle` | string | 对话框标题 | "输入对话框" |
| `PromptText` | string | 提示文本内容 | "请输入：" |
| `DefaultValue` | string | 输入框默认值 | "" |
| `InputValue` | string | 用户输入的值（只读） | - |

### ScrollBars枚举值
- `None`: 无滚动条
- `Horizontal`: 水平滚动条
- `Vertical`: 垂直滚动条
- `Both`: 双向滚动条

## 📚 使用方法

### 1. 简单输入对话框
```csharp
// 最简单的使用方式
string result = ETInputDialog.Show("请输入您的姓名：");
if (result != null)
{
    // 用户点击了确定，result包含输入的内容
    Console.WriteLine($"用户输入: {result}");
}
```

### 2. 带标题和默认值
```csharp
string result = ETInputDialog.Show(
    "请输入文件路径：", 
    "设置路径", 
    @"C:\默认路径");
```

### 3. 多行输入模式
```csharp
string result = ETInputDialog.Show(
    "请输入详细说明：", 
    "输入说明", 
    "默认内容...", 
    isMultiline: true, 
    scrollBars: ScrollBars.Vertical);
```

### 4. 完整配置示例
```csharp
string result = ETInputDialog.Show(
    promptText: "请输入长文本内容，支持多行输入和滚动：", 
    title: "多行输入对话框", 
    defaultValue: "这是默认的多行内容\n可以包含换行符", 
    isMultiline: true, 
    scrollBars: ScrollBars.Both, 
    maxPromptWidth: 500);
```

### 5. 手动创建和配置
```csharp
using (var dialog = new ETInputDialog())
{
    dialog.PromptText = "请输入配置信息：";
    dialog.DialogTitle = "配置设置";
    dialog.DefaultValue = "默认配置";
    dialog.IsMultiline = true;
    dialog.InputScrollBars = ScrollBars.Vertical;
    dialog.MaxPromptWidth = 350;
    
    if (dialog.ShowDialog() == DialogResult.OK)
    {
        string userInput = dialog.InputValue;
        // 处理用户输入
    }
}
```

## 🎯 实际应用示例

### 51Helper存储目录设置
```csharp
void 设置存储目录()
{
    // 读取当前配置
    string currentPath = config.GetValue("51Helper", "saveFilePath", "");
    
    // 显示输入对话框
    string promptText = "请输入51Helper文件存储目录路径：\n\n注意：\n- 请输入完整的文件夹路径\n- 如果目录不存在，系统将自动创建";
    
    string inputPath = ETInputDialog.Show(
        promptText, 
        "设置存储目录", 
        currentPath);
    
    if (inputPath != null)
    {
        // 处理用户输入的路径
        ProcessPath(inputPath);
    }
}
```

### 多行文本编辑
```csharp
void 编辑配置文件()
{
    string currentConfig = File.ReadAllText("config.txt");
    
    string newConfig = ETInputDialog.Show(
        "请编辑配置文件内容：", 
        "配置编辑器", 
        currentConfig, 
        isMultiline: true, 
        scrollBars: ScrollBars.Both, 
        maxPromptWidth: 600);
    
    if (newConfig != null)
    {
        File.WriteAllText("config.txt", newConfig);
    }
}
```

## 🔧 技术实现细节

### 自动布局算法
- 根据提示文本长度和最大宽度计算所需高度
- 自动调整对话框尺寸以适应内容
- 保持最小宽度确保按钮正常显示

### 响应式设计
- 使用Panel布局管理器
- 支持窗体大小调整（多行模式）
- 自动锚定控件位置

### 用户体验优化
- 自动选中输入框内容
- 支持键盘快捷键（Enter/Esc）
- 智能焦点管理

## 📝 注意事项

1. **内存管理**: 使用using语句确保对话框正确释放
2. **返回值检查**: 用户取消时返回null，需要进行null检查
3. **文本长度**: 超长文本建议使用多行模式和滚动条
4. **父窗体**: 对话框会相对于当前活动窗体居中显示
5. **线程安全**: 必须在UI线程中调用

## 🎯 最佳实践

1. **合理设置最大宽度**: 根据屏幕分辨率和内容长度设置合适的最大宽度
2. **提供清晰的提示**: 在提示文本中说明输入要求和格式
3. **设置合适的默认值**: 提供有意义的默认值提升用户体验
4. **错误处理**: 对用户输入进行验证和错误处理
5. **多行模式**: 对于长文本输入，建议使用多行模式和滚动条
