# C# 开发规划模板要求

## 🎯 核心目标

## 📖 模板结构要求

### 1. 项目概述 (Project Overview)

#### 1.1 项目基本信息
- **项目名称**：明确的项目标识
- **项目类型**：Web应用/桌面应用/API服务/类库/微服务等
- **目标.NET版本**：.NET Framework/.NET Core/.NET 5+

### 2. 需求分析 (Requirements Analysis)

#### 2.1 功能需求 (Functional Requirements)
```yaml
功能需求格式:
  功能模块名称:
    描述: 功能详细描述
    优先级: 高/中/低
    复杂度: 简单/中等/复杂
    依赖关系: 前置条件和依赖模块
    验收标准: 明确的验收条件
```

#### 2.2 非功能需求 (Non-Functional Requirements)
- **性能要求**：响应时间、并发用户数、吞吐量
- **可用性要求**：系统可用性、故障恢复时间
- **安全要求**：身份认证、数据加密、权限控制
- **兼容性要求**：浏览器兼容、操作系统支持
- **可维护性**：代码质量、文档完整性、可扩展性

### 3. 技术架构设计 (Technical Architecture)

#### 3.1 整体架构
- **架构模式**：单体/微服务/分层架构/六边形架构
- **技术栈选择**：框架、数据库、中间件、第三方服务
- **部署架构**：服务器配置、负载均衡、容器编排

#### 3.2 系统架构图
```mermaid
# 要求提供Mermaid格式的架构图
# 包括：用户界面层、业务逻辑层、数据访问层、基础设施层
```

#### 3.3 数据架构
- **数据模型设计**：实体关系图、数据字典
- **数据库选择**：SQL Server/MySQL/PostgreSQL/MongoDB
- **数据访问策略**：Entity Framework/Dapper/ADO.NET
- **缓存策略**：Redis/MemoryCache/分布式缓存

### 4. 功能模块划分 (Module Division)

#### 4.1 模块分解原则
- **单一职责**：每个模块职责明确
- **高内聚低耦合**：模块内部紧密相关，模块间松散耦合
- **可重用性**：通用功能抽象为可重用组件
- **可测试性**：模块设计便于单元测试

#### 4.2 核心模块定义
```yaml
模块结构示例:
  用户管理模块:
    功能范围: 用户注册、登录、权限管理
    技术实现: ASP.NET Core Identity
    数据模型: User, Role, Permission
    接口设计: IUserService, IAuthService
    依赖关系: 依赖于数据访问层
    
  业务核心模块:
    功能范围: 核心业务逻辑处理
    技术实现: 领域驱动设计(DDD)
    数据模型: 业务实体和聚合根
    接口设计: 业务服务接口
    依赖关系: 独立的业务逻辑层
```

### 5. 开发思路与策略 (Development Strategy)

#### 5.1 开发方法论
- **开发模式**：敏捷开发/瀑布模型/DevOps
- **版本控制**：Git工作流、分支策略
- **代码规范**：编码标准、命名约定、注释规范
- **质量保证**：代码审查、静态分析、自动化测试

#### 5.2 技术选型
- **框架选择**：为什么选择特定的.NET框架版本
- **ORM选择**：Entity Framework vs Dapper的选择理由
- **前端技术**：Blazor/React/Vue.js的选择依据
- **第三方库**：NuGet包选择和版本管理策略

#### 5.3 开发阶段规划
```yaml
开发阶段:
  第一阶段 - 基础搭建:
    任务: 项目初始化、基础架构搭建
    交付物: 项目骨架、基础配置
    
  第二阶段 - 核心功能:
    任务: 核心业务逻辑实现
    交付物: 主要功能模块
    
  第三阶段 - 集成测试:
    任务: 系统集成、测试验证
    交付物: 完整系统、测试报告
```

### 6. 重点代码设计 (Key Code Design)
（节省开发规划长度，重点代码仅列出对全局影响很大的代码）
#### 6.1 核心类设计

```csharp
// 提供关键类的设计示例
public interface IBusinessService
{
    Task<Result<T>> ProcessAsync<T>(BusinessRequest request);
}

public class BusinessService : IBusinessService
{
    // 核心业务逻辑实现框架
}
```


#### 6.2 关键算法设计
- **业务算法**：核心业务逻辑的算法实现
- **性能优化**：缓存算法、并发处理、异步编程
- **数据处理**：数据转换、验证、序列化

### 7. 代码执行流程图 (Code Execution Flow)

#### 7.1 业务流程图
```mermaid
# 使用Mermaid绘制主要业务流程
# 包括：用户操作 -> 控制器 -> 服务层 -> 数据层 -> 返回结果
```

#### 7.2 数据流图
```mermaid
# 数据在系统中的流转过程
# 包括：数据输入 -> 验证 -> 处理 -> 存储 -> 输出
```

#### 7.3 异常处理流程
```mermaid
# 异常处理和错误恢复机制
# 包括：异常捕获 -> 日志记录 -> 错误处理 -> 用户反馈
```

### 8. 数据库设计 (Database Design)
（如没涉及数据库不列本章节)
#### 8.1 数据模型
- **实体关系图(ERD)**：表结构和关系设计
- **数据字典**：字段定义、约束条件、索引设计
- **数据迁移策略**：版本控制、升级方案

#### 8.2 性能优化
- **索引策略**：主键、外键、复合索引设计
- **查询优化**：SQL优化、存储过程、视图设计
- **分库分表**：水平分割、垂直分割策略



### 9.2 监控与日志
- **应用监控**：Application Insights、健康检查
- **日志管理**：Serilog、ELK Stack、结构化日志

### 10. 风险管理 (Risk Management)

#### 10.1 技术风险
- **技术债务**：代码质量、架构腐化风险
- **性能风险**：并发处理、内存泄漏、数据库性能
- **安全风险**：SQL注入、XSS攻击、数据泄露
- **依赖风险**：第三方库、外部服务依赖

#### 10.2 风险缓解策略
- **代码质量**：静态分析、代码审查、重构计划
- **性能保证**：性能测试、监控告警、优化方案
- **安全防护**：安全编码、渗透测试、安全审计
- **依赖管理**：版本锁定、备选方案、服务降级

### 11. 质量保证 (Quality Assurance)

#### 11.1 代码质量标准
- **编码规范**：StyleCop、EditorConfig配置
- **代码审查**：Pull Request流程、审查清单
- **静态分析**：SonarQube、CodeQL分析
- **技术债务**：定期重构、代码异味检测

#### 11.2 质量度量指标
- **代码覆盖率**：≥80%单元测试覆盖率
- **圈复杂度**：≤10的方法复杂度控制
- **重复代码率**：≤5%的代码重复率
- **缺陷密度**：≤0.1个缺陷/KLOC

## 🔧 模板使用指南

### AI编写规划时的要求

1. **完整性检查**：确保所有章节都有具体内容，不能有空白或TODO
2. **技术深度**：提供具体的技术方案，不能只是概念性描述
3. **可执行性**：每个步骤都要有明确的实施方法和验收标准
4. **C#特色**：充分体现.NET生态的特点和最佳实践
5. **图表要求**：必须提供Mermaid格式的流程图和架构图
6. **代码示例**：关键设计点要有C#代码示例
7. **时间估算**：提供合理的时间估算和里程碑规划

### 输出格式要求

- 使用Markdown格式
- 代码块使用正确的语言标识
- 图表使用Mermaid语法
- 表格格式规范
- 层次结构清晰
- 中文表达准确专业

## 📊 成功标准

一份优秀的C#开发规划应该：
- ✅ 需求分析清晰完整
- ✅ 技术架构合理可行
- ✅ 模块划分科学合理
- ✅ 开发思路系统性强
- ✅ 重点代码设计精准
- ✅ 流程图清晰易懂
- ✅ 风险识别全面
- ✅ 质量保证措施完善
- ✅ 可执行性强
- ✅ 符合C#/.NET最佳实践

---

**注意**：本模板要求AI在编写开发规划时，必须结合具体项目需求，提供详细、专业、可执行的技术方案，而不是泛泛而谈的概念性内容。
