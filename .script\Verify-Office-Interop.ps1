# Verify Office Interop Assemblies Status Script

Write-Host "=== Office Interop Assemblies Status Check ===" -ForegroundColor Cyan

# 1. Check Visual Studio PIA folders
Write-Host "`n1. Checking Visual Studio PIA folders:" -ForegroundColor Green
$piaPath = "C:\Program Files (x86)\Microsoft Visual Studio\Shared\Visual Studio Tools for Office\PIA"

if (Test-Path $piaPath) {
    $officeVersions = Get-ChildItem $piaPath -Directory | Where-Object { $_.Name -like "Office*" }
    
    foreach ($version in $officeVersions) {
        Write-Host "Found version: $($version.Name)" -ForegroundColor Yellow
        
        $excelDll = Get-ChildItem $version.FullName -Filter "Microsoft.Office.Interop.Excel.dll" -ErrorAction SilentlyContinue
        if ($excelDll) {
            $fileVersion = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($excelDll.FullName)
            $assemblyVersion = ([System.Reflection.AssemblyName]::GetAssemblyName($excelDll.FullName)).Version
            
            Write-Host "  Excel Interop Assembly:" -ForegroundColor White
            Write-Host "    File Version: $($fileVersion.FileVersion)" -ForegroundColor Gray
            Write-Host "    Assembly Version: $assemblyVersion" -ForegroundColor Gray
            Write-Host "    Path: $($excelDll.FullName)" -ForegroundColor Gray
        }
    }
}
else {
    Write-Host "PIA path does not exist" -ForegroundColor Red
}

# 2. Check GAC assemblies
Write-Host "`n2. Checking Office interop assemblies in GAC:" -ForegroundColor Green
try {
    $gacAssembly = [System.Reflection.Assembly]::LoadWithPartialName("Microsoft.Office.Interop.Excel")
    if ($gacAssembly) {
        Write-Host "Assembly loaded from GAC:" -ForegroundColor Yellow
        Write-Host "  Full Name: $($gacAssembly.FullName)" -ForegroundColor Gray
        Write-Host "  Location: $($gacAssembly.Location)" -ForegroundColor Gray
    }
}
catch {
    Write-Host "Cannot load Office interop assembly from GAC: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Check current Office version
Write-Host "`n3. Checking current Office version:" -ForegroundColor Green
$officeRegPath = "HKLM:\SOFTWARE\Microsoft\Office\*\Excel\InstallRoot"
$officeInstalls = Get-ItemProperty -Path $officeRegPath -ErrorAction SilentlyContinue

if ($officeInstalls) {
    foreach ($install in $officeInstalls) {
        Write-Host "Office Install Path: $($install.Path)" -ForegroundColor Yellow
        Write-Host "Version: $($install.PSChildName)" -ForegroundColor Gray
    }
}

# 4. Check project references
Write-Host "`n4. Checking project file references:" -ForegroundColor Green
$projectFile = "HyExcelVsto\HyExcelVsto.csproj"

if (Test-Path $projectFile) {
    $projectContent = Get-Content $projectFile -Raw
    
    # Find Office interop assembly references
    $officeReferences = $projectContent | Select-String 'Microsoft\.Office\.Interop\.[^,]+, Version=([^,]+)' -AllMatches
    
    if ($officeReferences.Matches) {
        Write-Host "Office interop assembly references in project:" -ForegroundColor Yellow
        foreach ($match in $officeReferences.Matches) {
            Write-Host "  $($match.Value)" -ForegroundColor Gray
        }
    }
}
else {
    Write-Host "Project file does not exist: $projectFile" -ForegroundColor Red
}

# 5. Check for any remaining Office14 assemblies
Write-Host "`n5. Checking for remaining Office14 assemblies:" -ForegroundColor Green
$office14Remaining = Get-ChildItem "C:\Program Files (x86)\Microsoft Visual Studio" -Recurse -Filter "*Office*14*" -ErrorAction SilentlyContinue

if ($office14Remaining) {
    Write-Host "Found remaining Office14 files:" -ForegroundColor Red
    $office14Remaining | Select-Object Name, FullName | Format-Table -AutoSize
}
else {
    Write-Host "No Office14 assemblies found - cleanup successful!" -ForegroundColor Green
}

# 6. Recommendations
Write-Host "`n=== Recommendations ===" -ForegroundColor Cyan
Write-Host "1. Ensure only Office15 version interop assemblies remain" -ForegroundColor White
Write-Host "2. Project should reference Version=******** assemblies" -ForegroundColor White
Write-Host "3. Set EmbedInteropTypes=True to avoid version conflicts" -ForegroundColor White
Write-Host "4. Restart Visual Studio and Excel after removing old versions" -ForegroundColor White

Read-Host "`nPress any key to exit"
