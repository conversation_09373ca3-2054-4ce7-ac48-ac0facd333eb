﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace ET.Controls
{
    public partial class ETUcFileSelect : UserControl
    {
        // 定义委托
        public delegate void PathSelectedHandler(string filePath);

        // 最大历史记录数量
        private const int MAX_HISTORY_COUNT = 30;

        // 是否自动加载最近的值
        public bool AutoFillLatestValue { get; set; } = true;

        private readonly string _defaultFileDirectory = @"D:\";

        public override string Text
        {
            get => comboBox路径.Text;
            set => comboBox路径.Text = value;
        }

        public bool UseOpenFileDialog { get; set; } = true;
        public bool UseFolderBrowser { get; set; } = false;

        public string FileFilter { get; set; } = "所有文件 (*.*)|*.*";
        public string DefaultFileExtension { get; set; } = string.Empty;

        // 定义事件
        public event PathSelectedHandler OnPathSelected;

        public new event EventHandler TextChanged;

        private void button选择_Click(object sender, EventArgs e)
        {
            PresentFileDialog();
        }

        private void PresentFileDialog()
        {
            string initialDirectory = GetInitialDirectory(comboBox路径.Text.Trim());

            if (UseFolderBrowser)
            {
                using (FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog())
                {
                    folderBrowserDialog.Description = "选择文件夹";
                    folderBrowserDialog.SelectedPath = initialDirectory;
                    folderBrowserDialog.ShowNewFolderButton = true;

                    if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
                    {
                        comboBox路径.Text = folderBrowserDialog.SelectedPath;
                        SavePathToHistory(folderBrowserDialog.SelectedPath);
                        NotifyPathSelected();
                    }
                }
            }
            else if (UseOpenFileDialog)
            {
                using (OpenFileDialog openFileDialog = new()
                {
                    // 设置对话框标题和过滤器
                    Title = "打开文件",
                    Filter = FileFilter,

                    // 设置初始目录和恢复最后访问的目录
                    InitialDirectory = initialDirectory,
                    RestoreDirectory = true
                })
                {
                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        comboBox路径.Text = openFileDialog.FileName;
                        SavePathToHistory(openFileDialog.FileName);
                        NotifyPathSelected();
                    }
                }
            }
            else
            {
                using (SaveFileDialog saveFileDialog = new()
                {
                    // 设置对话框标题和过滤器
                    Title = "保存文件",
                    Filter = FileFilter,

                    // 设置初始目录和默认扩展名
                    InitialDirectory = initialDirectory,
                    DefaultExt = string.IsNullOrWhiteSpace(DefaultFileExtension) ? null : DefaultFileExtension
                })
                {
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        comboBox路径.Text = saveFileDialog.FileName;
                        SavePathToHistory(saveFileDialog.FileName);
                        NotifyPathSelected();
                    }
                }
            }
        }

        private string GetInitialDirectory(string path)
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(path);
                return Directory.Exists(directoryPath) ? directoryPath : _defaultFileDirectory;
            }
            catch
            {
                // 忽略异常，使用默认目录
            }

            return _defaultFileDirectory;
        }

        // 通知已选择路径
        private void NotifyPathSelected()
        {
            OnPathSelected?.Invoke(comboBox路径.Text);
        }

        private void splitContainer1_SizeChanged(object sender, EventArgs e)
        {
            splitContainer1.SplitterDistance = splitContainer1.Width - splitContainer1.Panel2.Height + 1;
        }

        #region 初始化

        public ETUcFileSelect()
        {
            InitializeComponent();

            // 添加Load事件处理器
            Load += ETUcFileSelect_Load;

            Uc初始化();
        }

        private void ETUcFileSelect_Load(object sender, EventArgs e)
        {
            // 确保在控件完全加载后加载历史记录
            LoadPathHistory();
        }

        private void ucExcelRangeSelect_SizeChanged(object sender, EventArgs e)
        {
            Uc初始化();
        }

        private void Uc初始化()
        {
            Height = 21;
            BorderStyle = System.Windows.Forms.BorderStyle.None; // 使用完整命名空间
            Width = Math.Max(50, Width);

            splitContainer1.Dock = DockStyle.Fill;
        }

        #endregion 初始化

        private void comboBox路径_TextChanged(object sender, EventArgs e)
        {
            TextChanged?.Invoke(comboBox路径.Text, e);
        }

        #region 历史记录管理

        /// <summary>
        /// 获取历史记录文件路径
        /// </summary>
        /// <returns>历史记录文件的完整路径</returns>
        private string GetHistoryFilePath()
        {
            string baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ET", "Cache", "ETUcFileSelect");

            // 确保目录存在
            if (!Directory.Exists(baseDir))
            {
                Directory.CreateDirectory(baseDir);
            }

            // 获取当前控件所在窗体的类名
            string formClassName = FindForm()?.GetType().Name ?? "Unknown";

            // 使用窗体类名和控件名生成文件名
            string fileName = $"{formClassName}_{Name}.data";

            return Path.Combine(baseDir, fileName);
        }

        /// <summary>
        /// 加载历史路径记录到ComboBox
        /// </summary>
        private void LoadPathHistory()
        {
            // 历史文件路径
            string historyFilePath = GetHistoryFilePath();

            // 确保文件存在
            if (File.Exists(historyFilePath))
            {
                try
                {
                    // 读取所有行
                    string[] paths = File.ReadAllLines(historyFilePath);

                    // 清空下拉列表
                    comboBox路径.Items.Clear();

                    // 将每个非空路径添加到下拉列表
                    foreach (string path in paths)
                    {
                        if (!string.IsNullOrWhiteSpace(path))
                        {
                            comboBox路径.Items.Add(path);
                        }
                    }

                    // 只有在AutoFillLatestValue为true且有历史记录时才填充最近的值
                    if (AutoFillLatestValue && comboBox路径.Items.Count > 0)
                    {
                        comboBox路径.Text = comboBox路径.Items[0].ToString();
                    }
                }
                catch (Exception ex)
                {
                    // 读取失败时静默处理，不影响控件正常使用
                    System.Diagnostics.Debug.WriteLine($"加载历史记录失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 保存当前路径到历史记录
        /// </summary>
        /// <param name="path">要保存的文件路径</param>
        private void SavePathToHistory(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return;

            // 获取当前所有项并转为List以便操作
            List<string> historyPaths = [];
            foreach (object item in comboBox路径.Items)
            {
                historyPaths.Add(item.ToString());
            }

            // 如果路径已存在，先移除
            historyPaths.Remove(path);

            // 将新路径添加到最前面
            historyPaths.Insert(0, path);

            // 限制历史记录数量
            if (historyPaths.Count > MAX_HISTORY_COUNT)
            {
                historyPaths = historyPaths.Take(MAX_HISTORY_COUNT).ToList();
            }

            // 更新ComboBox
            comboBox路径.Items.Clear();
            foreach (string historyPath in historyPaths)
            {
                comboBox路径.Items.Add(historyPath);
            }

            // 保存到文件
            SaveHistoryToFile(historyPaths);
        }

        /// <summary>
        /// 将历史记录保存到文件
        /// </summary>
        /// <param name="historyPaths">历史路径列表</param>
        private void SaveHistoryToFile(List<string> historyPaths)
        {
            try
            {
                string historyFilePath = GetHistoryFilePath();
                File.WriteAllLines(historyFilePath, historyPaths);
            }
            catch (Exception ex)
            {
                // 保存失败时静默处理，不影响控件正常使用
                System.Diagnostics.Debug.WriteLine($"保存历史记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存当前ComboBox中的路径到历史记录文件
        /// </summary>
        public void SavePathHistoryToFile()
        {
            // 如果当前有文本，先保存
            if (!string.IsNullOrWhiteSpace(comboBox路径.Text))
            {
                SavePathToHistory(comboBox路径.Text);
            }
            else
            {
                // 直接保存现有的项
                List<string> historyPaths = [];
                foreach (object item in comboBox路径.Items)
                {
                    historyPaths.Add(item.ToString());
                }
                SaveHistoryToFile(historyPaths);
            }
        }

        #endregion 历史记录管理
    }
}