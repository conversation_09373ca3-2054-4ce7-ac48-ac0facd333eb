using System;
using System.ClientModel;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using ET.AI.Models;
using ET.AI.Services.Core;
using ET.AI.Services.Core.Models;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenAI;
using OpenAI.Chat;

namespace ET.AI.Services
{
    /// <summary>
    /// AI服务类，提供AI配置管理、JSON构造和Excel数据处理功能
    /// </summary>
    /// <remarks>
    /// 该类负责：
    /// 1. AI配置的加载和管理
    /// 2. API密钥的管理和请求限制
    /// 3. 批量问题的并发处理
    /// 4. Excel数据的处理和转换
    /// </remarks>
    public class AIService
    {
        #region 常量定义

        /// <summary>
        /// AI服务器响应日志目录
        /// </summary>
        const string AI_RESPONSE_LOG_DIR = "logs/AIServerResponse";

        /// <summary>
        /// 默认的最大并行查询数
        /// </summary>
        /// <remarks>
        /// 用于控制每个API密钥的最大并发请求数，默认为10
        /// </remarks>
        internal const int DEFAULT_MAX_PARALLEL_QUERIES = 10;

        /// <summary>
        /// 默认的每分钟最大请求数
        /// </summary>
        /// <remarks>
        /// 用于限制每个API密钥每分钟的最大请求次数，默认为15
        /// </remarks>
        internal const int DEFAULT_MAX_REQUESTS_PER_MINUTE = 15;

        /// <summary>
        /// 默认的请求超时时间（秒）
        /// </summary>
        /// <remarks>
        /// 单个AI请求的最大等待时间，超过此时间将抛出超时异常
        /// </remarks>
        internal const int DEFAULT_REQUEST_TIMEOUT = 120;

        /// <summary>
        /// 配置文件中是否记录请求和响应的键名
        /// </summary>
        const string CONFIG_KEY_LOG_REQUEST_RESPONSE = "bool记录请求和响应";

        #endregion

        #region 事件定义

        /// <summary>
        /// AI查询进度事件委托
        /// </summary>
        /// <param name="message">进度消息，包含当前处理状态和进度信息</param>
        public delegate void QueryProgressEventHandler(string message);

        /// <summary>
        /// AI查询进度事件，用于通知查询处理的各个阶段
        /// </summary>
        /// <remarks>
        /// 订阅此事件可实时获取查询处理的进度信息，包括：
        /// - 查询开始和完成
        /// - 分组信息
        /// - 错误信息
        /// </remarks>
        public event QueryProgressEventHandler OnQueryProgress;

        #endregion

        #region 私有字段

        readonly AIQueryProcessor _queryProcessor;
        readonly AIExcelHandler _excelHandler;
        int _totalRequestCount = 0;
        string _currentRequestTimestamp;
        bool _enableRequestResponseLogging;

        #endregion

        #region 内部属性

        /// <summary>
        /// 获取当前使用的AI配置
        /// </summary>
        internal AIConfig CurrentConfig { get; private set; }

        /// <summary>
        /// 获取API密钥管理器实例
        /// </summary>
        internal APIKeyManager KeyManager { get; private set; }

        /// <summary>
        /// 触发进度事件
        /// </summary>
        /// <param name="message">进度消息</param>
        internal void RaiseProgressEvent(string message)
        {
            OnQueryProgress?.Invoke(message);
        }

        /// <summary>
        /// 保存原始的请求和响应数据
        /// </summary>
        /// <param name="groupIndex">分组索引</param>
        /// <param name="requestJson">请求JSON字符串</param>
        /// <param name="rawResponse">原始响应字符串</param>
        internal void SaveRawResponse(int groupIndex, string requestJson, string rawResponse)
        {
            // 如果未启用日志记录，直接返回
            if (!_enableRequestResponseLogging)
                return;

            try
            {
                // 确保日志目录存在
                string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AI_RESPONSE_LOG_DIR);
                Directory.CreateDirectory(logDir);

                // 保存请求JSON
                string requestLogFileName = Path.Combine(logDir, $"{_currentRequestTimestamp}-{groupIndex}-request.json");
                File.WriteAllText(requestLogFileName, requestJson);

                // 保存原始响应
                string responseLogFileName = Path.Combine(logDir, $"{_currentRequestTimestamp}-{groupIndex}-response.raw");
                File.WriteAllText(responseLogFileName, rawResponse);
            }
            catch (Exception ex)
            {
                // 记录日志错误但不影响主流程
                System.Diagnostics.Debug.WriteLine($"保存原始请求/响应数据时出错: {ex.Message}");
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化AI服务实例
        /// </summary>
        /// <remarks>
        /// 构造函数会：
        /// 1. 启用TLS 1.2安全协议
        /// 2. 初始化查询处理器
        /// 3. 初始化Excel处理器
        /// 4. 读取配置文件
        /// </remarks>
        public AIService()
        {
            // 启用 TLS 1.2
            System.Net.ServicePointManager.SecurityProtocol =
                System.Net.SecurityProtocolType.Tls12 |
                System.Net.SecurityProtocolType.Tls11 |
                System.Net.SecurityProtocolType.Tls;

            _queryProcessor = new AIQueryProcessor(this);
            _excelHandler = new AIExcelHandler();

            // 读取配置文件中的日志记录设置
            try
            {
                //string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "config.ini");
                string configPath = ETConfig.GetConfigPath("config.ini");
                if (File.Exists(configPath))
                {
                    ETIniFile iniFile = new ETIniFile(configPath);
                    _enableRequestResponseLogging = iniFile.GetBool("AI", CONFIG_KEY_LOG_REQUEST_RESPONSE, true);
                }
                else
                {
                    _enableRequestResponseLogging = true; // 默认启用日志记录
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取AI配置时出错: {ex.Message}");
                _enableRequestResponseLogging = true; // 出错时默认启用日志记录
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 将AI响应结果填充到Excel中
        /// </summary>
        /// <param name="questionRange">问题所在的单元格区域</param>
        /// <param name="knownInfoRange">已知信息所在的单元格区域</param>
        /// <param name="response">AI的响应结果（JSON格式）</param>
        /// <param name="isQuestionRow">是否按行填充问题</param>
        /// <param name="fillOption">填充选项，控制是否跳过空值</param>
        /// <returns>填充是否成功</returns>
        public bool FillResponseToExcel(
            Range questionRange,
            Range knownInfoRange,
            JObject response,
            bool isQuestionRow,
            ExcelFillOption fillOption = ExcelFillOption.FillAll)
        {
            return _excelHandler.FillResponseToExcel(questionRange, knownInfoRange, response, isQuestionRow, fillOption);
        }

        /// <summary>
        /// 获取AI回复的JSON结果（批量问题组）
        /// </summary>
        /// <param name="questionGroups">问题组列表</param>
        /// <param name="modelFileName">AI模型配置文件名</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="systemContentFile">系统内容文件名（可选）</param>
        /// <param name="onResponseReceived">响应接收回调（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>包含AI回复的JSON对象</returns>
        /// <exception cref="Exception">当配置无效或达到请求限制时抛出异常</exception>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<JObject> GetAIResponseAsync(
            List<AIQuestionGroup> questionGroups,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null,
            Action<JObject, bool> onResponseReceived = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // 获取AI配置
                CurrentConfig = AIConfigReader.ReadConfig(modelFileName);
                if (CurrentConfig == null || CurrentConfig.APIKeys.Count == 0)
                {
                    throw new Exception("无法读取AI配置或API密钥未设置！");
                }

                // 调用AppendSystemContent方法，将systemContentFile的内容追加到配置中的系统内容
                CurrentConfig.AppendSystemContent(systemContentFile);

                // 检查总请求数限制
                if (CurrentConfig.TotalMaxRequests.HasValue && _totalRequestCount >= CurrentConfig.TotalMaxRequests.Value)
                {
                    throw new Exception($"已达到总请求数限制（{CurrentConfig.TotalMaxRequests.Value}次），请稍后再试！");
                }

                // 计算每个API密钥的最大并发请求数
                int maxConcurrentRequestsPerKey = CurrentConfig.MaxConcurrentRequests ?? DEFAULT_MAX_PARALLEL_QUERIES;

                // 创建API密钥管理器
                KeyManager = new APIKeyManager(
                    CurrentConfig.APIKeys,
                    maxConcurrentRequestsPerKey,
                    CurrentConfig.MaxRequestsPerMinute ?? DEFAULT_MAX_REQUESTS_PER_MINUTE
                );

                // 记录查询开始时间
                _currentRequestTimestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");

                // 分组
                List<QueryGroup> groups = SplitIntoGroups(questionGroups.Select((g, index) => new QueryItem
                {
                    ListIndex = g.ListIndex,
                    KnownInfo1 = g.KnownInfo1,
                    KnownInfo2Path = g.KnownInfo2
                }).ToList());

                RaiseProgressEvent($"\n查询任务已分成{groups.Count}组");

                // 创建结果列表
                List<JObject> results = new List<JObject>();
                object resultsLock = new object();
                int nextGroupIndex = 0;

                // 创建任务列表
                List<Task> tasks = new List<Task>();

                // 定义处理单个组的函数
                async Task ProcessGroup()
                {
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        QueryGroup group;
                        lock (groups)
                        {
                            if (nextGroupIndex >= groups.Count)
                                return;
                            group = groups[nextGroupIndex++];
                        }

                        try
                        {
                            var (requestJson, result) = await _queryProcessor.ProcessQueryGroup(group, questionGroups, rulesFileName, onResponseReceived, cancellationToken).ConfigureAwait(false);

                            // 记录AI服务器响应到日志文件
                            SaveRawResponse(group.GroupIndex, requestJson.ToString(Formatting.Indented), result.ToString(Formatting.Indented));

                            lock (resultsLock)
                            {
                                results.Add(result);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            throw;
                        }
                        catch (Exception ex)
                        {
                            RaiseProgressEvent($"处理第{group.GroupIndex}组时出错: {ex.Message}");
                            throw;
                        }
                    }
                }

                // 启动最大可能的并发任务
                int totalMaxConcurrent = CurrentConfig.APIKeys.Count * maxConcurrentRequestsPerKey;
                for (int i = 0; i < totalMaxConcurrent; i++)
                {
                    tasks.Add(Task.Run(ProcessGroup, cancellationToken));
                }

                try
                {
                    // 等待所有任务完成
                    if (Thread.CurrentThread.GetApartmentState() == ApartmentState.STA)
                    {
                        WaitAllWithMessagePump(tasks);
                    }
                    else
                    {
                        await Task.WhenAll(tasks).ConfigureAwait(false);
                    }
                }
                catch (OperationCanceledException)
                {
                    RaiseProgressEvent("\n查询已被取消");
                    throw;
                }

                // 合并结果
                JObject finalResult = new JObject();
                JArray finalList = new JArray();
                foreach (JObject result in results.OrderBy(r => r["list"]?.First?["listIndex"]?.Value<int>() ?? 0))
                {
                    if (result["list"] is JArray resultList)
                    {
                        foreach (JToken item in resultList)
                        {
                            finalList.Add(item);
                        }
                    }
                }
                finalResult["list"] = finalList;

                // 触发最终回调
                if (onResponseReceived != null)
                {
                    onResponseReceived(finalResult, true);
                }

                return finalResult;
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取AI回复的JSON结果（基于Excel Range）
        /// </summary>
        /// <param name="questionRange">问题单元格区域</param>
        /// <param name="knownInfoRange1">已知信息1单元格区域</param>
        /// <param name="knownInfoRange2">已知信息2单元格区域</param>
        /// <param name="modelFileName">AI模型配置文件名</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="systemContentFile">系统内容文件名（可选）</param>
        /// <param name="fillOption">Excel填充选项</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>
        /// 返回一个元组，包含：
        /// - Response: AI回复的JSON对象
        /// - IsQuestionRow: 是否为问题行
        /// - RequestJson: 请求的JSON对象
        /// </returns>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<(JObject Response, bool IsQuestionRow, JObject RequestJson)> GetAIResponseAsync(
            Range questionRange,
            Range knownInfoRange1,
            Range knownInfoRange2,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null,
            ExcelFillOption fillOption = ExcelFillOption.FillAll,
            CancellationToken cancellationToken = default)
        {
            bool isQuestionRow;
            List<AIQuestionGroup> questionGroups = _excelHandler.GetQuestionGroupsFromRange(
                questionRange, knownInfoRange1, knownInfoRange2, out isQuestionRow);

            JObject response = await GetAIResponseAsync(
                questionGroups,
                modelFileName,
                rulesFileName,
                systemContentFile,
                (result, success) =>
                {
                    if (success && fillOption != ExcelFillOption.NoFill)
                    {
                        _excelHandler.FillResponseToExcel(questionRange, knownInfoRange1, result, isQuestionRow, fillOption);
                    }
                },
                cancellationToken).ConfigureAwait(false);

            return (response, isQuestionRow, null);
        }

        /// <summary>
        /// 获取单个问题的AI回复
        /// </summary>
        /// <param name="question">问题文本</param>
        /// <param name="modelFileName">AI模型配置文件名</param>
        /// <param name="rulesFileName">规则文件名</param>
        /// <param name="systemContentFile">系统内容文件名（可选）</param>
        /// <returns>AI回复的文本内容</returns>
        /// <remarks>
        /// 此方法用于处理单个问题场景，不进行并发处理，
        /// 适用于简单的问答交互。
        /// </remarks>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<string> GetSingleQuestionResponseAsync(
            string question,
            string modelFileName,
            string rulesFileName,
            string systemContentFile = null)
        {
            try
            {
                // 获取AI配置
                CurrentConfig = AIConfigReader.ReadConfig(modelFileName);
                if (CurrentConfig == null || CurrentConfig.APIKeys.Count == 0)
                {
                    throw new Exception("无法读取AI配置或API密钥未设置！");
                }

                // 调用AppendSystemContent方法，将systemContentFile的内容追加到配置中的系统内容
                CurrentConfig.AppendSystemContent(systemContentFile);

                // 创建API密钥管理器
                KeyManager = new APIKeyManager(
                    CurrentConfig.APIKeys,
                    1,  // 单个问题只需要一个并发
                    CurrentConfig.MaxRequestsPerMinute ?? DEFAULT_MAX_REQUESTS_PER_MINUTE
                );

                // 获取API密钥
                var (apiKey, releaseKey) = await KeyManager.GetNextAvailableKey().ConfigureAwait(false);
                try
                {
                    // 创建OpenAI客户端
                    OpenAIClientOptions options = new OpenAIClientOptions();
                    if (!string.IsNullOrEmpty(CurrentConfig.BaseURL))
                    {
                        options.Endpoint = new Uri(CurrentConfig.BaseURL);
                    }

                    // 设置或清除代理服务器
                    if (!string.IsNullOrWhiteSpace(CurrentConfig.ProxyHost) && CurrentConfig.ProxyPort.HasValue)
                    {
                        System.Net.WebRequest.DefaultWebProxy = new System.Net.WebProxy(CurrentConfig.ProxyHost, CurrentConfig.ProxyPort.Value);
                    }
                    else
                    {
                        System.Net.WebRequest.DefaultWebProxy = null;
                    }

                    OpenAIClient openAiClient = new OpenAIClient(new ApiKeyCredential(apiKey), options);
                    ChatClient chatClient = openAiClient.GetChatClient(CurrentConfig.Model);

                    // 创建消息列表
                    List<ChatMessage> messages = new List<ChatMessage>();
                    if (!string.IsNullOrEmpty(CurrentConfig.SystemContent))
                    {
                        messages.Add(new SystemChatMessage(CurrentConfig.SystemContent));
                    }

                    // 直接使用问题作为用户消息
                    messages.Add(new UserChatMessage(question));

                    // 设置选项
                    ChatCompletionOptions chatOptions = new ChatCompletionOptions
                    {
                        Temperature = CurrentConfig.Temperature ?? 0.2f,
                        TopP = CurrentConfig.TopP ?? 0.2f,
                        ResponseFormat = CurrentConfig.ResponseFormat
                    };

                    // 获取回复
                    ClientResult<ChatCompletion> completion = await chatClient.CompleteChatAsync(messages, chatOptions).ConfigureAwait(false);
                    return ExtractChatCompletionResponse(completion.Value.Content[0].Text);
                }
                finally
                {
                    releaseKey();
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 从响应中提取 ChatCompletion 内容
        /// </summary>
        /// <param name="response">原始响应文本</param>
        /// <returns>处理后的响应内容</returns>
        /// <remarks>
        /// 如果响应是 ChatCompletion 结构，则提取其值；否则返回原始响应
        /// </remarks>
        string ExtractChatCompletionResponse(string response)
        {
            try
            {
                // 尝试解析为 JSON
                JToken token = JToken.Parse(response);

                // 检查是否是 ChatCompletion 结构
                if (token is JObject obj && obj.ContainsKey("ChatCompletion"))
                {
                    return obj["ChatCompletion"].ToString();
                }

                // 如果不是特定结构，返回原始响应
                return response;
            }
            catch (JsonReaderException)
            {
                // 如果解析失败，返回原始响应
                return response;
            }
        }

        /// <summary>
        /// 在STA线程中等待所有任务完成，同时处理Windows消息
        /// </summary>
        /// <param name="tasks">要等待的任务集合</param>
        /// <remarks>
        /// 此方法用于在Windows窗体应用程序中等待异步任务完成，
        /// 同时保持UI响应性。它会持续处理Windows消息，直到所有任务完成。
        /// </remarks>
        void WaitAllWithMessagePump(IEnumerable<Task> tasks)
        {
            NativeWindow frame = new NativeWindow();
            List<Task> remainingTasks = tasks.ToList();

            while (remainingTasks.Count > 0)
            {
                // 处理Windows消息，保持UI响应
                System.Windows.Forms.Application.DoEvents();

                // 检查并移除已完成的任务
                remainingTasks.RemoveAll(t => t.IsCompleted);

                if (remainingTasks.Count > 0)
                {
                    Thread.Sleep(100);  // 避免CPU过度使用
                }
            }
        }

        /// <summary>
        /// 将查询项目列表分割成多个查询组
        /// </summary>
        /// <param name="items">要分组的查询项目列表</param>
        /// <returns>查询组列表</returns>
        /// <remarks>
        /// 此方法根据配置的并发限制将查询项目分成多个组，
        /// 每个组包含适量的查询项目，以便进行并发处理。
        /// </remarks>
        List<QueryGroup> SplitIntoGroups(List<QueryItem> items)
        {
            if (items == null || items.Count == 0)
            {
                return new List<QueryGroup>();
            }

            int totalCount = items.Count;
            int baseGroupSize = CurrentConfig.BaseGroupSize;
            int minGroupSizeThreshold = baseGroupSize / 2;

            // 如果总数小于等于基础组大小，直接返回一个组
            if (totalCount <= baseGroupSize)
            {
                return new List<QueryGroup>
                {
                    new QueryGroup
                    {
                        GroupIndex = 1,
                        Items = items
                    }
                };
            }

            List<QueryGroup> groups = new List<QueryGroup>();
            int currentIndex = 0;
            int groupIndex = 1;

            // 计算完整组数量
            int fullGroups = totalCount / baseGroupSize;
            int remainder = totalCount % baseGroupSize;

            // 如果余数小于最小组大小阈值，将其分配到最后一个完整组
            if (remainder > 0 && remainder < minGroupSizeThreshold)
            {
                fullGroups--;
            }

            // 创建完整的组
            for (int i = 0; i < fullGroups; i++)
            {
                groups.Add(new QueryGroup
                {
                    GroupIndex = groupIndex++,
                    Items = items.GetRange(currentIndex, baseGroupSize)
                });
                currentIndex += baseGroupSize;
            }

            // 处理剩余项
            int remainingItems = totalCount - currentIndex;
            if (remainingItems > 0)
            {
                if (remainingItems <= baseGroupSize * 1.5)
                {
                    // 如果剩余项不多，作为一个组
                    groups.Add(new QueryGroup
                    {
                        GroupIndex = groupIndex,
                        Items = items.GetRange(currentIndex, remainingItems)
                    });
                }
                else
                {
                    // 将剩余项平均分成两组
                    int halfSize = remainingItems / 2;
                    groups.Add(new QueryGroup
                    {
                        GroupIndex = groupIndex++,
                        Items = items.GetRange(currentIndex, halfSize)
                    });
                    groups.Add(new QueryGroup
                    {
                        GroupIndex = groupIndex,
                        Items = items.GetRange(currentIndex + halfSize, remainingItems - halfSize)
                    });
                }
            }

            return groups;
        }

        #endregion
    }
}