﻿namespace HyExcelVsto.Module.Common
{
    partial class frm向下填充
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.button按上一行的值填充 = new System.Windows.Forms.Button();
            this.button清除标色 = new System.Windows.Forms.Button();
            this.radioButton填充当前单元格下方 = new System.Windows.Forms.RadioButton();
            this.radioButton填充筛选行下方 = new System.Windows.Forms.RadioButton();
            this.radioButton填充选定单元格 = new System.Windows.Forms.RadioButton();
            this.SuspendLayout();
            // 
            // button按上一行的值填充
            // 
            this.button按上一行的值填充.Location = new System.Drawing.Point(8, 80);
            this.button按上一行的值填充.Name = "button按上一行的值填充";
            this.button按上一行的值填充.Size = new System.Drawing.Size(123, 28);
            this.button按上一行的值填充.TabIndex = 3;
            this.button按上一行的值填充.Text = "填充空白";
            this.button按上一行的值填充.UseVisualStyleBackColor = true;
            this.button按上一行的值填充.Click += new System.EventHandler(this.button按上一行的值填充_Click);
            // 
            // button清除标色
            // 
            this.button清除标色.Location = new System.Drawing.Point(137, 80);
            this.button清除标色.Name = "button清除标色";
            this.button清除标色.Size = new System.Drawing.Size(66, 28);
            this.button清除标色.TabIndex = 4;
            this.button清除标色.Text = "清除标色";
            this.button清除标色.UseVisualStyleBackColor = true;
            this.button清除标色.Click += new System.EventHandler(this.button清除标色_Click);
            // 
            // radioButton填充当前单元格下方
            // 
            this.radioButton填充当前单元格下方.AutoSize = true;
            this.radioButton填充当前单元格下方.Location = new System.Drawing.Point(8, 15);
            this.radioButton填充当前单元格下方.Name = "radioButton填充当前单元格下方";
            this.radioButton填充当前单元格下方.Size = new System.Drawing.Size(95, 16);
            this.radioButton填充当前单元格下方.TabIndex = 0;
            this.radioButton填充当前单元格下方.Text = "填充当前下方";
            this.radioButton填充当前单元格下方.UseVisualStyleBackColor = true;
            // 
            // radioButton填充筛选行下方
            // 
            this.radioButton填充筛选行下方.AutoSize = true;
            this.radioButton填充筛选行下方.Checked = true;
            this.radioButton填充筛选行下方.Location = new System.Drawing.Point(8, 35);
            this.radioButton填充筛选行下方.Name = "radioButton填充筛选行下方";
            this.radioButton填充筛选行下方.Size = new System.Drawing.Size(107, 16);
            this.radioButton填充筛选行下方.TabIndex = 1;
            this.radioButton填充筛选行下方.TabStop = true;
            this.radioButton填充筛选行下方.Text = "填充筛选行下方";
            this.radioButton填充筛选行下方.UseVisualStyleBackColor = true;
            // 
            // radioButton填充选定单元格
            // 
            this.radioButton填充选定单元格.AutoSize = true;
            this.radioButton填充选定单元格.Location = new System.Drawing.Point(8, 55);
            this.radioButton填充选定单元格.Name = "radioButton填充选定单元格";
            this.radioButton填充选定单元格.Size = new System.Drawing.Size(107, 16);
            this.radioButton填充选定单元格.TabIndex = 2;
            this.radioButton填充选定单元格.Text = "填充选定单元格";
            this.radioButton填充选定单元格.UseVisualStyleBackColor = true;
            // 
            // frm向下填充
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(212, 112);
            this.Controls.Add(this.radioButton填充选定单元格);
            this.Controls.Add(this.radioButton填充筛选行下方);
            this.Controls.Add(this.radioButton填充当前单元格下方);
            this.Controls.Add(this.button清除标色);
            this.Controls.Add(this.button按上一行的值填充);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm向下填充";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "向下填充";
            this.Load += new System.EventHandler(this.frm向下填充_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Button button按上一行的值填充;
        private System.Windows.Forms.Button button清除标色;
        private System.Windows.Forms.RadioButton radioButton填充当前单元格下方;
        private System.Windows.Forms.RadioButton radioButton填充筛选行下方;
        private System.Windows.Forms.RadioButton radioButton填充选定单元格;
    }
}